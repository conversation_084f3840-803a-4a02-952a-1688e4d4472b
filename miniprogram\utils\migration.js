// migration.js
// 数据迁移工具函数 - 用于处理数据库中数据的迁移和清理操作

/**
 * 清理课程数据中的冗余字段
 * @returns {Promise<Object>} 迁移结果 - 返回一个Promise对象，包含迁移操作的结果
 */
// export关键字用于导出函数，使其可以在其他文件中被导入使用
// const用于声明常量变量
// async表示这是一个异步函数，可以使用await关键字等待Promise完成
export const cleanCourseData = async () => {
  // try-catch块用于错误处理，捕获可能发生的异常
  try {
    // await关键字用于等待Promise完成，使异步代码看起来像同步代码
    // wx.cloud.callFunction调用微信云开发的云函数
    const result = await wx.cloud.callFunction({
      name: 'dataMigration', // 云函数的名称
      data: {
        action: 'cleanCourseData' // 传递给云函数的参数，指定要执行的操作
      }
    });
    
    // 检查云函数返回的结果是否成功
    if (result.result.success) {
      // console.log用于在控制台输出信息，方便调试
      console.log('数据清理成功:', result.result);
      return result.result; // 返回成功的结果
    } else {
      // console.error用于在控制台输出错误信息
      console.error('数据清理失败:', result.result);
      // throw new Error创建并抛出一个新的错误对象，中断函数执行
      throw new Error(result.result.message);
    }
  } catch (error) {
    // 捕获并处理函数执行过程中可能发生的任何错误
    console.error('调用数据迁移云函数失败:', error);
    throw error; // 将错误继续向上抛出，让调用者知道发生了错误
  }
}; 