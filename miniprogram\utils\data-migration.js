/**
 * 数据迁移工具
 * 用于将现有的相册数据迁移到新的文件夹系统
 * 
 * 迁移内容：
 * 1. 为现有图片添加新字段（isFavorite, folderIds, isDeleted等）
 * 2. 创建系统文件夹（收藏夹、首页展示）
 * 3. 确保向下兼容性
 */

const db = wx.cloud.database();

/**
 * 数据迁移主函数
 * 这个函数会检查数据是否需要迁移，如果需要则执行迁移
 */
export async function migrateAlbumData() {
  console.log('开始检查相册数据迁移...');
  
  try {
    // 1. 检查是否已经迁移过
    const migrationStatus = await checkMigrationStatus();
    if (migrationStatus.completed) {
      console.log('数据已经迁移过，跳过迁移');
      return { success: true, message: '数据已是最新版本' };
    }

    // 2. 创建系统文件夹
    await createSystemFolders();
    
    // 3. 迁移现有图片数据
    await migrateExistingImages();
    
    // 4. 标记迁移完成
    await markMigrationCompleted();
    
    console.log('相册数据迁移完成');
    return { success: true, message: '数据迁移成功' };
    
  } catch (error) {
    console.error('数据迁移失败:', error);
    return { success: false, message: '数据迁移失败', error };
  }
}

/**
 * 检查迁移状态
 * 通过查看是否存在系统文件夹来判断是否已经迁移
 */
async function checkMigrationStatus() {
  try {
    // 检查是否存在系统文件夹集合
    const folderResult = await db.collection('album_folders').limit(1).get();
    
    // 检查现有图片是否有新字段
    const imageResult = await db.collection('album_images').limit(1).get();
    
    const hasSystemFolders = folderResult.data.length > 0;
    const hasNewFields = imageResult.data.length > 0 && 
                        imageResult.data[0].hasOwnProperty('isFavorite');
    
    return {
      completed: hasSystemFolders && hasNewFields,
      hasSystemFolders,
      hasNewFields
    };
  } catch (error) {
    console.log('检查迁移状态时出错，可能是首次运行:', error);
    return { completed: false, hasSystemFolders: false, hasNewFields: false };
  }
}

/**
 * 创建系统文件夹
 * 创建收藏夹和首页展示两个系统文件夹
 */
async function createSystemFolders() {
  console.log('创建系统文件夹...');
  
  const systemFolders = [
    {
      id: 'folder_favorite',
      name: '收藏夹',
      type: 'system',
      systemType: 'favorite',
      createTime: new Date(),
      updateTime: new Date(),
      imageCount: 0
    },
    {
      id: 'folder_banner',
      name: '首页展示',
      type: 'system',
      systemType: 'banner',
      createTime: new Date(),
      updateTime: new Date(),
      imageCount: 0
    },
    {
      id: 'folder_trash',
      name: '回收站',
      type: 'system',
      systemType: 'trash',
      createTime: new Date(),
      updateTime: new Date(),
      imageCount: 0
    }
  ];

  // 批量创建系统文件夹
  for (const folder of systemFolders) {
    try {
      // 不在data中包含_id，而是通过doc()指定
      const { id, ...folderData } = folder;
      await db.collection('album_folders').doc(id).set({
        data: folderData
      });
      console.log(`创建系统文件夹: ${folder.name}`);
    } catch (error) {
      if (error.errCode === -502002) {
        // 文档已存在，跳过
        console.log(`系统文件夹已存在: ${folder.name}`);
      } else {
        throw error;
      }
    }
  }
}

/**
 * 迁移现有图片数据
 * 为所有现有图片添加新字段
 */
async function migrateExistingImages() {
  console.log('迁移现有图片数据...');
  
  let hasMore = true;
  let lastId = null;
  let migratedCount = 0;
  
  while (hasMore) {
    // 分批查询现有图片（每次20张）
    let query = db.collection('album_images').orderBy('_id', 'asc').limit(20);
    
    if (lastId) {
      query = query.where({
        _id: db.command.gt(lastId)
      });
    }
    
    const result = await query.get();
    const images = result.data;
    
    if (images.length === 0) {
      hasMore = false;
      break;
    }
    
    // 批量更新图片数据
    for (const image of images) {
      await migrateImageData(image);
      migratedCount++;
      lastId = image._id;
    }
    
    console.log(`已迁移 ${migratedCount} 张图片...`);
    
    // 如果本批次少于20张，说明已经是最后一批
    if (images.length < 20) {
      hasMore = false;
    }
  }
  
  console.log(`图片数据迁移完成，共迁移 ${migratedCount} 张图片`);
}

/**
 * 迁移单张图片的数据
 * 为图片添加新字段，保持现有字段不变
 */
async function migrateImageData(image) {
  const updateData = {};
  
  // 添加收藏字段（默认未收藏）
  if (!image.hasOwnProperty('isFavorite')) {
    updateData.isFavorite = false;
  }
  
  // 添加文件夹关联字段
  if (!image.hasOwnProperty('folderIds')) {
    const folderIds = [];
    
    // 如果是首页图片，添加到首页展示文件夹
    if (image.bannerOrder && image.bannerOrder > 0) {
      folderIds.push('folder_banner');
    }
    
    updateData.folderIds = folderIds;
  }
  
  // 添加软删除字段（默认未删除）
  if (!image.hasOwnProperty('isDeleted')) {
    updateData.isDeleted = false;
  }
  
  // 添加更新时间字段
  if (!image.hasOwnProperty('updateTime')) {
    updateData.updateTime = image.createTime || new Date();
  }
  
  // 如果有需要更新的字段，则执行更新
  if (Object.keys(updateData).length > 0) {
    await db.collection('album_images').doc(image._id).update({
      data: updateData
    });
  }
}

/**
 * 标记迁移完成
 * 创建一个迁移记录文档
 */
async function markMigrationCompleted() {
  await db.collection('system_migration').doc('album_migration_v1').set({
    data: {
      version: '1.0.0',
      completedAt: new Date(),
      description: '相册文件夹系统迁移'
    }
  });
}

/**
 * 获取系统文件夹列表
 * 返回所有系统文件夹
 */
export async function getSystemFolders() {
  try {
    const result = await db.collection('album_folders')
      .where({ type: 'system' })
      .orderBy('createTime', 'asc')
      .get();
    
    return result.data;
  } catch (error) {
    console.error('获取系统文件夹失败:', error);
    return [];
  }
}

/**
 * 更新文件夹图片数量
 * 重新计算指定文件夹的图片数量
 */
export async function updateFolderImageCount(folderId) {
  try {
    let count = 0;
    
    if (folderId === 'folder_favorite') {
      // 收藏夹：统计收藏的图片
      const result = await db.collection('album_images')
        .where({
          isFavorite: true,
          isDeleted: false
        })
        .count();
      count = result.total;
    } else if (folderId === 'folder_banner') {
      // 首页展示：统计有bannerOrder的图片
      const result = await db.collection('album_images')
        .where({
          bannerOrder: db.command.neq(null),
          isDeleted: false
        })
        .count();
      count = result.total;
    } else {
      // 自定义文件夹：统计包含该文件夹ID的图片
      const result = await db.collection('album_images')
        .where({
          folderIds: db.command.in([folderId]),
          isDeleted: false
        })
        .count();
      count = result.total;
    }
    
    // 更新文件夹的图片数量
    await db.collection('album_folders').doc(folderId).update({
      data: {
        imageCount: count,
        updateTime: new Date()
      }
    });
    
    return count;
  } catch (error) {
    console.error('更新文件夹图片数量失败:', error);
    return 0;
  }
}
