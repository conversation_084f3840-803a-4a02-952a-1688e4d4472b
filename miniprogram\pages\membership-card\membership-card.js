// miniprogram/pages/membership-card/membership-card.js
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

Page({
  data: {
    cards: [],
    loading: true
  },
  onLoad() {
    showLoading(this, '加载中...');
    this.fetchMembershipCards({ showLoading: true });
  },
  
  onShow() {
    // 每次显示页面时自动刷新数据
    this.fetchMembershipCards({ showLoading: false });
  },
  onPullDownRefresh() {
    this.fetchMembershipCards({ showLoading: false }, () => wx.stopPullDownRefresh());
  },
  async fetchMembershipCards({ showLoading }, callback) {
    if (showLoading) this.setData({ loading: true });
    try {
      const app = getApp();
      const userInfo = app.getUserInfo();
      if (!userInfo || !userInfo.openid) {
        showToast(this, { message: '请先登录', theme: 'warning' });
        this.setData({ cards: [] });
        if (showLoading) this.setData({ loading: false });
        if (callback) callback();
        return;
      }
      const db = wx.cloud.database();
      const res = await db.collection('membershipCard')
        .where({ userId: userInfo.openid })
        .orderBy('issueDate', 'desc')
        .get();
      const now = new Date();
      const cards = res.data.map(card => {
        const validFrom = this.formatDate(card.validFrom);
        const validTo = this.formatDate(card.validTo);
        const issueDate = this.formatDate(card.issueDate);
        // 状态判断
        const toDate = new Date(card.validTo);
        const isExpired = now > toDate;
        const isExpiring = !isExpired && (toDate - now < 7 * 24 * 60 * 60 * 1000); // 7天内到期
        return {
          ...card,
          validFrom,
          validTo,
          issueDate,
          isExpired,
          isExpiring
        };
      });
      this.setData({ cards });
    } catch (e) {
      showToast(this, { message: '获取考勤卡失败', theme: 'error' });
      this.setData({ cards: [] });
    }
    if (showLoading) this.setData({ loading: false });
    hideToast(this);
    if (callback) callback();
  },
  formatDate(dateStr) {
    if (!dateStr) return '';
    const d = new Date(dateStr);
    if (isNaN(d.getTime())) return '';
    return `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}-${d.getDate().toString().padStart(2,'0')}`;
  }
}); 