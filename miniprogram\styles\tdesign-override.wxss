/* TDesign 按钮扁平蓝色风格覆盖 */
.t-button {
  border-radius: 8px !important;
  font-weight: 600 !important;
  box-shadow: none !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.t-button--primary {
  background: #0052d9 !important;
  border: none !important;
  color: #fff !important;
  box-shadow: none !important;
}
.t-button--primary:active {
  background: #1765ff !important;
  transform: translateY(2px) !important;
}

.t-button--danger {
  background: #d54941 !important;
  border: none !important;
  color: #fff !important;
  box-shadow: none !important;
}
.t-button--danger:active {
  background: #ad3530 !important;
  transform: translateY(2px) !important;
}

.t-button--success {
  background: #00a870 !important;
  border: none !important;
  color: #fff !important;
  box-shadow: none !important;
}
.t-button--success:active {
  background: #00965c !important;
  transform: translateY(2px) !important;
}

.t-button--warning {
  background: #ed7b2f !important;
  border: none !important;
  color: #fff !important;
  box-shadow: none !important;
}
.t-button--warning:active {
  background: #d25b18 !important;
  transform: translateY(2px) !important;
}

.t-button--default {
  background: #fff !important;
  border: 1px solid #e7e7e7 !important;
  color: #333 !important;
  box-shadow: none !important;
}
.t-button--default:active {
  background: #f8f9fa !important;
  transform: translateY(1px) !important;
}

/* 统一TDesign Toast样式为紧凑风格 */
t-toast, .t-toast {
  min-height: 0 !important;
  padding: 16rpx 32rpx !important;
  border-radius: 12rpx !important;
  font-size: 28rpx !important;
  max-width: 80vw !important;
}
.t-toast__content {
  min-height: 0 !important;
  padding: 0 !important;
}
.t-toast__text {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  line-height: 44rpx !important;
}