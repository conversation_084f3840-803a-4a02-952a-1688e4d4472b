// notificationManagement/index.js
// 通知管理云函数，统一处理通知相关操作

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 数据库集合
const NOTIFICATIONS = 'notifications';
const USERS = 'users';
const COURSES = 'courses';
const BOOKINGS = 'bookings';

// 通知类型定义
const NOTIFICATION_TYPES = {
  'booking_success_student': '预约成功（学员）',
  'booking_success_coach': '新增预约（讲师）',
  'booking_cancelled_student': '取消预约成功（学员）',
  'booking_cancelled_coach': '学员取消预约（讲师）',
  'booking_cancelled_by_admin_student': '预约已被取消（学员）',
  'booking_cancelled_by_admin_coach': '预约被管理员取消（讲师）',
  'course_offline_coach': '课程已下线（讲师）',
  'course_offline_student': '课程已取消（学员）',
  'daily_reminder_student': '明日课程提醒（学员）',
  'daily_reminder_coach': '明日课程提醒（讲师）',
  'course_reminder_student': '课程即将开始（学员）',
  'course_reminder_coach': '课程即将开始（讲师）'
};

/**
 * 云函数入口函数
 * @param {Object} event 事件对象
 * @param {string} event.action 操作类型
 * @param {Object} event.data 数据
 * @param {Object} context 上下文
 */
exports.main = async (event, context) => {
  const { action, data } = event;
  
  try {
    switch (action) {
      case 'createNotification':
        return await createNotification(data);
      case 'getNotifications':
        return await getNotifications(data);
      case 'markAsRead':
        return await markAsRead(data);
      case 'markAllAsRead':
        return await markAllAsRead(data);
      case 'deleteNotification':
        return await deleteNotification(data);
      case 'getUnreadCount':
        return await getUnreadCount(data);
      case 'sendBookingNotification':
        return await sendBookingNotification(data);
      case 'sendCourseOfflineNotification':
        return await sendCourseOfflineNotification(data);
      case 'cleanExpiredNotifications':
        return await cleanExpiredNotifications(data);
      case 'createTestNotifications':
        return await createTestNotifications(data);
      case 'clearAllNotifications':
        return await clearAllNotifications(data);
      case 'checkNewCourses':
        return await checkNewCourses(data);
      case 'updateScheduleViewTime':
        return await updateScheduleViewTime(data);
      default:
        return {
          success: false,
          message: '未知操作类型'
        };
    }
  } catch (error) {
    console.error('通知管理操作失败:', error);
    return {
      success: false,
      message: '操作失败',
      error: error.message
    };
  }
};

/**
 * 创建通知
 * @param {Object} data 通知数据
 * @param {string} data.recipientId 接收者openid
 * @param {string} data.type 通知类型
 * @param {string} data.title 通知标题
 * @param {string} data.content 通知内容
 * @param {string} data.courseId 相关课程ID（可选）
 * @param {string} data.bookingId 相关预约ID（可选）
 */
async function createNotification(data) {
  try {
    const { recipientId, type, title, content, courseId, bookingId } = data;
    
    // 验证必填字段
    if (!recipientId || !type || !title || !content) {
      return {
        success: false,
        message: '缺少必填字段'
      };
    }
    
    // 验证通知类型
    if (!NOTIFICATION_TYPES[type]) {
      return {
        success: false,
        message: '无效的通知类型'
      };
    }
    
    // 使用UTC时间（标准做法）
    const now = new Date();
    const expireTime = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30天后过期
    
    const notification = {
      recipientId,
      type,
      title,
      content,
      courseId: courseId || null,
      bookingId: bookingId || null,
      isRead: false,
      createTime: now,
      updateTime: now,
      expireTime
    };
    
    const result = await db.collection(NOTIFICATIONS).add({
      data: notification
    });
    
    return {
      success: true,
      message: '通知创建成功',
      data: {
        notificationId: result._id,
        ...notification
      }
    };
  } catch (error) {
    console.error('创建通知失败:', error);
    return {
      success: false,
      message: '创建通知失败',
      error: error.message
    };
  }
}

/**
 * 获取用户通知列表
 * @param {Object} data 查询参数
 * @param {string} data.userId 用户openid
 * @param {number} data.page 页码（从1开始）
 * @param {number} data.pageSize 每页数量
 * @param {string} data.filter 筛选条件：'all'|'unread'
 */
async function getNotifications(data) {
  try {
    console.log('getNotifications 被调用，参数:', data);

    const { userId, page = 1, pageSize = 20, filter = 'all' } = data;

    if (!userId) {
      console.log('错误：缺少用户ID');
      return {
        success: false,
        message: '缺少用户ID'
      };
    }

    // 构建查询条件
    const where = {
      recipientId: userId,
      expireTime: _.gt(new Date()) // 只查询未过期的通知
    };

    if (filter === 'unread') {
      where.isRead = false;
    }

    console.log('查询条件:', where);

    // 计算跳过的记录数
    const skip = (page - 1) * pageSize;

    console.log('分页参数:', { page, pageSize, skip });

    // 查询通知列表
    const { data: notifications } = await db.collection(NOTIFICATIONS)
      .where(where)
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get();

    console.log('查询到的通知数据:', notifications);
    console.log('通知数据详情:', notifications.map(n => ({
      _id: n._id,
      recipientId: n.recipientId,
      title: n.title,
      createTime: n.createTime,
      isRead: n.isRead
    })));

    // 查询总数
    const { total } = await db.collection(NOTIFICATIONS)
      .where(where)
      .count();

    console.log('通知总数:', total);

    const result = {
      success: true,
      data: {
        notifications,
        total,
        page,
        pageSize,
        hasMore: skip + notifications.length < total
      }
    };

    console.log('返回结果:', result);

    return result;
  } catch (error) {
    console.error('获取通知列表失败:', error);
    return {
      success: false,
      message: '获取通知列表失败',
      error: error.message
    };
  }
}

/**
 * 标记通知为已读
 * @param {Object} data 参数
 * @param {string} data.notificationId 通知ID
 * @param {string} data.userId 用户openid（用于权限验证）
 */
async function markAsRead(data) {
  try {
    const { notificationId, userId } = data;

    if (!notificationId || !userId) {
      return {
        success: false,
        message: '缺少必填字段'
      };
    }

    // 验证通知是否属于当前用户
    const { data: notification } = await db.collection(NOTIFICATIONS)
      .doc(notificationId)
      .get();

    if (!notification) {
      return {
        success: false,
        message: '通知不存在'
      };
    }

    if (notification.recipientId !== userId) {
      return {
        success: false,
        message: '无权限操作此通知'
      };
    }

    // 更新为已读
    await db.collection(NOTIFICATIONS).doc(notificationId).update({
      data: {
        isRead: true,
        updateTime: new Date()
      }
    });

    return {
      success: true,
      message: '标记已读成功'
    };
  } catch (error) {
    console.error('标记已读失败:', error);
    return {
      success: false,
      message: '标记已读失败',
      error: error.message
    };
  }
}

/**
 * 标记所有通知为已读
 * @param {Object} data 参数
 * @param {string} data.userId 用户openid
 */
async function markAllAsRead(data) {
  try {
    const { userId } = data;

    if (!userId) {
      return {
        success: false,
        message: '缺少用户ID'
      };
    }

    // 批量更新用户的所有未读通知
    await db.collection(NOTIFICATIONS).where({
      recipientId: userId,
      isRead: false,
      expireTime: _.gt(new Date())
    }).update({
      data: {
        isRead: true,
        updateTime: new Date()
      }
    });

    return {
      success: true,
      message: '全部标记已读成功'
    };
  } catch (error) {
    console.error('全部标记已读失败:', error);
    return {
      success: false,
      message: '全部标记已读失败',
      error: error.message
    };
  }
}

/**
 * 删除通知
 * @param {Object} data 参数
 * @param {string} data.notificationId 通知ID
 * @param {string} data.userId 用户openid（用于权限验证）
 */
async function deleteNotification(data) {
  try {
    const { notificationId, userId } = data;

    if (!notificationId || !userId) {
      return {
        success: false,
        message: '缺少必填字段'
      };
    }

    // 验证通知是否属于当前用户
    const { data: notification } = await db.collection(NOTIFICATIONS)
      .doc(notificationId)
      .get();

    if (!notification) {
      return {
        success: false,
        message: '通知不存在'
      };
    }

    if (notification.recipientId !== userId) {
      return {
        success: false,
        message: '无权限操作此通知'
      };
    }

    // 删除通知
    await db.collection(NOTIFICATIONS).doc(notificationId).remove();

    return {
      success: true,
      message: '删除成功'
    };
  } catch (error) {
    console.error('删除通知失败:', error);
    return {
      success: false,
      message: '删除通知失败',
      error: error.message
    };
  }
}

/**
 * 获取用户未读通知数量
 * @param {Object} data 参数
 * @param {string} data.userId 用户openid
 */
async function getUnreadCount(data) {
  try {
    const { userId } = data;

    if (!userId) {
      return {
        success: false,
        message: '缺少用户ID'
      };
    }

    const { total } = await db.collection(NOTIFICATIONS).where({
      recipientId: userId,
      isRead: false,
      expireTime: _.gt(new Date())
    }).count();

    return {
      success: true,
      data: {
        unreadCount: total
      }
    };
  } catch (error) {
    console.error('获取未读数量失败:', error);
    return {
      success: false,
      message: '获取未读数量失败',
      error: error.message
    };
  }
}

/**
 * 发送预约相关通知
 * @param {Object} data 参数
 * @param {string} data.type 通知类型
 * @param {string} data.courseId 课程ID
 * @param {string} data.bookingId 预约ID
 * @param {string} data.studentId 学员openid
 * @param {string} data.cancelBy 取消者（可选）
 */
async function sendBookingNotification(data) {
  try {
    const { type, courseId, bookingId, studentId, cancelBy } = data;

    if (!type || !courseId || !studentId) {
      return {
        success: false,
        message: '缺少必填字段'
      };
    }

    // 获取课程信息
    const { data: course } = await db.collection(COURSES).doc(courseId).get();
    if (!course) {
      return {
        success: false,
        message: '课程不存在'
      };
    }

    // 获取学员信息
    const { data: studentList } = await db.collection(USERS).where({
      openid: studentId
    }).get();
    const student = studentList[0];
    const studentName = student ? (student.nickName || studentId.slice(-4)) : studentId.slice(-4);

    // 获取讲师信息
    const coachOpenids = Array.isArray(course.coach) ? course.coach : [course.coach];
    const { data: coaches } = await db.collection(USERS).where({
      openid: _.in(coachOpenids)
    }).get();
    const coachMap = coaches.reduce((acc, coach) => {
      acc[coach.openid] = coach.nickName || coach.openid.slice(-4);
      return acc;
    }, {});

    // 格式化时间（转换为北京时间显示）
    const formatTime = (date) => {
      const d = new Date(date);
      // 转换为北京时间（UTC+8）
      const beijingTime = new Date(d.getTime() + 8 * 60 * 60 * 1000);
      const endTime = new Date(course.endTime);
      const beijingEndTime = new Date(endTime.getTime() + 8 * 60 * 60 * 1000);

      return `${beijingTime.getUTCFullYear()}年${beijingTime.getUTCMonth() + 1}月${beijingTime.getUTCDate()}日 ${beijingTime.getUTCHours().toString().padStart(2, '0')}:${beijingTime.getUTCMinutes().toString().padStart(2, '0')}-${beijingEndTime.getUTCHours().toString().padStart(2, '0')}:${beijingEndTime.getUTCMinutes().toString().padStart(2, '0')}`;
    };

    const timeStr = formatTime(course.startTime);
    const coachNames = coachOpenids.map(id => coachMap[id] || id.slice(-4)).join('、');

    // 获取当前预约学员列表（用于讲师通知）
    const { data: currentBookings } = await db.collection(BOOKINGS).where({
      courseId,
      status: 'upcoming'
    }).get();

    const currentStudentIds = currentBookings.map(b => b.userId);
    const { data: currentStudents } = await db.collection(USERS).where({
      openid: _.in(currentStudentIds)
    }).get();
    const currentStudentNames = currentStudents.map(s => s.nickName || s.openid.slice(-4)).join('、');

    const notifications = [];

    // 根据通知类型生成通知内容
    switch (type) {
      case 'booking_success_student':
        notifications.push({
          recipientId: studentId,
          type,
          title: '预约成功',
          content: `您已成功预约《${course.name}》，讲师：${coachNames}，时间：${timeStr}，请准时参加。`,
          courseId,
          bookingId
        });
        break;

      case 'booking_success_coach':
        for (const coachId of coachOpenids) {
          notifications.push({
            recipientId: coachId,
            type,
            title: '新增预约',
            content: `您的课程《${course.name}》有新的预约，时间：${timeStr}，当前学员：${currentStudentNames || studentName}。`,
            courseId,
            bookingId
          });
        }
        break;

      case 'booking_cancelled_student':
        notifications.push({
          recipientId: studentId,
          type,
          title: '取消预约成功',
          content: `您已取消《${course.name}》的预约，时间：${timeStr}。`,
          courseId,
          bookingId
        });
        break;

      case 'booking_cancelled_coach':
        for (const coachId of coachOpenids) {
          notifications.push({
            recipientId: coachId,
            type,
            title: '学员取消预约',
            content: `学员${studentName}已取消《${course.name}》的预约，时间：${timeStr}${currentStudentNames ? `，当前学员：${currentStudentNames}` : ''}。`,
            courseId,
            bookingId
          });
        }
        break;

      case 'booking_cancelled_by_admin_student':
        notifications.push({
          recipientId: studentId,
          type,
          title: '预约已被取消',
          content: `很抱歉，您的《${course.name}》预约已被取消，时间：${timeStr}，如有疑问请联系管理员。`,
          courseId,
          bookingId
        });
        break;

      case 'booking_cancelled_by_admin_coach':
        for (const coachId of coachOpenids) {
          notifications.push({
            recipientId: coachId,
            type,
            title: '预约被管理员取消',
            content: `管理员已取消学员${studentName}的《${course.name}》预约，时间：${timeStr}${currentStudentNames ? `，当前学员：${currentStudentNames}` : ''}。`,
            courseId,
            bookingId
          });
        }
        break;
    }

    // 批量创建通知
    const results = [];
    for (const notification of notifications) {
      try {
        const result = await createNotification(notification);
        results.push(result);
      } catch (error) {
        console.error('创建通知失败:', error);
        results.push({ success: false, error: error.message });
      }
    }

    return {
      success: true,
      message: '通知发送完成',
      data: {
        results,
        notificationCount: notifications.length
      }
    };
  } catch (error) {
    console.error('发送预约通知失败:', error);
    return {
      success: false,
      message: '发送预约通知失败',
      error: error.message
    };
  }
}

/**
 * 发送课程下线通知
 * @param {Object} data 参数
 * @param {string} data.courseId 课程ID
 */
async function sendCourseOfflineNotification(data) {
  try {
    const { courseId } = data;

    if (!courseId) {
      return {
        success: false,
        message: '缺少课程ID'
      };
    }

    // 获取课程信息
    const { data: course } = await db.collection(COURSES).doc(courseId).get();
    if (!course) {
      return {
        success: false,
        message: '课程不存在'
      };
    }

    // 获取讲师信息
    const coachOpenids = Array.isArray(course.coach) ? course.coach : [course.coach];
    const { data: coaches } = await db.collection(USERS).where({
      openid: _.in(coachOpenids)
    }).get();

    // 获取已预约的学员
    const { data: bookings } = await db.collection(BOOKINGS).where({
      courseId,
      status: 'upcoming'
    }).get();

    const studentIds = bookings.map(b => b.userId);
    const { data: students } = await db.collection(USERS).where({
      openid: _.in(studentIds)
    }).get();

    // 格式化时间（转换为北京时间显示）
    const formatTime = (date) => {
      const d = new Date(date);
      // 转换为北京时间（UTC+8）
      const beijingTime = new Date(d.getTime() + 8 * 60 * 60 * 1000);
      const endTime = new Date(course.endTime);
      const beijingEndTime = new Date(endTime.getTime() + 8 * 60 * 60 * 1000);

      return `${beijingTime.getUTCFullYear()}年${beijingTime.getUTCMonth() + 1}月${beijingTime.getUTCDate()}日 ${beijingTime.getUTCHours().toString().padStart(2, '0')}:${beijingTime.getUTCMinutes().toString().padStart(2, '0')}-${beijingEndTime.getUTCHours().toString().padStart(2, '0')}:${beijingEndTime.getUTCMinutes().toString().padStart(2, '0')}`;
    };

    const timeStr = formatTime(course.startTime);
    const notifications = [];

    // 给讲师发送通知
    for (const coach of coaches) {
      notifications.push({
        recipientId: coach.openid,
        type: 'course_offline_coach',
        title: '课程已下线',
        content: `您的课程《${course.name}》已被下线，时间：${timeStr}。`,
        courseId
      });
    }

    // 给已预约学员发送通知
    for (const student of students) {
      notifications.push({
        recipientId: student.openid,
        type: 'course_offline_student',
        title: '课程已取消',
        content: `很抱歉，您预约的《${course.name}》已被取消，时间：${timeStr}，如有疑问请联系管理员。`,
        courseId
      });
    }

    // 批量创建通知
    const results = [];
    for (const notification of notifications) {
      try {
        const result = await createNotification(notification);
        results.push(result);
      } catch (error) {
        console.error('创建通知失败:', error);
        results.push({ success: false, error: error.message });
      }
    }

    return {
      success: true,
      message: '课程下线通知发送完成',
      data: {
        results,
        notificationCount: notifications.length,
        coachCount: coaches.length,
        studentCount: students.length
      }
    };
  } catch (error) {
    console.error('发送课程下线通知失败:', error);
    return {
      success: false,
      message: '发送课程下线通知失败',
      error: error.message
    };
  }
}

/**
 * 清理过期通知
 * @param {Object} data 参数（可选）
 */
async function cleanExpiredNotifications(data) {
  try {
    const now = new Date();

    // 查询过期的通知
    const { data: expiredNotifications } = await db.collection(NOTIFICATIONS).where({
      expireTime: _.lt(now)
    }).get();

    if (expiredNotifications.length === 0) {
      return {
        success: true,
        message: '没有过期通知需要清理',
        data: {
          deletedCount: 0
        }
      };
    }

    // 批量删除过期通知
    const deletePromises = expiredNotifications.map(notification =>
      db.collection(NOTIFICATIONS).doc(notification._id).remove()
    );

    await Promise.all(deletePromises);

    return {
      success: true,
      message: '过期通知清理完成',
      data: {
        deletedCount: expiredNotifications.length
      }
    };
  } catch (error) {
    console.error('清理过期通知失败:', error);
    return {
      success: false,
      message: '清理过期通知失败',
      error: error.message
    };
  }
}

/**
 * 创建测试通知
 * 用于调试和测试通知系统
 * @param {Object} data 参数
 * @param {string} data.userId 用户openid
 */
async function createTestNotifications(data) {
  try {
    const { userId } = data;

    if (!userId) {
      return {
        success: false,
        message: '缺少用户ID'
      };
    }

    console.log('为用户创建测试通知:', userId);

    const now = new Date();
    const testNotifications = [
      {
        recipientId: userId,
        type: 'booking_success_student',
        title: '预约成功',
        content: '您已成功预约《瑜伽基础课程》，讲师：张老师，时间：2024年1月15日 14:00-16:00，请准时参加。',
        courseId: 'test_course_1',
        bookingId: 'test_booking_1',
        isRead: false,
        createTime: now,
        updateTime: now,
        expireTime: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
      },
      {
        recipientId: userId,
        type: 'daily_reminder_student',
        title: '明日课程提醒',
        content: '您明天有1节课程：《普拉提进阶课程》，讲师：李老师，时间：10:00-12:00，请准时参加。',
        courseId: 'test_course_2',
        isRead: false,
        createTime: new Date(now.getTime() - 2 * 60 * 60 * 1000), // 2小时前
        updateTime: new Date(now.getTime() - 2 * 60 * 60 * 1000),
        expireTime: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
      },
      {
        recipientId: userId,
        type: 'course_reminder_student',
        title: '课程即将开始',
        content: '您预约的《冥想放松课程》将在3小时后开始，讲师：王老师，时间：19:00-20:30，请准时参加。',
        courseId: 'test_course_3',
        isRead: true,
        createTime: new Date(now.getTime() - 24 * 60 * 60 * 1000), // 1天前
        updateTime: new Date(now.getTime() - 24 * 60 * 60 * 1000),
        expireTime: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
      }
    ];

    const results = [];
    for (const notification of testNotifications) {
      try {
        const result = await db.collection(NOTIFICATIONS).add({
          data: notification
        });
        results.push({
          success: true,
          notificationId: result._id,
          type: notification.type
        });
        console.log('创建测试通知成功:', notification.type, result._id);
      } catch (error) {
        console.error('创建测试通知失败:', notification.type, error);
        results.push({
          success: false,
          type: notification.type,
          error: error.message
        });
      }
    }

    return {
      success: true,
      message: '测试通知创建完成',
      data: {
        userId,
        results,
        createdCount: results.filter(r => r.success).length,
        totalCount: testNotifications.length
      }
    };
  } catch (error) {
    console.error('创建测试通知失败:', error);
    return {
      success: false,
      message: '创建测试通知失败',
      error: error.message
    };
  }
}

/**
 * 清空用户的所有通知
 * @param {Object} data 参数
 * @param {string} data.userId 用户openid
 */
async function clearAllNotifications(data) {
  try {
    const { userId } = data;

    if (!userId) {
      return {
        success: false,
        message: '缺少用户ID'
      };
    }

    console.log('清空用户所有通知:', userId);

    // 删除用户的所有通知
    const result = await db.collection(NOTIFICATIONS)
      .where({
        recipientId: userId
      })
      .remove();

    console.log('清空通知结果:', result);

    return {
      success: true,
      message: '清空成功',
      data: {
        userId,
        deletedCount: result.stats.removed
      }
    };
  } catch (error) {
    console.error('清空通知失败:', error);
    return {
      success: false,
      message: '清空通知失败',
      error: error.message
    };
  }
}

/**
 * 检查是否有新课程
 * 用于在活动表页面显示Badge徽标
 * @param {Object} data 参数
 * @param {string} data.userId 用户openid
 */
async function checkNewCourses(data) {
  try {
    const { userId } = data;

    if (!userId) {
      return {
        success: false,
        message: '缺少用户ID'
      };
    }

    console.log('检查用户新课程:', userId);

    // 获取用户上次查看课程表的时间
    const userCollection = db.collection('users');
    const { data: userData } = await userCollection.where({
      openid: userId
    }).get();

    if (userData.length === 0) {
      return {
        success: true,
        data: {
          hasNewCourses: false
        }
      };
    }

    const user = userData[0];
    const lastViewTime = user.lastScheduleViewTime || new Date(0); // 如果没有记录，使用最早时间

    // 查询在用户上次查看后创建的课程
    const coursesCollection = db.collection('courses');
    const { data: newCourses } = await coursesCollection.where({
      createTime: _.gt(lastViewTime),
      status: 'published' // 只查询已发布的课程
    }).get();

    console.log('新课程数量:', newCourses.length);

    return {
      success: true,
      data: {
        hasNewCourses: newCourses.length > 0,
        newCoursesCount: newCourses.length
      }
    };
  } catch (error) {
    console.error('检查新课程失败:', error);
    return {
      success: false,
      message: '检查新课程失败',
      error: error.message
    };
  }
}

/**
 * 更新用户查看课程表的时间
 * 当用户查看课程表时调用，用于清除Badge
 * @param {Object} data 参数
 * @param {string} data.userId 用户openid
 */
async function updateScheduleViewTime(data) {
  try {
    const { userId } = data;

    if (!userId) {
      return {
        success: false,
        message: '缺少用户ID'
      };
    }

    const now = new Date();
    const userCollection = db.collection('users');

    // 更新用户的最后查看时间
    await userCollection.where({
      openid: userId
    }).update({
      data: {
        lastScheduleViewTime: now
      }
    });

    console.log('更新用户课程表查看时间:', userId, now);

    return {
      success: true,
      data: {
        updateTime: now
      }
    };
  } catch (error) {
    console.error('更新查看时间失败:', error);
    return {
      success: false,
      message: '更新查看时间失败',
      error: error.message
    };
  }
}
