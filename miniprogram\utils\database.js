// coach=讲师，course=活动，数据库结构保持不变
// database.js
// 微信云开发数据库操作工具函数 - 封装了与微信云数据库交互的各种操作

// 导入常量文件中定义的预约状态枚举
// import语句用于从其他文件导入变量、函数或对象
import { BOOKING_STATUS } from './constants.js';

// 课程数据库集合名称 - 定义一个常量存储数据库集合名称，方便统一管理
// const关键字用于声明一个不可重新赋值的变量
const COURSES_COLLECTION = 'courses';

/**
 * 获取数据库实例 - 创建并返回微信云数据库实例
 * @returns {Object} 数据库实例 - 返回可用于操作数据库的对象
 */
// 这是一个内部函数，没有使用export导出，只能在本文件内使用
const getDatabase = () => {
  // try-catch块用于捕获可能发生的错误
  try {
    // wx.cloud.database()是微信小程序云开发提供的API，用于获取数据库实例
    return wx.cloud.database();
  } catch (error) {
    // 如果获取数据库实例失败，记录错误并抛出异常
    console.error('获取数据库实例失败:', error);
    throw new Error('数据库初始化失败');
  }
};

/**
 * 获取热门课程列表（前5个）- 获取最新创建的5个课程
 * @returns {Promise<Array>} 热门课程列表 - 返回包含课程对象的数组
 */
// export关键字使这个函数可以被其他文件导入使用
// async表示这是一个异步函数，可以使用await等待Promise完成
export const getHotCourses = async () => {
  try {
    // 获取数据库实例
    const db = getDatabase();
    
    // 使用解构赋值从返回对象中提取data属性
    // await等待数据库查询完成
    // collection()指定要查询的集合
    // orderBy()指定排序方式，'desc'表示降序(从新到旧)
    // limit(5)限制返回结果数量为5个
    // get()执行查询并获取结果
    const { data } = await db.collection(COURSES_COLLECTION)
      .orderBy('createTime', 'desc') // 按创建时间倒序，最新的排在前面
      .limit(5) // 限制返回5个结果
      .get();
    
    // 返回查询结果
    return data;
  } catch (error) {
    // 如果查询失败，记录错误并显示提示
    console.error('获取热门课程失败:', error);
    // wx.showToast()是微信小程序的API，用于显示消息提示框
    wx.showToast({
      title: '获取课程信息失败',
      icon: 'error' // 显示错误图标
    });
    // 返回空数组，避免调用者收到undefined或null
    return [];
  }
};

/**
 * 获取所有课程列表
 * @param {Object} options 查询选项
 * @param {string} options.coachId 讲师ID
 * @returns {Promise<Array>} 课程列表
 */
export const getCourseList = async (options = {}) => {
  try {
    const db = getDatabase();
    
    let query = db.collection(COURSES_COLLECTION);
    
    // 构建查询条件
    const whereCondition = {};
    
    if (options.coachId) {
      whereCondition.coach = options.coachId;
    }
    
    // 应用查询条件
    if (Object.keys(whereCondition).length > 0) {
      query = query.where(whereCondition);
    }
    
    const { data } = await query
      .orderBy('createTime', 'desc')
      .get();
    
    // 为每个课程计算剩余名额和可用状态
    const coursesWithBookingInfo = await Promise.all(
      data.map(async (course) => {
        // 获取该课程的预约记录数量（只统计状态为upcoming的）
        const bookingCount = await db.collection('bookings')
          .where({
            courseId: course._id,
            status: BOOKING_STATUS.UPCOMING // 只统计有效的预约
          })
          .count();
        
        const bookedCount = bookingCount.total;
        const remaining = course.capacity - bookedCount;
        const available = remaining > 0;
        
        return {
          ...course,
          remaining,
          available,
          bookedCount
        };
      })
    );
    
    return coursesWithBookingInfo;
  } catch (error) {
    console.error('获取课程列表失败:', error);
    wx.showToast({
      title: '获取课程信息失败',
      icon: 'error'
    });
    return [];
  }
};

/**
 * 根据ID获取课程详情 - 查询单个课程的完整信息
 * @param {string} courseId 课程ID - 要查询的课程唯一标识符
 * @returns {Promise<Object>} 课程详情 - 返回包含课程完整信息的对象，失败时返回null
 */
export const getCourseDetail = async (courseId) => {
  try {
    // 获取数据库实例
    const db = getDatabase();
    
    // 使用doc()方法根据ID查询单个文档
    // doc()接收文档ID作为参数
    // get()执行查询并获取结果
    const { data } = await db.collection(COURSES_COLLECTION)
      .doc(courseId)  // 指定要查询的文档ID
      .get();         // 执行查询
    
    // 返回查询到的课程数据
    return data;
  } catch (error) {
    // 如果查询失败，记录错误并显示提示
    console.error('获取课程详情失败:', error);
    wx.showToast({
      title: '获取课程详情失败',
      icon: 'error'
    });
    // 查询失败时返回null
    return null;
  }
};

/**
 * 预约课程 - 为用户预约指定的课程
 * @param {string} courseId 课程ID - 要预约的课程ID
 * @param {string} userId 用户ID - 进行预约的用户ID
 * @returns {Promise<boolean>} 预约是否成功 - 返回true表示预约成功，false表示失败
 */
export const bookCourse = async (courseId, userId) => {
  try {
    // 调用云函数进行预约 - 使用云函数可以进行更复杂的业务逻辑和权限控制
    // wx.cloud.callFunction调用微信云开发的云函数
    const result = await wx.cloud.callFunction({
      name: 'bookingManagement',  // 云函数名称
      data: {              // 传递给云函数的参数
        action: 'bookCourse',
        data: {
          courseId,          // ES6简写语法，等同于courseId: courseId
          userId             // ES6简写语法，等同于userId: userId
        }
      }
    });
    
    // 检查云函数返回的结果
    if (result.result.success) {
      // 预约成功
      return true;
    } else {
      // 预约失败，记录错误信息并显示提示
      console.error('预约课程失败:', result.result.message);
      wx.showToast({
        title: result.result.message || '预约失败', // 使用云函数返回的错误消息或默认消息
        icon: 'error'
      });
      return false;
    }
  } catch (error) {
    // 捕获并处理调用云函数过程中可能发生的错误
    console.error('预约课程失败:', error);
    wx.showToast({
      title: '预约失败，请重试',
      icon: 'error'
    });
    return false;
  }
};

/**
 * 取消预约 - 取消用户已经预约的课程
 * @param {string} courseId 课程ID - 要取消预约的课程ID
 * @param {string} userId 用户ID - 进行取消操作的用户ID
 * @returns {Promise<boolean>} 取消是否成功 - 返回true表示取消成功，false表示失败
 */
export const cancelBooking = async (courseId, userId) => {
  try {
    // 调用云函数取消预约 - 使用云函数可以进行更复杂的业务逻辑和权限控制
    const result = await wx.cloud.callFunction({
      name: 'bookingManagement',  // 云函数名称
      data: {                 // 传递给云函数的参数
        action: 'cancelBooking',
        data: {
          courseId,             // ES6简写语法，等同于courseId: courseId
          userId                // ES6简写语法，等同于userId: userId
        }
      }
    });
    
    // 检查云函数返回的结果
    if (result.result.success) {
      // 取消预约成功
      return true;
    } else {
      // 取消预约失败，记录错误信息并显示提示
      console.error('取消预约失败:', result.result.message);
      wx.showToast({
        title: result.result.message || '取消预约失败', // 使用云函数返回的错误消息或默认消息
        icon: 'error'
      });
      return false;
    }
  } catch (error) {
    // 捕获并处理调用云函数过程中可能发生的错误
    console.error('取消预约失败:', error);
    wx.showToast({
      title: '取消预约失败，请重试',
      icon: 'error'
    });
    return false;
  }
};

/**
 * 获取用户的预约记录 - 查询指定用户的所有预约记录
 * @param {string} userId 用户ID - 要查询预约记录的用户ID
 * @returns {Promise<Array>} 预约记录列表 - 返回包含预约记录对象的数组
 */
export const getUserBookings = async (userId) => {
  try {
    // 获取数据库实例
    const db = getDatabase();
    
    // 查询指定用户的所有预约记录
    // where()方法用于指定查询条件
    // orderBy()方法用于指定排序方式
    const { data } = await db.collection('bookings')
      .where({
        userId: userId  // 查询条件：匹配指定的用户ID
      })
      .orderBy('createTime', 'desc')  // 按创建时间倒序排列，最新的排在前面
      .get();  // 执行查询
    
    // 返回查询结果
    return data;
  } catch (error) {
    // 如果查询失败，记录错误并显示提示
    console.error('获取用户预约记录失败:', error);
    wx.showToast({
      title: '获取预约记录失败',
      icon: 'error'
    });
    // 返回空数组，避免调用者收到undefined或null
    return [];
  }
};

/**
 * 获取所有课程的预约记录 - 查询所有有效的预约记录
 * @returns {Promise<Array>} 所有课程的预约记录 - 返回包含预约记录对象的数组
 */
export const getCourseBookings = async () => {
  try {
    // 获取数据库实例
    const db = getDatabase();
    
    // 查询所有状态为"即将开始"的有效预约记录
    const { data } = await db.collection('bookings')
      .where({
        status: BOOKING_STATUS.UPCOMING  // 只查询状态为"即将开始"的预约
      })
      .get();  // 执行查询
    
    // 返回查询结果
    return data;
  } catch (error) {
    // 如果查询失败，记录错误并显示提示
    console.error('获取课程预约记录失败:', error);
    wx.showToast({
      title: '获取预约记录失败',
      icon: 'error'
    });
    // 返回空数组，避免调用者收到undefined或null
    return [];
  }
};