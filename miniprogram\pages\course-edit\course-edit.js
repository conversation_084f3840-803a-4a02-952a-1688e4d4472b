// course-edit.js
// 课程编辑页面逻辑文件
// 这是小程序的课程编辑页面，负责课程的创建、编辑、模板管理等功能
// 类似于Web应用的内容管理系统(CMS)或移动应用的表单编辑页面

/**
 * 模块导入说明
 *
 * 课程编辑页面的特点：
 * 1. 复杂表单：包含多种输入类型（文本、时间、选择器等）
 * 2. 数据验证：严格的表单验证和错误处理
 * 3. 模板系统：支持课程模板的创建和使用
 * 4. 多模式支持：新建、编辑、模板等不同模式
 * 5. 实时预览：表单数据的实时验证和反馈
 *
 * 技术复杂度：
 * 这是项目中表单逻辑最复杂的页面之一，涉及：
 * - 复杂的数据结构管理
 * - 多种输入组件的状态同步
 * - 表单验证和错误处理
 * - 时间计算和格式化
 * - 讲师选择和关联
 */

// 导入课程难度常量
// 用于课程难度的标准化定义和显示
import { COURSE_DIFFICULTY, COURSE_DIFFICULTY_TEXT } from '../../utils/constants.js';

// 导入Toast工具函数
// 用于用户操作反馈和状态提示
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

/**
 * Page()函数：注册课程编辑页面
 *
 * 页面功能：
 * 1. 课程信息编辑：名称、时间、地点、容量、详情等
 * 2. 讲师管理：选择和分配课程讲师
 * 3. 时间管理：开始时间、结束时间、时长的联动计算
 * 4. 模板功能：创建、使用、管理课程模板
 * 5. 表单验证：实时验证和错误提示
 * 6. 多模式支持：新建课程、编辑课程、创建模板
 *
 * 设计模式：
 * - 表单驱动：以表单数据为核心的页面设计
 * - 状态管理：复杂的表单状态和UI状态管理
 * - 组件化：使用TDesign组件构建统一的表单界面
 * - 响应式：实时的数据验证和UI反馈
 */
Page({
  /**
   * data: 页面数据对象
   *
   * 数据结构设计：
   * 1. 模式控制：页面模式和状态标识
   * 2. 课程数据：核心的课程信息对象
   * 3. 选择器数据：各种选择器的选项和状态
   * 4. UI状态：表单验证、提交状态、弹窗状态
   * 5. 辅助数据：讲师列表、模板列表等
   *
   * 数据复杂度：
   * 这个页面的数据结构相当复杂，需要管理：
   * - 表单数据的双向绑定
   * - 多个选择器的状态同步
   * - 时间数据的多种格式转换
   * - 表单验证的错误状态
   */
  data: {
    /**
     * 页面模式控制
     */

    // 活动模式标识
    // 字符串类型，控制页面的功能模式
    // 可能的值：'course'（课程模式）、'template'（模板模式）
    mode: 'course',

    // 是否为编辑模式
    // 布尔值，true表示编辑现有课程，false表示创建新课程
    isEdit: false,

    // 课程ID（编辑模式下使用）
    // 字符串类型，编辑现有课程时的课程标识
    courseId: '',

    // 模板ID（使用模板创建课程时使用）
    // 字符串类型，基于模板创建课程时的模板标识
    templateId: '',

    /**
     * 核心课程数据对象
     *
     * 数据结构说明：
     * 这是页面的核心数据结构，包含课程的所有基础信息
     * 设计时考虑了数据的完整性和默认值的合理性
     */
    course: {
      // 课程名称
      // 字符串类型，课程的标题或名称
      name: '',

      // 讲师数组
      // 数组类型，存储讲师的openid（微信用户唯一标识）
      // 支持多讲师课程的设计
      coach: [],

      /**
       * 默认开始时间设置
       *
       * 使用立即执行函数表达式(IIFE)：
       * (() => { ... })()
       *
       * 设计逻辑：
       * 1. 获取当前日期
       * 2. 设置为当天上午8点
       * 3. 返回Date对象
       *
       * 为什么选择8点：
       * 大多数健身课程在上午8点开始，这是一个合理的默认值
       */
      startTime: (() => {
        const now = new Date();
        now.setHours(8, 0, 0, 0);  // 设置为8:00:00.000
        return new Date(now);
      })(),

      /**
       * 默认结束时间设置
       *
       * 计算逻辑：
       * 1. 基于开始时间（8点）
       * 2. 增加1小时（60 * 60 * 1000毫秒）
       * 3. 默认课程时长为1小时
       *
       * 时间计算：
       * getTime()：获取时间戳（毫秒）
       * + 60 * 60 * 1000：增加1小时的毫秒数
       * new Date()：转换回Date对象
       */
      endTime: (() => {
        const now = new Date();
        now.setHours(8, 0, 0, 0);
        return new Date(now.getTime() + 60 * 60 * 1000); // 默认1小时
      })(),

      // 课程时长（分钟）
      // 数字类型，默认60分钟（1小时）
      duration: 60,

      // 上课地点
      // 字符串类型，课程举办的场地或地址
      venue: '',

      // 课程容量
      // 数字类型，最大参与人数
      capacity: 0,

      // 课程详情描述
      // 字符串类型，课程的详细介绍和说明
      // 注释说明：只保留字符串格式，不使用富文本
      activityDetail: '',

      // 课程状态
      // 字符串类型，课程的发布状态
      // 'offline'：下线状态，'online'：上线状态
      status: 'offline',

      // 课程图片
      // 数组类型，存储课程图片的云存储fileID
      // 支持多张图片，用于课程详情页展示
      images: []
    },

    /**
     * 讲师相关数据
     */

    // 讲师列表（原始数据）
    // 数组类型，从数据库获取的所有可用讲师信息
    coachList: [],

    // 讲师选择器选项
    // 数组类型，格式化后的讲师选项，用于选择器组件
    coachOptions: [],

    // 已选择的讲师
    // 数组类型，当前选中的讲师信息
    selectedCoaches: [],

    // 当前选中的单个讲师（用于头像显示）
    // 对象类型，包含讲师的完整信息（姓名、头像、职位等）
    selectedCoach: null,

    // 讲师选择器显示文本
    // 字符串类型，在选择器中显示的文本
    coachText: '请选择讲师',



    /**
     * 时间相关数据
     */

    // 开始时间显示文本
    // 字符串类型，格式化后的开始时间文本
    startTimeText: '',

    // 开始时间选择器显示状态
    // 布尔值，控制时间选择器弹窗的显示
    startTimeVisible: false,

    /**
     * 开始时间时间戳
     *
     * 数据格式说明：
     * 使用时间戳格式存储，便于时间选择器组件使用
     * 与course.startTime保持同步
     */
    startTime: (() => {
      const now = new Date();
      now.setHours(8, 0, 0, 0);
      return now.getTime();  // 返回时间戳
    })(),

    // 结束时间显示文本
    // 字符串类型，格式化后的结束时间文本
    endTimeText: '',

    /**
     * 模板相关数据
     */

    // 模板列表
    // 数组类型，可用的课程模板列表
    templateList: [],

    // 模板选择器显示状态
    // 布尔值，控制模板选择器弹窗的显示
    templatePickerVisible: false,

    // 模板选择器选项
    // 数组类型，格式化后的模板选项
    templatePickerOptions: [],

    // 模板选择器选中值
    // 数组类型，选择器组件的值格式
    templatePickerValue: [],

    /**
     * 表单状态管理
     */

    // 表单验证错误
    // 对象类型，存储各字段的验证错误信息
    // 格式：{ fieldName: 'error message' }
    formErrors: {},

    // 表单提交状态
    // 布尔值，true表示正在提交，防止重复提交
    isSubmitting: false,

    /**
     * UI控制状态
     *
     * 新增的UI状态控制，用于管理各种弹窗和选择器
     */

    // 讲师选择器弹出状态
    // 布尔值，控制讲师选择器弹窗的显示状态
    coachVisible: false,

    // 讲师选择器绑定值
    // 数组类型，与讲师选择器组件双向绑定的值
    coachValue: [],

    /**
     * 图片管理相关状态
     */

    // 课程图片列表（包含临时URL）
    // 数组类型，存储图片的详细信息，包括fileID和临时访问URL
    courseImages: [],

    // 图片上传状态
    // 布尔值，true表示正在上传图片
    isUploadingImage: false,

    // 图片预览状态
    // 布尔值，控制图片预览弹窗的显示
    imagePreviewVisible: false,

    // 当前预览的图片索引
    // 数字类型，用于图片预览时的索引定位
    currentImageIndex: 0
  },

  /**
   * onLoad: 页面生命周期函数 - 页面加载时调用
   *
   * 主要任务：
   * 1. 隐藏TabBar（编辑页面不需要底部导航）
   * 2. 解析页面参数（确定页面模式和编辑状态）
   * 3. 初始化页面数据（加载讲师、模板等基础数据）
   *
   * @param {Object} options - 页面参数对象
   *   - options.id: 课程ID或模板ID（编辑模式）
   *   - options.mode: 页面模式（'course' | 'template'）
   *   - options.templateId: 模板ID（基于模板创建课程）
   */
  async onLoad(options) {
    // 隐藏TabBar导航栏
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setSelectedValue(-1);
    }

    // 解析页面参数，确定页面模式和状态
    this.parsePageOptions(options);

    // 初始化页面数据
    await this.initializeData();
  },

  /**
   * onShow: 页面生命周期函数 - 页面显示时调用
   *
   * 设计考虑：
   * 每次显示都刷新基础数据，确保信息是最新的
   */
  onShow() {
    // 每次显示页面时都隐藏TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setSelectedValue(-1);
    }

    // 每次显示页面时重新加载讲师列表和模板列表
    this.fetchCoachList();
    this.fetchTemplateList();
  },

  /**
   * parsePageOptions: 解析页面参数方法
   *
   * 根据传入的参数确定页面的工作模式和状态
   * 支持多种页面使用场景
   */
  parsePageOptions(options) {
    const { id, mode, templateId } = options;
    if (mode === 'template') {
      this.setData({ mode: 'template' });
      if (id) {
        this.setData({ isEdit: true, templateId: id });
      }
    } else {
      this.setData({ mode: 'course' });
      if (id) {
        this.setData({ isEdit: true, courseId: id });
      }
    }
    if (templateId) {
      this.setData({ templateId });
    }
  },

  /**
   * initializeData: 初始化页面数据的异步方法
   *
   * 根据页面模式和状态加载相应的数据
   * 确保页面显示前所有必要数据都已准备就绪
   */
  async initializeData() {
    // 先加载基础数据（讲师列表和模板列表）
    await Promise.all([
      this.fetchCoachList(),
      this.fetchTemplateList()
    ]);

    // 基础数据加载完成后，再处理具体的页面数据
    if (this.data.isEdit) {
      await this.loadEditData();
    } else if (this.data.templateId) {
      await this.applyTemplate();
    } else {
      // 新建时重置为当天8点
      const now = new Date();
      now.setHours(8, 0, 0, 0);
      const end = new Date(now.getTime() + (this.data.course.duration || 60) * 60 * 1000);
      this.setData({
        'course.startTime': new Date(now),
        'course.endTime': end,
        startTime: now.getTime(),
        startTimeText: this.formatDateTime(now),
        endTimeText: this.formatDateTime(end.getTime())
      });
    }
  },

  /**
   * loadEditData: 加载编辑数据的异步方法
   *
   * 功能说明：
   * 在编辑模式下，从数据库加载现有的课程或模板数据
   * 根据页面模式选择不同的数据表进行查询
   *
   * 数据表选择逻辑：
   * - 课程模式：从 'courses' 表加载课程数据
   * - 模板模式：从 'coursesTemplate' 表加载模板数据
   *
   * 错误处理：
   * 加载失败时显示错误提示，但不阻断页面使用
   */
  async loadEditData() {
    // 获取云数据库实例
    const db = wx.cloud.database();

    try {
      // 根据页面模式选择数据表
      const collection = this.data.mode === 'template' ? 'coursesTemplate' : 'courses';

      // 根据页面模式选择ID字段
      const id = this.data.mode === 'template' ? this.data.templateId : this.data.courseId;

      // 查询指定文档
      const res = await db.collection(collection).doc(id).get();
      const data = res.data;

      // 填充表单数据
      this.fillFormData(data);
    } catch (error) {
      // 错误处理：记录日志并显示用户友好的错误信息
      console.error('加载数据失败:', error);
      this.showMessage('数据加载失败', 'error');
    }
  },

  /**
   * applyTemplate: 应用模板数据的异步方法
   *
   * 功能说明：
   * 基于现有模板创建新课程时，加载模板数据并填充到表单
   * 这是一种快速创建课程的方式，避免重复输入相同信息
   *
   * 使用场景：
   * 1. 定期课程：每周的固定课程可以基于模板快速创建
   * 2. 系列课程：相似的课程可以基于同一模板创建
   * 3. 标准化：确保同类课程的信息格式一致
   *
   * 与编辑模式的区别：
   * - 编辑模式：修改现有数据，保持ID不变
   * - 模板应用：创建新数据，使用模板内容但生成新ID
   */
  async applyTemplate() {
    const db = wx.cloud.database();

    try {
      // 从模板表中查询指定模板
      const res = await db.collection('coursesTemplate').doc(this.data.templateId).get();
      const template = res.data;

      // 使用模板数据填充表单
      this.fillFormData(template);

      // 显示成功提示
      this.showMessage('模板应用成功', 'success');
    } catch (error) {
      // 错误处理
      console.error('应用模板失败:', error);
      this.showMessage('模板应用失败', 'error');
    }
  },

  /**
   * fillFormData: 填充表单数据的方法
   *
   * 功能说明：
   * 将从数据库获取的数据填充到页面表单中
   * 处理数据格式转换和兼容性问题
   *
   * @param {Object} data - 从数据库获取的原始数据
   *
   * 数据处理逻辑：
   * 1. 类型转换：将数据库的数据转换为表单需要的格式
   * 2. 默认值处理：为空值提供合理的默认值
   * 3. 兼容性处理：处理历史数据格式的兼容问题
   *
   * 兼容性说明：
   * activityDetail字段在历史版本中可能是对象格式
   * 现在统一使用字符串格式，需要进行兼容处理
   */
  fillFormData(data) {
    console.log('fillFormData 开始填充数据:', data);
    console.log('当前coachList长度:', this.data.coachList?.length || 0);
    console.log('当前coachOptions长度:', this.data.coachOptions?.length || 0);

    /**
     * 活动详情字段兼容性处理
     *
     * 历史问题：
     * 早期版本的activityDetail可能存储为对象格式：
     * { description: "课程描述", other: "其他信息" }
     *
     * 现在统一为字符串格式，需要兼容处理
     *
     * 处理逻辑：
     * 1. 如果是字符串：直接使用
     * 2. 如果是对象：提取description字段
     * 3. 如果都不是：使用空字符串
     */
    let activityDetail = '';
    if (typeof data.activityDetail === 'string') {
      // 字符串格式：直接使用
      activityDetail = data.activityDetail;
    } else if (data.activityDetail && typeof data.activityDetail === 'object') {
      // 对象格式：提取description字段
      activityDetail = data.activityDetail.description || '';
    }

    console.log('课程讲师数据:', data.coach);

    /**
     * 批量更新表单数据
     *
     * setData语法说明：
     * 使用点语法更新嵌套对象的属性
     * 'course.name' 等价于 course: { name: value }
     *
     * 默认值处理：
     * 使用 || 操作符为空值提供默认值
     * 确保表单字段不会显示 undefined 或 null
     *
     * 数据类型转换：
     * - 时间字段：将字符串或时间戳转换为Date对象
     * - 数组字段：确保是数组格式，空值时使用空数组
     * - 数字字段：提供合理的默认数值
     */
    // 处理时间数据
    const startTime = data.startTime ? new Date(data.startTime) : new Date();
    const endTime = data.endTime ? new Date(data.endTime) : new Date();

    this.setData({
      'course.name': data.name || '',                    // 课程名称
      'course.coach': data.coach || [],                  // 讲师数组
      'course.startTime': startTime,                     // 开始时间
      'course.endTime': endTime,                         // 结束时间
      'course.duration': data.duration || 60,           // 课程时长（分钟）
      'course.venue': data.venue || '',                 // 上课地点
      'course.capacity': data.capacity || 0,            // 课程容量
      'course.activityDetail': activityDetail,          // 课程详情
      'course.status': data.status || 'offline',        // 课程状态
      'course.images': data.images || [],               // 课程图片

      // 设置时间显示文本
      startTime: startTime.getTime(),
      startTimeText: this.formatDateTime(startTime),
      endTimeText: this.formatDateTime(endTime)
    });

    // 如果有讲师数据，需要更新讲师选择器的状态
    if (data.coach && data.coach.length > 0) {
      // 异步更新讲师显示状态，不阻塞其他数据的加载
      this.updateCoachDisplayFromData(data.coach).catch(error => {
        console.error('更新讲师显示状态失败:', error);
      });
    }

    // 如果有图片数据，加载图片的临时访问URL
    if (data.images && data.images.length > 0) {
      this.loadCourseImages(data.images);
    }
  },

  /**
   * fetchCoachList: 获取讲师列表的异步方法
   *
   * 功能说明：
   * 从数据库获取所有可用的讲师信息，并关联用户基础信息
   * 为讲师选择器提供数据源
   *
   * 数据关联逻辑：
   * 1. 从 coachInfo 表获取讲师专业信息
   * 2. 从 users 表获取讲师的用户基础信息（昵称、头像等）
   * 3. 将两个表的数据关联，形成完整的讲师信息
   *
   * 性能优化：
   * 使用批量查询避免单次查询数据量过大的限制
   * 云数据库的 in 查询有20个条件的限制，需要分批处理
   */
  async fetchCoachList() {
    const db = wx.cloud.database();

    try {
      // 第一步：获取所有讲师的专业信息
      const res = await db.collection('coachInfo').get();
      const coachList = res.data;

      // 提取所有讲师的openid，用于查询用户信息
      const openids = coachList.map(item => item.openid);

      // 第二步：批量获取讲师的用户基础信息
      const users = [];
      if (openids.length > 0) {
        /**
         * 批量查询优化
         *
         * 限制说明：
         * 云数据库的 in 查询最多支持20个条件
         * 当讲师数量超过20个时，需要分批查询
         *
         * 分批逻辑：
         * 1. 设置批次大小为20
         * 2. 使用 slice 方法分割openids数组
         * 3. 逐批查询并合并结果
         */
        const batchSize = 20;
        for (let i = 0; i < openids.length; i += batchSize) {
          // 获取当前批次的openids
          const batchOpenids = openids.slice(i, i + batchSize);

          // 查询当前批次的用户信息
          const userRes = await db.collection('users').where({
            openid: db.command.in(batchOpenids)
          }).get();

          // 合并到总结果中
          users.push(...userRes.data);
        }
      }

      // 第三步：关联数据，生成选择器选项
      const coachOptions = coachList.map(item => {
        // 查找对应的用户信息
        const user = users.find(u => u.openid === item.openid);

        /**
         * 选择器选项格式
         *
         * 显示名称优先级：
         * 1. 优先使用用户昵称（更友好）
         * 2. 如果没有昵称，使用openid后4位（标识符）
         *
         * 数据结构：
         * - label: 显示给用户看的文字
         * - value: 实际的值（openid）
         * - name: 备用的显示名称
         */
        return {
          label: user ? user.nickName : item.openid.slice(-4),
          value: item.openid,
          name: user ? user.nickName : item.openid.slice(-4),
          // 添加头像和职位信息，用于头像显示功能
          avatarUrl: user ? user.avatarUrl : '',
          avatar: user ? user.avatarUrl : '',  // 兼容性字段
          title: item.title || item.specialty || '讲师',
          specialty: item.specialty || '',
          // 保存完整信息用于后续使用
          userInfo: user,
          coachInfo: item
        };
      });

      // 更新页面数据
      this.setData({ coachList, coachOptions });
    } catch (error) {
      // 错误处理
      console.error('获取讲师列表失败:', error);
      this.showMessage('讲师列表加载失败', 'error');
    }
  },

  /**
   * fetchTemplateList: 获取模板列表的异步方法
   *
   * 功能说明：
   * 从数据库获取所有可用的课程模板
   * 为模板选择器提供数据源
   *
   * 模板的作用：
   * 1. 快速创建：基于模板快速创建相似的课程
   * 2. 标准化：确保同类课程的格式一致
   * 3. 效率提升：减少重复输入相同的信息
   */
  async fetchTemplateList() {
    const db = wx.cloud.database();

    try {
      // 获取所有课程模板
      const res = await db.collection('coursesTemplate').get();

      // 生成选择器选项
      let options = [];
      if (Array.isArray(res.data)) {
        options = res.data.map(tpl => ({
          /**
           * 模板显示格式
           *
           * 格式：模板名称
           * 例如：基础课
           *
           * 条件显示：
           * 如果有描述则显示，没有则只显示名称
           */
          label: tpl.name + (tpl.description ? `（${tpl.description}）` : ''),
          value: tpl._id
        }));
      }

      /**
       * 选择器数据格式说明
       *
       * templatePickerOptions: [options]
       *
       * 为什么是二维数组：
       * TDesign的选择器组件支持多级选择
       * 即使只有一级，也需要包装成二维数组格式
       * 例如：[[选项1, 选项2, 选项3]]
       */
      this.setData({
        templateList: res.data,           // 原始模板数据
        templatePickerOptions: [options]  // 选择器选项（二维数组）
      });
    } catch (error) {
      // 错误处理
      console.error('获取模板列表失败:', error);
      this.showMessage('模板列表加载失败', 'error');

      // 确保选择器数据格式正确
      // 即使出错也要保证是二维数组，避免组件报错
      this.setData({ templatePickerOptions: [[]] });
    }
  },

  /**
   * onChooseTemplate: 选择模板事件处理
   *
   * 功能说明：
   * 用户点击"选择模板"按钮时触发
   * 检查是否有可用模板，然后显示模板选择器
   *
   * 用户体验：
   * 如果没有模板，给出友好提示而不是显示空的选择器
   */
  onChooseTemplate() {
    // 检查是否有可用模板
    if (this.data.templateList.length === 0) {
      this.showMessage('暂无可用模板', 'warning');
      return;
    }

    // 显示模板选择器
    this.setData({ templatePickerVisible: true });
  },

  /**
   * onTemplatePickerConfirm: 模板选择器确认事件处理
   *
   * 功能说明：
   * 用户在模板选择器中选择模板并确认时触发
   * 将选中的模板数据应用到当前表单
   *
   * @param {Object} e - 事件对象
   *   - e.detail.value: 选择器返回的值数组
   *
   * 数据处理：
   * 选择器返回的是数组格式，需要提取第一个元素作为模板ID
   */
  onTemplatePickerConfirm(e) {
    // 获取选择器返回的值
    const { value } = e.detail;

    // 检查是否有选中的值
    if (value && value[0]) {
      // 提取模板ID（选择器返回数组，取第一个元素）
      const templateId = value[0];

      // 在模板列表中查找对应的模板数据
      const template = this.data.templateList.find(t => t._id === templateId);

      if (template) {
        // 应用模板数据到表单
        this.fillFormData(template);

        // 显示成功提示
        this.showMessage('模板应用成功', 'success');
      }
    }

    // 关闭模板选择器
    this.setData({ templatePickerVisible: false });
  },

  /**
   * onInputChange: 输入框变化事件处理
   *
   * 功能说明：
   * 统一处理所有表单输入框的值变化事件
   * 实现数据双向绑定和实时验证
   *
   * @param {Object} e - 事件对象
   *   - e.currentTarget.dataset.field: 字段名称
   *   - e.detail.value: 输入的新值
   *
   * 设计模式：
   * 使用统一的事件处理函数，通过 data-field 属性区分不同字段
   * 这种方式比为每个字段单独写处理函数更简洁
   *
   * 实时验证：
   * 根据字段类型进行相应的验证，提供即时反馈
   */
  onInputChange(e) {
    // 从事件对象中获取字段名和新值
    const { field } = e.currentTarget.dataset;
    let { value } = e.detail;

    // 对特定字段进行数据类型转换
    if (field === 'capacity') {
      // 容量字段：转换为数字类型
      value = parseInt(value) || 0;
    }

    // 更新对应字段的值
    // 使用模板字符串动态构建属性路径
    this.setData({ [`course.${field}`]: value });

    // 清除该字段的错误提示
    // 用户开始输入时，清除之前的错误状态
    this.clearFieldError(field);

    /**
     * 字段特定的实时验证
     *
     * 不同字段有不同的验证规则和处理逻辑：
     * - capacity: 数字范围验证
     * - name: 必填验证
     * - venue: 必填验证
     * - activityDetail: 长度验证
     * - duration: 数字验证 + 结束时间更新
     */
    if (field === 'capacity') {
      // 容量字段：验证数字范围
      this.validateCapacity(value);
    } else if (field === 'name') {
      // 课程名称：必填验证
      this.validateField('name', value);
    } else if (field === 'venue') {
      // 上课地点：必填验证
      this.validateField('venue', value);
    } else if (field === 'activityDetail') {
      // 课程详情：长度验证
      this.validateField('activityDetail', value);
    } else if (field === 'duration') {
      // 课程时长：数字验证 + 联动更新结束时间
      this.validateDuration(value);
      this.updateEndTime();  // 时长改变时自动更新结束时间
    }
  },

  /**
   * validateCapacity: 验证课程容量的方法
   *
   * 功能说明：
   * 验证课程容量是否为有效的正整数
   *
   * @param {string} value - 输入的容量值
   * @returns {boolean} - 验证是否通过
   *
   * 验证规则：
   * 1. 必须是数字
   * 2. 必须是正整数
   * 3. 范围在1-999之间
   *
   * 业务考虑：
   * - 最小值1：至少要有1个名额
   * - 最大值999：避免过大的数字，符合实际场地限制
   */
  validateCapacity(value) {
    // 将字符串转换为整数
    const num = parseInt(value);

    // 验证数字有效性和范围
    if (isNaN(num) || num < 1 || num > 999) {
      // 验证失败：设置错误信息
      this.setData({ 'formErrors.capacity': '请输入1-999之间的正整数' });
      return false;
    }

    // 验证通过：清除错误信息
    if (this.data.formErrors.capacity) {
      this.setData({ 'formErrors.capacity': '' });
    }

    return true;
  },

  validateDuration(value) {
    const num = parseInt(value);
    if (isNaN(num) || num < 1 || num > 1440) { // 最大24小时
      this.setData({ 'formErrors.duration': '请输入1-1440之间的正整数' });
      return false;
    }
    // 验证通过，清除错误
    if (this.data.formErrors.duration) {
      this.setData({ 'formErrors.duration': '' });
    }
    return true;
  },

  // 验证单个字段
  validateField(field, value) {
    const { course, mode } = this.data;
    
    switch (field) {
      case 'name':
        if (!value || value.trim() === '') {
          this.setData({ 'formErrors.name': '请输入活动名称' });
          return false;
        }
        break;
      case 'venue':
        if (mode === 'course' && (!value || value.trim() === '')) {
          this.setData({ 'formErrors.venue': '请输入上课地点' });
          return false;
        }
        break;
      case 'activityDetail':
        if (!value || value.trim() === '') {
          this.setData({ 'formErrors.activityDetail': '请输入活动详情' });
          return false;
        }
        break;
    }
    
    // 验证通过，清除错误
    if (this.data.formErrors[field]) {
      this.setData({ [`formErrors.${field}`]: '' });
    }
    return true;
  },

  showDateTimePicker(e) {
    const { mode } = e.currentTarget.dataset;
    if (mode === 'startTime') {
      this.setData({ startTimeVisible: true });
      this.clearFieldError('startTime');
    }
  },

  hideStartTimePicker() {
    this.setData({ startTimeVisible: false });
  },

  onStartTimeConfirm(e) {
    const { value } = e.detail;
    const startTime = new Date(value);
    const duration = this.data.course.duration || 60;
    const endTime = new Date(startTime.getTime() + duration * 60 * 1000);

    this.setData({
      startTime: value,
      startTimeText: this.formatDateTime(value),
      'course.startTime': startTime,
      'course.endTime': endTime,
      endTimeText: this.formatDateTime(endTime.getTime())
    });
    this.clearFieldError('startTime');
    this.hideStartTimePicker();
  },

  onStartTimeColumnChange(e) {},

  updateEndTime() {
    const { course } = this.data;
    if (course.startTime && course.duration) {
      const startTime = course.startTime instanceof Date ? course.startTime : new Date(course.startTime);
      const duration = parseInt(course.duration) || 60;
      const endTime = new Date(startTime.getTime() + duration * 60 * 1000);

      this.setData({
        'course.endTime': endTime,
        endTimeText: this.formatDateTime(endTime.getTime())
      });
    }
  },

  setDuration(e) {
    const { duration } = e.currentTarget.dataset;
    const durationValue = parseInt(duration);
    
    this.setData({
      'course.duration': durationValue
    });
    
    // 清除错误提示
    this.clearFieldError('duration');
    
    // 验证时长
    this.validateDuration(durationValue);
    
    // 更新结束时间
    this.updateEndTime();
  },

  formatDateTime(val) {
    if (!val) return '';
    const date = new Date(val);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    // 获取周几
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekday = weekdays[date.getDay()];

    return `${year}-${month}-${day} ${weekday} ${hours}:${minutes}`;
  },

  validateForm() {
    const errors = {};
    const { course, mode } = this.data;
    
    // 基础信息验证（课程和模板都需要）
    if (!course.name || course.name.trim() === '') {
      errors.name = '请输入活动名称';
    }
    if (!course.activityDetail || course.activityDetail.trim() === '') {
      errors.activityDetail = '请输入活动详情';
    }
    
    // 课程安排验证（仅课程模式需要）
    if (mode === 'course') {
      if (!course.startTime) {
        errors.startTime = '请选择开始时间';
      }
      if (!course.duration || !this.validateDuration(course.duration)) {
        errors.duration = '请输入有效的活动时长';
      }
      if (!course.venue || course.venue.trim() === '') {
        errors.venue = '请输入上课地点';
      }
      if (!course.capacity || !this.validateCapacity(course.capacity)) {
        errors.capacity = '请输入有效的人数上限';
      }
    }
    
    this.setData({ formErrors: errors });
    return Object.keys(errors).length === 0;
  },

  validateFormForTemplate() {
    const errors = {};
    const { course } = this.data;
    
    // 模板只需要基础信息验证
    if (!course.name || course.name.trim() === '') {
      errors.name = '请输入活动名称';
    }
    if (!course.activityDetail || course.activityDetail.trim() === '') {
      errors.activityDetail = '请输入活动详情';
    }
    
    this.setData({ formErrors: errors });
    return Object.keys(errors).length === 0;
  },

  validateFormWithDetails() {
    const errors = {};
    const { course, mode } = this.data;
    
    // 基础信息验证（课程和模板都需要）
    if (!course.name || course.name.trim() === '') {
      errors.name = '请输入活动名称';
    }
    if (!course.activityDetail || course.activityDetail.trim() === '') {
      errors.activityDetail = '请输入活动详情';
    }
    
    // 课程安排验证（仅课程模式需要）
    if (mode === 'course') {
      if (!course.startTime) {
        errors.startTime = '请选择开始时间';
      }
      if (!course.duration || !this.validateDuration(course.duration)) {
        errors.duration = '请输入有效的活动时长';
      }
      if (!course.venue || course.venue.trim() === '') {
        errors.venue = '请输入上课地点';
      }
      if (!course.capacity || !this.validateCapacity(course.capacity)) {
        errors.capacity = '请输入有效的人数上限';
      }
    }
    
    this.setData({ formErrors: errors });
    return {
      isValid: Object.keys(errors).length === 0,
      errors: errors
    };
  },

  /**
   * showValidationErrors: 显示表单验证错误的方法
   *
   * 功能说明：
   * 将表单验证错误以用户友好的方式展示给用户
   * 包括错误信息的格式化和自动滚动到错误位置
   *
   * @param {Object} errors - 验证错误对象，格式：{ fieldName: errorMessage }
   *
   * 用户体验设计：
   * 1. 友好的字段名称映射
   * 2. 结构化的错误信息展示
   * 3. 根据页面模式调整提示文案
   * 4. 自动滚动到第一个错误字段
   */
  showValidationErrors(errors) {
    const errorMessages = [];

    /**
     * 字段名称映射表
     *
     * 将技术字段名转换为用户友好的显示名称
     * 提升错误提示的可读性
     */
    const fieldNames = {
      name: '活动名称',
      activityDetail: '活动详情',
      startTime: '开始时间',
      duration: '活动时长',
      venue: '上课地点',
      capacity: '人数上限'
    };

    // 收集所有错误信息
    Object.keys(errors).forEach(field => {
      const fieldName = fieldNames[field] || field;  // 使用映射名称或原字段名
      errorMessages.push(`• ${fieldName}：${errors[field]}`);
    });

    // 根据模式显示不同的标题和说明
    const { mode } = this.data;
    const title = mode === 'template' ? '请完善模板信息' : '请完善活动信息';
    const modeText = mode === 'template' ? '模板' : '活动';

    // 显示错误提示弹窗
    const errorText = `以下${modeText}信息需要完善：\n\n${errorMessages.join('\n')}`;
    wx.showModal({
      title: title,
      content: errorText,
      showCancel: false,        // 不显示取消按钮
      confirmText: '我知道了'   // 自定义确认按钮文字
    });

    // 滚动到第一个错误字段
    this.scrollToFirstError(errors);
  },

  /**
   * scrollToFirstError: 滚动到第一个错误字段的方法
   *
   * 功能说明：
   * 自动滚动页面到第一个有错误的表单字段
   * 帮助用户快速定位需要修改的内容
   *
   * @param {Object} errors - 验证错误对象
   *
   * 技术实现：
   * 1. 使用wx.createSelectorQuery()查询DOM元素位置
   * 2. 计算滚动位置（考虑顶部偏移）
   * 3. 使用wx.pageScrollTo()平滑滚动
   */
  scrollToFirstError(errors) {
    const errorFields = Object.keys(errors);

    if (errorFields.length > 0) {
      const firstErrorField = errorFields[0];

      // 创建选择器查询
      const query = wx.createSelectorQuery();

      // 查询错误字段的位置信息
      query.select(`[data-field="${firstErrorField}"]`).boundingClientRect();

      // 查询页面滚动信息
      query.selectViewport().scrollOffset();

      // 执行查询
      query.exec((res) => {
        if (res[0]) {
          // 计算滚动位置：当前滚动位置 + 元素顶部位置 - 顶部偏移量
          wx.pageScrollTo({
            scrollTop: res[1].scrollTop + res[0].top - 100,  // 100px顶部偏移
            duration: 300  // 300ms动画时长
          });
        }
      });
    }
  },

  /**
   * clearFieldError: 清除指定字段错误的方法
   *
   * 功能说明：
   * 清除特定字段的验证错误提示
   * 通常在用户开始修改字段时调用
   *
   * @param {string} field - 字段名称
   *
   * 用户体验：
   * 用户开始输入时立即清除错误提示，提供即时反馈
   */
  clearFieldError(field) {
    if (this.data.formErrors[field]) {
      this.setData({ [`formErrors.${field}`]: '' });
    }
  },

  /**
   * clearAllErrors: 清除所有错误的方法
   *
   * 功能说明：
   * 清除表单中的所有验证错误提示
   * 通常在表单重置或提交成功后调用
   */
  clearAllErrors() {
    this.setData({ formErrors: {} });
  },

  validateFormForTemplateWithDetails() {
    const errors = {};
    const { course } = this.data;
    
    // 模板只需要基础信息验证
    if (!course.name || course.name.trim() === '') {
      errors.name = '请输入活动名称';
    }
    if (!course.activityDetail || course.activityDetail.trim() === '') {
      errors.activityDetail = '请输入活动详情';
    }
    
    this.setData({ formErrors: errors });
    return {
      isValid: Object.keys(errors).length === 0,
      errors: errors
    };
  },

  prepareSubmitData() {
    const { course, selectedCoaches } = this.data;

    /**
     * 重要说明：coachName字段的使用原则
     *
     * 1. coachName字段仅供开发人员维护使用，用于数据库调试和维护
     * 2. 任何业务逻辑中需要获取教练名字时，都必须通过openid从users表查询
     * 3. 严禁直接读取coachName字段用于业务逻辑，这极容易出错
     * 4. 这里设置coachName是为了保持数据一致性，便于开发维护
     */
    const coachName = selectedCoaches.map(coach => coach.nickName || coach.name || '未知教练');
    const toTime = v => v instanceof Date ? v.toISOString() : v;
    const submitData = {
      name: course.name || '',
      coach: course.coach || [],
      startTime: toTime(course.startTime) || '',
      endTime: toTime(course.endTime) || '',
      duration: course.duration || 60,
      venue: course.venue || '',
      capacity: parseInt(course.capacity) || 0,
      activityDetail: { description: course.activityDetail || '' }, // 兼容云函数
      status: course.status || 'offline',
      images: course.images || [], // 课程图片
      coachName: coachName,
      createTime: toTime(new Date()),
      updateTime: toTime(new Date())
    };
    if (this.data.isEdit) {
      const id = this.data.mode === 'template' ? this.data.templateId : this.data.courseId;
      submitData._id = id;
    }
    if (submitData.capacity) {
      submitData.capacity = parseInt(submitData.capacity);
    }
    if (submitData.duration) {
      submitData.duration = parseInt(submitData.duration);
    }
    return submitData;
  },

  /**
   * onSubmit: 表单提交事件处理
   *
   * 功能说明：
   * 处理用户点击保存按钮的操作
   * 根据页面模式（课程/模板）和状态（新建/编辑）执行相应的保存逻辑
   *
   * 提交流程：
   * 1. 防重复提交检查
   * 2. 表单验证
   * 3. 数据准备
   * 4. 根据模式选择保存方式
   * 5. 成功反馈和页面跳转
   *
   * 保存方式：
   * - 模板：直接操作数据库
   * - 课程：通过云函数处理（涉及复杂的业务逻辑）
   */
  async onSubmit() {
    // 防止重复提交
    if (this.data.isSubmitting) return;

    /**
     * 表单验证
     *
     * 验证失败时：
     * 1. 显示详细的错误信息
     * 2. 自动滚动到第一个错误字段
     * 3. 终止提交流程
     */
    const validationResult = this.validateFormWithDetails();
    if (!validationResult.isValid) {
      this.showValidationErrors(validationResult.errors);
      return;
    }

    // 设置提交状态，显示loading并防止重复提交
    this.setData({ isSubmitting: true });

    try {
      // 准备提交数据
      const submitData = this.prepareSubmitData();
      console.log('准备提交的数据:', submitData);

      /**
       * 根据页面模式选择保存方式
       *
       * 模板模式 vs 课程模式的区别：
       * - 模板：数据结构简单，直接操作数据库即可
       * - 课程：涉及复杂业务逻辑（预约管理、通知发送等），需要通过云函数处理
       */
      if (this.data.mode === 'template') {
        /**
         * 模板模式：直接数据库操作
         *
         * 为什么不用云函数：
         * 1. 模板操作相对简单，不涉及复杂业务逻辑
         * 2. 减少云函数调用，提升性能
         * 3. 降低系统复杂度
         */
        const db = wx.cloud.database();

        if (this.data.isEdit) {
          // 编辑模板
          const { _id, ...updateData } = submitData;

          // 过滤空值，避免覆盖现有数据
          Object.keys(updateData).forEach(key => {
            if (updateData[key] === '' || updateData[key] === undefined || updateData[key] === null) {
              delete updateData[key];
            }
          });

          console.log('[模板编辑] onSubmit update _id:', _id, 'updateData:', updateData);

          // 更新模板
          const updateRes = await db.collection('coursesTemplate').doc(_id).update({
            data: updateData
          });

          console.log('[模板编辑] onSubmit update 返回:', JSON.stringify(updateRes));
          this.showMessage('模板更新成功', 'success');
        } else {
          // 新建模板
          const { _id, ...addData } = submitData;

          // 创建模板
          const result = await db.collection('coursesTemplate').add({
            data: addData
          });

          console.log('模板创建结果:', result);
          this.showMessage('模板创建成功', 'success');
        }
      } else {
        /**
         * 课程模式：通过云函数处理
         *
         * 为什么使用云函数：
         * 1. 课程操作涉及复杂的业务逻辑
         * 2. 需要权限验证和数据校验
         * 3. 可能需要发送通知给已预约用户
         * 4. 统一的错误处理和日志记录
         */
        const action = this.data.isEdit ? 'updateCourse' : 'addCourse';
        console.log('调用云函数:', action, submitData);

        // 调用云函数
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',    // 云函数名称
          data: {
            action: action,           // 操作类型
            data: submitData          // 提交数据
          }
        });

        console.log('课程操作结果:', result);

        /**
         * 云函数结果处理
         *
         * 云函数返回格式：
         * {
         *   result: {
         *     success: boolean,
         *     message: string,
         *     data: any
         *   },
         *   errMsg: string
         * }
         */
        if (result.result && result.result.success) {
          // 成功：显示成功消息
          const message = this.data.isEdit ? '课程更新成功' : '课程创建成功';
          this.showMessage(message, 'success');
        } else {
          // 失败：提取错误信息并抛出异常
          const errorMessage = result.result?.message || result.errMsg || '操作失败';
          console.error('云函数返回错误:', result.result);
          throw new Error(errorMessage);
        }
      }

      /**
       * 成功后的处理
       *
       * 延迟返回：
       * 让用户看到成功提示后再返回上一页
       * 提升用户体验
       */
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      /**
       * 错误处理
       *
       * 错误类型：
       * 1. 网络错误：网络连接问题
       * 2. 云函数错误：业务逻辑错误
       * 3. 数据库错误：数据操作失败
       * 4. 验证错误：数据格式或权限问题
       */
      console.error('保存失败:', error);
      console.error('错误详情:', error.message, error.errMsg);

      // 显示用户友好的错误信息
      this.showMessage(error.message || '保存失败，请重试', 'error');
    } finally {
      /**
       * 清理工作
       *
       * 无论成功还是失败，都要重置提交状态
       * 确保用户可以重新尝试操作
       */
      this.setData({ isSubmitting: false });
    }
  },

  /**
   * validateTemplateForm: 模板表单验证方法
   *
   * 功能说明：
   * 专门用于模板保存的简化验证
   * 模板只需要活动名称，其他字段可以为空
   *
   * 设计考虑：
   * 模板是为了快速创建课程，不需要完整信息
   * 用户可以先保存基础模板，后续使用时再完善
   */
  validateTemplateForm() {
    const { course } = this.data;
    const formErrors = {};

    // 只验证活动名称
    if (!course.name || !course.name.trim()) {
      formErrors.name = '活动名称不能为空';
    }

    // 更新错误状态
    this.setData({ formErrors });

    // 返回验证结果
    return Object.keys(formErrors).length === 0;
  },

  /**
   * onSaveAsTemplate: 保存为模板事件处理
   *
   * 功能说明：
   * 将当前表单内容保存为课程模板
   * 使用简化的验证规则
   *
   * 使用场景：
   * 用户在编辑课程时，发现这个课程可以作为模板重复使用
   * 点击"保存为模板"按钮快速创建模板
   */
  onSaveAsTemplate() {
    // 使用简化的模板验证
    if (!this.validateTemplateForm()) {
      return;
    }

    // 其他字段可以为空，直接保存模板
    this.saveTemplate();
  },

  /**
   * onCancel: 取消编辑事件处理
   *
   * 功能说明：
   * 用户点击取消按钮时的确认处理
   * 防止用户误操作丢失编辑内容
   *
   * 用户体验：
   * 1. 显示确认弹窗，说明后果
   * 2. 用户确认后才真正取消
   * 3. 保护用户的编辑成果
   */
  onCancel() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消编辑吗？未保存的内容将丢失。',
      success: (res) => {
        if (res.confirm) {
          // 用户确认取消，返回上一页
          wx.navigateBack();
        }
        // 用户点击取消，继续编辑，不做任何操作
      }
    });
  },

  /**
   * updateCoachText: 更新讲师显示文本和头像显示的方法
   *
   * 功能说明：
   * 根据选中的讲师数量和信息，更新讲师选择器的显示文本和头像显示
   * 提供用户友好的显示格式和视觉反馈
   *
   * 显示逻辑：
   * - 0个讲师：显示"请选择讲师"，清空头像
   * - 1个讲师：显示讲师姓名，显示头像
   * - 多个讲师：显示"讲师1、讲师2、讲师3"格式，显示第一个讲师头像
   */
  updateCoachText() {
    const { selectedCoaches } = this.data;

    if (selectedCoaches.length === 0) {
      // 没有选择讲师
      this.setData({
        coachText: '请选择讲师',
        selectedCoach: null  // 清空头像显示
      });
    } else if (selectedCoaches.length === 1) {
      // 选择了一个讲师
      const coach = selectedCoaches[0];
      this.setData({
        coachText: coach.name,
        selectedCoach: {
          name: coach.name || coach.nickName || '未知讲师',
          avatar: coach.avatarUrl || coach.avatar || '',
          title: coach.title || coach.specialty || '讲师',
          openid: coach.openid
        }
      });
      this.clearFieldError('coach');  // 清除讲师字段的错误提示
    } else {
      // 选择了多个讲师，显示第一个讲师的头像
      const names = selectedCoaches.map(coach => coach.name).join('、');
      const firstCoach = selectedCoaches[0];
      this.setData({
        coachText: names,
        selectedCoach: {
          name: firstCoach.name || firstCoach.nickName || '未知讲师',
          avatar: firstCoach.avatarUrl || firstCoach.avatar || '',
          title: firstCoach.title || firstCoach.specialty || '讲师',
          openid: firstCoach.openid
        }
      });
      this.clearFieldError('coach');
    }
  },

  /**
   * showMessage: 显示消息提示的工具方法
   *
   * 功能说明：
   * 统一的消息提示方法，封装Toast组件的调用
   *
   * @param {string} content - 提示内容
   * @param {string} type - 提示类型：'success', 'error', 'info'
   *
   * 设计优势：
   * 1. 统一接口：所有消息提示使用相同的方法
   * 2. 类型映射：将业务类型映射为组件主题
   * 3. 易于维护：如果要更换提示组件，只需修改这一个方法
   */
  showMessage(content, type = 'info') {
    // 将业务类型映射为Toast组件的主题
    const theme = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';

    // 调用Toast工具函数
    showToast(this, { message: content, theme });
  },

  /**
   * onCoachPicker: 打开讲师选择器事件处理
   *
   * 功能说明：
   * 用户点击讲师选择按钮时触发
   * 显示讲师选择器弹窗
   */
  onCoachPicker() {
    this.setData({ coachVisible: true });
  },

  /**
   * onCoachPickerConfirm: 讲师选择器确认事件处理
   *
   * 功能说明：
   * 用户在讲师选择器中选择讲师并确认时触发
   * 处理选择结果并更新相关数据
   *
   * @param {Object} e - 事件对象
   *   - e.detail.value: 选中的讲师openid数组
   *
   * 数据处理：
   * 1. 获取选中的openid数组
   * 2. 根据openid查找对应的讲师信息
   * 3. 更新多个相关的数据字段
   * 4. 更新显示文本和清除错误提示
   */
  onCoachPickerConfirm(e) {
    // 获取选中的讲师openid数组
    const value = e.detail.value;
    const selectedCoachOpenids = value;

    // 根据openid查找对应的讲师信息
    const selectedCoaches = selectedCoachOpenids.map(openid => {
      // 在讲师选项中查找对应的讲师信息
      const option = this.data.coachOptions.find(opt => opt.value === openid);

      return {
        openid,
        name: option ? option.name : openid.slice(-4),  // 备用显示名称
        // 添加头像和职位信息，用于头像显示功能
        avatarUrl: option ? option.avatarUrl : '',
        avatar: option ? option.avatar : '',
        title: option ? option.title : '讲师',
        specialty: option ? option.specialty : '',
        // 保存完整信息
        userInfo: option ? option.userInfo : null,
        coachInfo: option ? option.coachInfo : null
      };
    });

    // 批量更新相关数据
    this.setData({
      coachValue: value,                        // 选择器绑定值
      'course.coach': selectedCoachOpenids,     // 课程数据中的讲师字段
      selectedCoaches,                          // 选中的讲师详细信息
      coachVisible: false                       // 关闭选择器
    });

    // 更新显示文本
    this.updateCoachText();

    // 清除讲师字段的错误提示
    this.clearFieldError('coach');
  },

  /**
   * saveTemplate: 保存模板的异步方法
   *
   * 功能说明：
   * 将当前表单数据保存为课程模板
   * 支持新建模板和编辑现有模板两种模式
   *
   * 模板的作用：
   * 1. 快速创建：基于模板快速创建相似的课程
   * 2. 标准化：确保同类课程的格式一致
   * 3. 效率提升：减少重复输入相同的信息
   *
   * 数据处理：
   * 编辑模式下会过滤空值，避免覆盖现有的有效数据
   */
  saveTemplate: async function() {
    console.log('[模板编辑] saveTemplate called');

    // 防止重复提交
    if (this.data.isSubmitting) return;
    this.setData({ isSubmitting: true });

    try {
      const db = wx.cloud.database();

      // 准备提交数据
      const submitData = this.prepareSubmitData();

      if (this.data.isEdit) {
        /**
         * 编辑模板模式
         *
         * 数据处理策略：
         * 1. 移除_id字段（不能更新）
         * 2. 过滤空值，避免覆盖现有的有效数据
         * 3. 只更新有意义的字段
         */
        const { _id, ...updateData } = submitData;

        // 移除空字符串和undefined/null字段
        Object.keys(updateData).forEach(key => {
          if (updateData[key] === '' || updateData[key] === undefined || updateData[key] === null) {
            delete updateData[key];
          }
        });

        console.log('[模板编辑] update _id:', _id, 'updateData:', updateData);

        // 更新模板数据
        const updateRes = await db.collection('coursesTemplate').doc(_id).update({
          data: updateData
        });

        console.log('[模板编辑] update 返回:', JSON.stringify(updateRes));
        this.showMessage('模板更新成功', 'success');
      } else {
        /**
         * 新建模板模式
         *
         * 数据处理：
         * 移除_id字段，让数据库自动生成新的ID
         */
        const { _id, ...addData } = submitData;

        // 创建新模板
        const result = await db.collection('coursesTemplate').add({
          data: addData
        });

        console.log('模板创建结果:', result);
        this.showMessage('模板创建成功', 'success');
      }

      // 延迟返回上一页，让用户看到成功提示
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      // 错误处理
      console.error('保存模板失败:', error);
      this.showMessage(error.message || '保存模板失败，请重试', 'error');
    } finally {
      // 无论成功失败，都要重置提交状态
      this.setData({ isSubmitting: false });
    }
  },

  /**
   * updateCoachDisplayFromData: 根据课程数据更新讲师显示状态
   *
   * @param {Array} coachOpenids - 课程中的讲师openid数组
   */
  /**
   * updateCoachDisplayFromData: 根据课程数据更新讲师显示状态
   *
   * @param {Array} coachOpenids - 课程中的讲师openid数组
   */
  async updateCoachDisplayFromData(coachOpenids) {
    console.log('updateCoachDisplayFromData 开始处理讲师数据:', coachOpenids);

    if (!coachOpenids || coachOpenids.length === 0) {
      console.log('讲师数据为空，清空选择');
      this.setData({
        coachValue: [],
        selectedCoaches: []
      });
      return;
    }

    // 确保coachOptions已经加载，如果没有则重新加载
    if (!this.data.coachOptions || this.data.coachOptions.length === 0) {
      console.log('coachOptions未加载，重新获取讲师列表');
      await this.fetchCoachList();
    }

    // 从已加载的讲师列表中找到对应的讲师信息
    const allCoaches = this.data.coachList || [];
    const allCoachOptions = this.data.coachOptions || [];
    const selectedCoaches = [];
    const coachValue = [];

    // 如果仍然没有讲师数据，尝试直接查询数据库
    if (allCoaches.length === 0 || allCoachOptions.length === 0) {
      console.log('讲师数据为空，直接查询数据库获取讲师信息');
      await this.loadCoachInfoByOpenids(coachOpenids);
      return;
    }

    coachOpenids.forEach(openid => {
      // 从coachOptions中查找对应的教练信息（已经包含了从users表查询的昵称）
      const coachOption = allCoachOptions.find(option => option.value === openid);
      const coachInfo = allCoaches.find(c => c.openid === openid);

      if (coachOption && coachInfo) {
        // 构建完整的教练信息对象，包含用户信息和专业信息
        const completeCoachInfo = {
          ...coachInfo,
          nickName: coachOption.name, // 从users表查询的昵称
          name: coachOption.name      // 保持兼容性
        };

        selectedCoaches.push(completeCoachInfo);
        coachValue.push({
          label: coachOption.label,
          value: coachOption.value
        });
      } else {
        console.warn(`未找到讲师信息: ${openid}`);
      }
    });

    this.setData({
      coachValue: coachValue,
      selectedCoaches: selectedCoaches
    });

    // 更新显示文本
    this.updateCoachText();
  },

  /**
   * loadCoachInfoByOpenids: 直接通过openid查询讲师信息
   *
   * @param {Array} coachOpenids - 讲师openid数组
   */
  async loadCoachInfoByOpenids(coachOpenids) {
    if (!coachOpenids || coachOpenids.length === 0) return;

    try {
      const db = wx.cloud.database();

      // 查询讲师专业信息
      const coachInfoRes = await db.collection('coachInfo')
        .where({
          openid: db.command.in(coachOpenids)
        })
        .get();

      // 查询用户基础信息
      const userRes = await db.collection('users')
        .where({
          openid: db.command.in(coachOpenids)
        })
        .get();

      const selectedCoaches = [];
      const coachValue = [];

      coachOpenids.forEach(openid => {
        const coachInfo = coachInfoRes.data.find(c => c.openid === openid) || {};
        const user = userRes.data.find(u => u.openid === openid) || {};

        const displayName = user.nickName || openid.slice(-4);

        const completeCoachInfo = {
          ...coachInfo,
          openid: openid,
          nickName: displayName,
          name: displayName
        };

        selectedCoaches.push(completeCoachInfo);
        coachValue.push({
          label: displayName,
          value: openid
        });
      });

      this.setData({
        coachValue: coachValue,
        selectedCoaches: selectedCoaches
      });

      // 更新显示文本
      this.updateCoachText();

    } catch (error) {
      console.error('直接查询讲师信息失败:', error);
    }
  },

  /**
   * 图片管理相关方法
   */

  /**
   * loadCourseImages: 加载课程图片的临时访问URL
   *
   * @param {Array} imageFileIds - 图片的fileID数组
   */
  async loadCourseImages(imageFileIds) {
    if (!imageFileIds || imageFileIds.length === 0) {
      this.setData({ courseImages: [] });
      return;
    }

    try {
      const result = await wx.cloud.getTempFileURL({
        fileList: imageFileIds
      });

      const courseImages = result.fileList.map((file, index) => ({
        fileID: file.fileID,
        tempFileURL: file.tempFileURL,
        index: index
      }));

      this.setData({ courseImages });
    } catch (error) {
      console.error('获取图片临时URL失败:', error);
      this.showMessage('图片加载失败', 'error');
    }
  },

  /**
   * onChooseImage: 选择图片事件处理
   */
  onChooseImage() {
    wx.chooseImage({
      count: 9 - this.data.courseImages.length, // 最多9张图片
      sizeType: ['compressed'], // 使用压缩图片
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.uploadImages(res.tempFilePaths);
      }
    });
  },

  /**
   * uploadImages: 上传图片到云存储
   *
   * @param {Array} tempFilePaths - 临时文件路径数组
   */
  async uploadImages(tempFilePaths) {
    if (this.data.isUploadingImage) return;

    this.setData({ isUploadingImage: true });

    try {
      const uploadTasks = tempFilePaths.map(filePath => {
        const timestamp = Date.now();
        const randomStr = Math.random().toString(36).substr(2, 9);
        const cloudPath = `course-images/${timestamp}_${randomStr}.jpg`;

        return wx.cloud.uploadFile({
          cloudPath,
          filePath
        });
      });

      const uploadResults = await Promise.all(uploadTasks);
      const newFileIds = uploadResults.map(result => result.fileID);

      // 更新课程数据中的图片数组
      const updatedImages = [...this.data.course.images, ...newFileIds];
      this.setData({ 'course.images': updatedImages });

      // 重新加载图片显示
      await this.loadCourseImages(updatedImages);

      this.showMessage('图片上传成功', 'success');
    } catch (error) {
      console.error('图片上传失败:', error);
      this.showMessage('图片上传失败', 'error');
    } finally {
      this.setData({ isUploadingImage: false });
    }
  },

  /**
   * onDeleteImage: 删除图片事件处理
   *
   * @param {Object} e - 事件对象
   */
  onDeleteImage(e) {
    const { index } = e.currentTarget.dataset;
    const imageToDelete = this.data.courseImages[index];

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      confirmText: '删除',
      confirmColor: '#E34D59',
      success: (res) => {
        if (res.confirm) {
          this.deleteImage(index, imageToDelete.fileID);
        }
      }
    });
  },

  /**
   * deleteImage: 删除图片
   *
   * @param {number} index - 图片索引
   * @param {string} fileID - 图片的fileID
   */
  async deleteImage(index, fileID) {
    try {
      // 从云存储删除文件
      await wx.cloud.deleteFile({
        fileList: [fileID]
      });

      // 从课程数据中移除图片
      const updatedImages = this.data.course.images.filter(id => id !== fileID);
      this.setData({ 'course.images': updatedImages });

      // 重新加载图片显示
      await this.loadCourseImages(updatedImages);

      this.showMessage('图片删除成功', 'success');
    } catch (error) {
      console.error('图片删除失败:', error);
      this.showMessage('图片删除失败', 'error');
    }
  },

  /**
   * onPreviewImage: 预览图片事件处理
   *
   * @param {Object} e - 事件对象
   */
  onPreviewImage(e) {
    const { index } = e.currentTarget.dataset;
    const urls = this.data.courseImages.map(img => img.tempFileURL);

    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  }
});

/**
 * 文件总结：course-edit.js
 *
 * 这个文件实现了一个功能完整的课程编辑页面，主要特点：
 *
 * 1. 多模式支持：
 *    - 新建课程：创建全新的课程
 *    - 编辑课程：修改现有课程信息
 *    - 新建模板：创建课程模板
 *    - 编辑模板：修改现有模板
 *    - 基于模板创建：使用模板快速创建课程
 *
 * 2. 复杂表单管理：
 *    - 多种输入类型：文本、数字、时间、选择器、文本域
 *    - 实时验证：输入时立即验证并提示错误
 *    - 数据联动：时长改变时自动更新结束时间
 *    - 状态管理：加载、提交、错误等状态的完整处理
 *
 * 3. 数据处理：
 *    - 格式转换：时间格式、数字格式的转换
 *    - 兼容性处理：历史数据格式的兼容
 *    - 验证机制：完整的表单验证和错误提示
 *    - 关联查询：讲师信息的多表关联
 *
 * 4. 用户体验：
 *    - 智能默认值：合理的时间和数值默认设置
 *    - 即时反馈：实时验证和错误提示
 *    - 防误操作：重复提交保护、取消确认
 *    - 友好提示：成功、失败、警告等不同类型的提示
 *
 * 5. 技术特点：
 *    - 异步处理：完整的async/await异步操作
 *    - 错误处理：完善的try-catch错误捕获
 *    - 性能优化：批量查询、防抖处理
 *    - 代码复用：统一的事件处理和验证方法
 *
 * 与您熟悉的技术对比：
 * - 表单处理：类似于WinForms的数据绑定和验证
 * - 异步操作：类似于C#的async/await模式
 * - 数据验证：类似于ASP.NET的ModelState验证
 * - 状态管理：类似于MVVM模式的ViewModel
 * - 错误处理：类似于.NET的异常处理机制
 */