// bookingUtils.js
// 统一的预约和取消预约逻辑工具

import { showToast, showLoading, hideToast } from './toast.js';
import { cancelBooking as cancelBookingApi } from './database.js';
import { getSystemSettings } from './systemSettings.js';

/**
 * 检查用户登录状态
 * @param {Object} page 页面实例
 * @returns {Object|null} 用户信息或null
 */
export const checkUserLogin = (page) => {
  const app = getApp();
  if (!app.isLoggedIn()) {
    showToast(page, { message: '请先登录', theme: 'warning' });
    return null;
  }
  const userInfo = app.getUserInfo();
  if (!userInfo || !userInfo.openid) {
    showToast(page, { message: '用户信息无效，请重新登录', theme: 'error' });
    return null;
  }
  return userInfo;
};

/**
 * 检查课程是否可预约
 * @param {Object} course 课程信息
 * @param {Object} page 页面实例
 * @returns {boolean} 是否可预约
 */
export const checkCourseAvailability = (course, page) => {
  if (course.ended) {
    showToast(page, { message: '活动已结束，无法预约', theme: 'warning' });
    return false;
  }
  if (course.started) {
    showToast(page, { message: '活动已开始，无法预约', theme: 'warning' });
    return false;
  }
  if (!course.available) {
    showToast(page, { message: '活动已满', theme: 'error' });
    return false;
  }
  return true;
};

/**
 * 检查用户是否为课程讲师
 * @param {Object} course 课程信息
 * @param {Object} userInfo 用户信息
 * @param {Object} page 页面实例
 * @returns {boolean} 如果是讲师返回true（不能预约），否则返回false（可以预约）
 */
export const checkIfUserIsCoach = (course, userInfo, page) => {
  // 检查用户是否有讲师角色
  const userRoles = userInfo.roles || [];
  const isCoach = userRoles.includes('讲师');

  // 如果用户不是讲师，可以预约
  if (!isCoach) {
    return false;
  }

  // 如果用户是讲师，检查是否为该课程的讲师
  const courseCoaches = course.coach || [];
  const userOpenid = userInfo.openid;

  // 检查用户openid是否在课程的讲师列表中
  if (courseCoaches.includes(userOpenid)) {
    showToast(page, { message: '讲师不能预约自己的课程', theme: 'warning' });
    return true; // 是该课程的讲师，不能预约
  }

  return false; // 不是该课程的讲师，可以预约
};

/**
 * 检查取消时间限制
 * @param {Object} course 课程信息
 * @param {number} cancelTimeLimit 取消时间限制（分钟）
 * @param {Object} page 页面实例
 * @returns {boolean} 是否在取消时间限制内
 */
export const checkCancelTimeLimit = (course, cancelTimeLimit, page) => {
  if (cancelTimeLimit === null || cancelTimeLimit === undefined) {
    showToast(page, { message: '系统配置错误，无法取消预约', theme: 'error' });
    return true; // 返回true表示无法取消
  }
  
  if (course.startTime) {
    const now = new Date();
    const courseStartTime = new Date(course.startTime);
    const timeDiffMinutes = (courseStartTime.getTime() - now.getTime()) / (1000 * 60);
    
    if (timeDiffMinutes < cancelTimeLimit) {
      const hours = Math.floor(cancelTimeLimit / 60);
      const minutes = cancelTimeLimit % 60;
      let timeLimitText = '';
      if (hours > 0 && minutes > 0) {
        timeLimitText = `${hours}小时${minutes}分钟`;
      } else if (hours > 0) {
        timeLimitText = `${hours}小时`;
      } else {
        timeLimitText = `${minutes}分钟`;
      }
      
      showToast(page, { message: `课程开始前${timeLimitText}内无法取消预约`, theme: 'warning' });
      return true; // 返回true表示无法取消
    }
  }
  return false; // 返回false表示可以取消
};

/**
 * 格式化时间限制文本
 * @param {number} cancelTimeLimit 取消时间限制（分钟）
 * @returns {string} 格式化的时间文本
 */
export const formatTimeLimitText = (cancelTimeLimit) => {
  const hours = Math.floor(cancelTimeLimit / 60);
  const minutes = cancelTimeLimit % 60;
  let timeLimitText = '';
  if (hours > 0 && minutes > 0) {
    timeLimitText = `${hours}小时${minutes}分钟`;
  } else if (hours > 0) {
    timeLimitText = `${hours}小时`;
  } else {
    timeLimitText = `${minutes}分钟`;
  }
  return timeLimitText;
};

/**
 * 检查预约时间限制并显示警告
 * @param {Object} course 课程信息
 * @param {number} cancelTimeLimit 取消时间限制（分钟）
 * @param {Function} onConfirm 确认回调
 * @param {Object} page 页面实例
 */
export const showBookingWarningWithCountdown = (course, cancelTimeLimit, onConfirm, page) => {
  const timeLimitText = formatTimeLimitText(cancelTimeLimit);
  
  if (page.data.showDialog !== undefined) {
    // 使用页面自定义对话框
    page.setData({
      showDialog: true,
      dialogTitle: '预约确认',
      dialogContent: `课程开始前${timeLimitText}内预约成功后无法取消，确定要预约吗？`,
      dialogConfirmText: '确认预约',
      dialogCancelText: '取消',
      dialogCallback: (action) => {
        if (action === 'confirm') {
          onConfirm();
        }
      }
    });
  } else {
    // 使用系统默认对话框
    wx.showModal({
      title: '预约确认',
      content: `课程开始前${timeLimitText}内预约成功后无法取消，确定要预约吗？`,
      confirmText: '确认预约',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          onConfirm();
        }
      }
    });
  }
};

/**
 * 统一的预约逻辑
 * @param {Object} course 课程信息
 * @param {Object} page 页面实例
 * @param {Function} onSuccess 成功回调
 * @param {Function} onError 错误回调
 */
export const bookCourse = async (course, page, onSuccess, onError) => {
  // 检查登录状态
  const userInfo = checkUserLogin(page);
  if (!userInfo) return;

  // 检查课程可用性
  if (!checkCourseAvailability(course, page)) return;

  // 检查用户是否为该课程的讲师
  if (checkIfUserIsCoach(course, userInfo, page)) return;

  // 检查取消时间限制
  const cancelTimeLimit = page.data.cancelTimeLimit;
  if (cancelTimeLimit !== null && cancelTimeLimit !== undefined && course.startTime) {
    const now = new Date();
    const courseStartTime = new Date(course.startTime);
    const timeDiffMinutes = (courseStartTime.getTime() - now.getTime()) / (1000 * 60);

    if (timeDiffMinutes < cancelTimeLimit) {
      // 在取消时间限制内，显示警告和倒计时确认
      showBookingWarningWithCountdown(course, cancelTimeLimit, () => {
        confirmBooking(course, userInfo, page, onSuccess, onError);
      }, page);
      return;
    }
  }

  // 正常预约流程
  confirmBooking(course, userInfo, page, onSuccess, onError);
};

/**
 * 确认预约
 * @param {Object} course 课程信息
 * @param {Object} userInfo 用户信息
 * @param {Object} page 页面实例
 * @param {Function} onSuccess 成功回调
 * @param {Function} onError 错误回调
 */
const confirmBooking = async (course, userInfo, page, onSuccess, onError) => {
  // 检查考勤卡（如果需要）
  try {
    const db = wx.cloud.database();
    const now = new Date();
    const membershipCards = await db.collection('membershipCard')
      .where({ 
        userId: userInfo.openid, 
        status: '正常', 
        validFrom: db.command.lte(now), 
        validTo: db.command.gte(now), 
        remainingTimes: db.command.gt(0) 
      })
      .orderBy('validTo', 'asc')
      .get();

    if (membershipCards.data.length === 0) {
      if (page.data.showDialog !== undefined) {
        page.setData({
          showDialog: true,
          dialogTitle: '无法预约',
          dialogContent: '您没有有效的考勤卡，无法预约课程。请联系管理员。',
          dialogConfirmText: '我知道了',
          dialogCancelText: '',
          dialogCallback: null
        });
      } else {
        showToast(page, { message: '您没有有效的考勤卡，无法预约课程', theme: 'error' });
      }
      return;
    }

    const selectedCard = membershipCards.data[0];
    let content = `确定要预约"${course.name}"吗？\n\n`;
    content += `将使用考勤卡：${selectedCard.cardNumber}\n`;
    content += `剩余次数：${selectedCard.remainingTimes}次\n`;
    content += `有效期至：${formatDate(selectedCard.validTo)}`;
    if (membershipCards.data.length > 1) {
      content += `\n\n您还有${membershipCards.data.length - 1}张其他有效考勤卡`;
    }

    if (page.data.showDialog !== undefined) {
      page.setData({
        showDialog: true,
        dialogTitle: '确认预约',
        dialogContent: content,
        dialogConfirmText: '确认',
        dialogCancelText: '取消',
        dialogCallback: async (action) => {
          if (action === 'confirm') {
            await performBooking(course, userInfo, page, onSuccess, onError);
          }
        }
      });
    } else {
      wx.showModal({
        title: '确认预约',
        content: content,
        success: async (res) => {
          if (res.confirm) {
            await performBooking(course, userInfo, page, onSuccess, onError);
          }
        }
      });
    }
  } catch (error) {
    showToast(page, { message: '检查考勤卡失败，请重试', theme: 'error' });
  }
};

/**
 * 执行预约操作
 * @param {Object} course 课程信息
 * @param {Object} userInfo 用户信息
 * @param {Object} page 页面实例
 * @param {Function} onSuccess 成功回调
 * @param {Function} onError 错误回调
 */
const performBooking = async (course, userInfo, page, onSuccess, onError) => {
  showLoading(page, '预约中...');
  try {
    const result = await wx.cloud.callFunction({
      name: 'bookingManagement',
      data: {
        action: 'bookCourse',
        data: {
          courseId: course.id || course._id,
          userId: userInfo.openid,
          courseName: course.name,
          courseTime: course.time,
          courseDate: new Date()
        }
      }
    });
    
    hideToast(page);
    
    if (result.result.success) {
      showToast(page, { message: '预约成功', theme: 'success' });
      if (onSuccess) {
        onSuccess(course);
      }
    } else {
      showToast(page, { message: result.result.message || '预约失败', theme: 'error' });
      if (onError) {
        onError(result.result.message);
      }
    }
  } catch (error) {
    hideToast(page);
    console.error('预约失败:', error);
    showToast(page, { message: '预约失败，请重试', theme: 'error' });
    if (onError) {
      onError(error);
    }
  }
};

/**
 * 统一的取消预约逻辑
 * @param {Object} course 课程信息
 * @param {Object} page 页面实例
 * @param {Function} onSuccess 成功回调
 * @param {Function} onError 错误回调
 */
export const cancelBooking = async (course, page, onSuccess, onError) => {
  // 检查登录状态
  const userInfo = checkUserLogin(page);
  if (!userInfo) return;

  // 检查课程状态
  if (course.ended) {
    showToast(page, { message: '活动已结束，无法取消', theme: 'warning' });
    return;
  }
  if (course.started) {
    showToast(page, { message: '活动已开始，无法取消', theme: 'warning' });
    return;
  }

  // 检查取消时间限制
  if (checkCancelTimeLimit(course, page.data.cancelTimeLimit, page)) {
    return;
  }

  // 确认取消
  const confirmContent = `确定要取消预约"${course.name}"吗？`;
  
  if (page.data.showDialog !== undefined) {
    page.setData({
      showDialog: true,
      dialogTitle: '确认取消',
      dialogContent: confirmContent,
      dialogConfirmText: '确认',
      dialogCancelText: '取消',
      dialogCallback: async (action) => {
        if (action === 'confirm') {
          await performCancelBooking(course, userInfo, page, onSuccess, onError);
        }
      }
    });
  } else {
    wx.showModal({
      title: '确认取消',
      content: confirmContent,
      success: async (res) => {
        if (res.confirm) {
          await performCancelBooking(course, userInfo, page, onSuccess, onError);
        }
      }
    });
  }
};

/**
 * 执行取消预约操作
 * @param {Object} course 课程信息
 * @param {Object} userInfo 用户信息
 * @param {Object} page 页面实例
 * @param {Function} onSuccess 成功回调
 * @param {Function} onError 错误回调
 */
const performCancelBooking = async (course, userInfo, page, onSuccess, onError) => {
  showLoading(page, '取消中...');
  try {
    const success = await cancelBookingApi(course.id || course._id, userInfo.openid);
    hideToast(page);
    
    if (success) {
      showToast(page, { message: '取消成功', theme: 'success' });
      if (onSuccess) {
        onSuccess(course);
      }
    } else {
      showToast(page, { message: '取消失败', theme: 'error' });
      if (onError) {
        onError('取消失败');
      }
    }
  } catch (error) {
    hideToast(page);
    console.error('取消预约失败:', error);
    showToast(page, { message: '取消预约失败，请重试', theme: 'error' });
    if (onError) {
      onError(error);
    }
  }
};

/**
 * 格式化日期
 * @param {Date|string} date 日期
 * @returns {string} 格式化的日期字符串
 */
const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  return `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}-${d.getDate().toString().padStart(2,'0')}`;
};

/**
 * 加载系统设置
 * @param {Object} page 页面实例
 * @returns {Promise<number|null>} 取消时间限制
 */
export const loadSystemSettings = async (page) => {
  try {
    const settings = await getSystemSettings();
    const cancelTimeLimit = settings?.booking?.cancelTimeLimitMinutes;
    page.setData({ cancelTimeLimit: cancelTimeLimit ?? null });
    return cancelTimeLimit;
  } catch (error) {
    console.error('加载系统设置失败:', error);
    page.setData({ cancelTimeLimit: null });
    showToast(page, { message: '加载系统设置失败', theme: 'error' });
    return null;
  }
}; 