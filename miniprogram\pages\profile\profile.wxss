/* profile.wxss */
/*
 * Profile页面样式文件 - 统一设计规范版本
 *
 * 设计规范：
 * 1. 字体：统一使用苹方字体 (PingFang SC)
 * 2. 字体大小：仅使用三种规格
 *    - 大字体: 32rpx (主要标题、重要信息)
 *    - 中字体: 28rpx (正文、功能项)
 *    - 小字体: 24rpx (辅助信息、标签)
 * 3. 色彩：统一蓝色主题 (#0052d9)
 * 4. 圆角：统一使用 24rpx
 * 5. 阴影：统一使用 0 8rpx 32rpx rgba(0, 82, 217, 0.15)
 */

/* ==================== 全局样式和工具类 ==================== */

/* 统一字体 */
.pingfang-font {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 标准字体大小 */
.font-large { font-size: 32rpx; }   /* 大字体 - 主要标题 */
.font-medium { font-size: 28rpx; }  /* 中字体 - 正文内容 */
.font-small { font-size: 24rpx; }   /* 小字体 - 辅助信息 */

/* ==================== 页面容器 ==================== */

.container {
  padding: 32rpx;
  padding-bottom: calc(32rpx + 120rpx + env(safe-area-inset-bottom));
  background: linear-gradient(135deg, #f0f8ff 0%, #f5f5f5 50%, #fafafa 100%);
  min-height: 100vh;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
}

/* Logo样式 */
.logo {
  margin-right: 12px;
  border: 3px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.avatar {
  margin-right: 24rpx;
  /* 更厚的白色边框，增强层次感 */
  border: 4rpx solid white;
  /* 多层阴影效果，营造浮起的感觉 */
  box-shadow:
    0 4rpx 12rpx rgba(0, 0, 0, 0.15),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  /* 添加过渡动画 */
  transition: all 0.3s ease;
}

/* 头像悬浮效果（为未来扩展准备） */
.avatar:hover {
  transform: scale(1.05);
  box-shadow:
    0 6rpx 20rpx rgba(0, 0, 0, 0.2),
    0 3rpx 10rpx rgba(0, 0, 0, 0.12);
}

/* 登录区域样式 - 优化未登录状态 */
.login-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  /* 添加动画过渡效果 */
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
}

/* 登录区域动画效果 */
.login-section.fadeIn {
  opacity: 1;
  transform: translateY(0);
  animation: fadeInUp 0.6s ease-out;
}

.login-section.fadeOut {
  opacity: 0;
  transform: translateY(-20px);
  animation: fadeOutUp 0.5s ease-in;
}

/* 淡入动画关键帧 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 淡出动画关键帧 */
@keyframes fadeOutUp {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

.login-card {
  /* 渐变背景，从纯白到微蓝的渐变 */
  background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
  border-radius: 24rpx; /* 更大的圆角 */
  /* 更强的阴影效果，突出登录卡片的重要性 */
  box-shadow:
    0 8rpx 32rpx rgba(0, 82, 217, 0.08),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 60rpx 40rpx; /* 增加上下内边距 */
  display: flex;
  justify-content: center;
  /* 微妙的边框，增强卡片质感 */
  border: 1rpx solid rgba(0, 82, 217, 0.05);
  /* 添加动画过渡 */
  transition: all 0.3s ease;
}

.login-content {
  text-align: center;
  width: 100%;
  max-width: 320px;
  padding: 40px 20px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 统一文字样式 */
.login-text {
  font-size: 32rpx; /* 大字体 */
  font-weight: 600;
  color: #1a1a1a;
  margin: 24px 0 12px 0;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.login-tip {
  font-size: 24rpx; /* 小字体 */
  color: #666666;
  margin-bottom: 40px;
  line-height: 1.5;
  font-weight: 400;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 登录按钮区域样式 */
.login-button-section {
  margin: 32px 0 24px 0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.wx-login-btn {
  border-radius: 24rpx; /* 统一圆角 */
  font-weight: 600;
  font-size: 28rpx; /* 中字体 */
  height: 52px;
  line-height: 52px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  /* 更流畅的过渡动画 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  /* 增强阴影效果 */
  box-shadow:
    0 6px 20px rgba(0, 82, 217, 0.25),
    0 3px 10px rgba(0, 82, 217, 0.15);
  letter-spacing: 0.8px; /* 增加字间距 */
  /* 添加渐变背景 */
  background: linear-gradient(135deg, #0052D9 0%, #1890ff 100%) !important;
  /* 微妙的边框 */
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.wx-login-btn:active {
  transform: translateY(3px) scale(0.98); /* 增强按压效果 */
  box-shadow:
    0 3px 12px rgba(0, 82, 217, 0.4),
    0 1px 6px rgba(0, 82, 217, 0.2);
  /* 按下时稍微改变渐变 */
  background: linear-gradient(135deg, #003d9e 0%, #1677cc 100%) !important;
}

/* 重置button默认样式 */
.wx-login-btn::after {
  border: none;
}

/* 登录按钮加载状态样式 */
.wx-login-btn.loading {
  /* 加载时稍微降低透明度，表示正在处理 */
  opacity: 0.8;
  /* 禁用点击时的变换效果 */
  transform: none !important;
  /* 加载时的渐变背景 */
  background: linear-gradient(135deg, #0052D9 0%, #1890ff 100%) !important;
}

/* 登录按钮内容区域样式 */
.login-loading-content,
.login-normal-content {
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/*
注意：不再需要手动的加载图标旋转动画
因为TDesign的loading属性会自动处理加载图标的显示和旋转
*/

/* 个人资料区域样式 */
.profile-section {
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* ==================== 全新用户信息卡片样式 ==================== */

/* 用户信息卡片主容器 */
.user-profile-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  padding: 40rpx 32rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 82, 217, 0.15);
  border: 1rpx solid rgba(0, 82, 217, 0.1);
  /* 添加微妙的纹理效果 */
  background-image:
    radial-gradient(circle at 20% 80%, rgba(0, 82, 217, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 82, 217, 0.05) 0%, transparent 50%);
}

/* 顶部操作区域 */
.profile-actions {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  display: flex;
  gap: 16rpx;
  z-index: 10;
}

.profile-actions .quick-edit-btn,
.profile-actions .notification-icon-wrapper {
  padding: 12rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.profile-actions .quick-edit-btn:active,
.profile-actions .notification-icon-wrapper:active {
  background: rgba(255, 255, 255, 1);
  transform: scale(0.95);
}

/* 主要信息区域 */
.profile-main {
  text-align: center;
  margin-bottom: 40rpx;
}

/* 头像区域 */
.profile-avatar-section {
  margin-bottom: 32rpx;
}

.avatar-wrapper-new {
  position: relative;
  display: inline-block;
}

.avatar-new {
  border: 6rpx solid white;
  box-shadow: 0 8rpx 24rpx rgba(0, 82, 217, 0.2);
}

.avatar-btn-overlay-new {
  position: absolute;
  top: 0;
  left: 0;
  width: 100rpx;
  height: 100rpx;
  opacity: 0;
  border-radius: 50%;
  background: none;
  border: none;
  padding: 0;
}

/* 用户基本信息 */
.profile-info {
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.profile-name {
  font-size: 32rpx; /* 大字体 */
  font-weight: 600;
  margin-bottom: 16rpx;
  color: #1a1a1a;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.profile-roles {
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 12rpx;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}



/* 统计数据区域 */
.profile-stats {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 217, 0.1);
  border: 1rpx solid rgba(0, 82, 217, 0.1);
}

.stats-row {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.stat-item-new {
  text-align: center;
  color: #333;
  flex: 1;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.stat-number-new {
  font-size: 32rpx; /* 大字体 */
  font-weight: 700;
  margin-bottom: 8rpx;
  color: #0052d9;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.stat-label-new {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background: rgba(0, 82, 217, 0.2);
  margin: 0 16rpx;
}

/* 卡片样式 - 与用户信息卡片完全一致的阴影效果 */
.profile-card {
  background: #ffffff;
  border-radius: 24rpx; /* 与用户信息卡片保持一致的圆角 */
  margin-bottom: 32rpx;
  /* 使用与用户信息卡片完全相同的阴影效果 */
  box-shadow: 0 8rpx 32rpx rgba(0, 82, 217, 0.15);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  /* 添加过渡动画，让交互更流畅 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* 使用与用户信息卡片相同的边框 */
  border: 1rpx solid rgba(0, 82, 217, 0.1);
}

/* 卡片悬浮效果（虽然小程序没有hover，但可以用于未来扩展） */
.profile-card:hover {
  transform: translateY(-2rpx);
  box-shadow:
    0 8rpx 30rpx rgba(0, 0, 0, 0.12),
    0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.profile-card.student,
.profile-card.coach,
.profile-card.admin {
  border-top: none;
}
/*
  移除左侧色条设计，保持简洁统一的视觉风格
  与上方的用户信息卡片保持一致
*/
.card-header {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx 20rpx;
  /* 添加淡蓝色背景，与用户信息卡片呼应 */
  background: linear-gradient(135deg, #fafbff 0%, #f5f8ff 100%);
  /* 移除底部边框线，让设计更简洁现代 */
  /* border-bottom: 1rpx solid #f0f0f0; */
  position: relative;
}

/* 用户信息卡片头部特殊样式 - 支持右侧通知图标 */
.profile-card.info .card-header {
  justify-content: space-between; /* 两端对齐，左侧标题，右侧通知图标 */
}

/* 卡片头部左侧区域 */
.card-header-left {
  display: flex;
  align-items: center;
}

/* 卡片头部右侧区域 */
.card-header-right {
  display: flex;
  align-items: center;
  gap: 16rpx; /* 设置子元素间距 */
}

/* 快捷编辑按钮 */
.quick-edit-btn {
  padding: 8rpx;
  border-radius: 8rpx;
  transition: background-color 0.2s ease;
}

.quick-edit-btn:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 28rpx; /* 中字体 */
  font-weight: 600;
  color: #333333;
  margin-left: 16rpx;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.card-content {
  padding-left: 32rpx;
  padding-right: 32rpx;
  margin-bottom: 8rpx;

}
.card-content-userprofile {
 padding: 32rpx;
}

.user-info {
  display: flex;
  align-items: center;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  padding-bottom: 32rpx;
  padding-top: 32rpx;
}

/* 增强版用户信息区域 */
.user-info.enhanced {
  align-items: flex-start; /* 改为顶部对齐，适应更多内容 */
  padding-bottom: 24rpx; /* 减少底部间距，为统计区域留空间 */
}

.user-details {
  margin-left: 16px;
  flex: 1;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 增强版用户详情区域 */
.user-details.enhanced {
  margin-left: 0; /* 重置左边距，由头像包装器控制间距 */
  display: flex;
  flex-direction: column;
  gap: 16rpx; /* 设置子元素间距 */
}

.user-name {
  font-size: 28rpx; /* 中字体 */
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 用户昵称和等级区域 */
.user-name-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap; /* 允许换行 */
}

/* 增强版用户昵称 */
.user-name.enhanced {
  font-size: 32rpx; /* 大字体 */
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 用户等级徽章 */
.user-level {
  flex-shrink: 0; /* 防止压缩 */
}

.user-role {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 增强版角色标签区域 */
.user-role.enhanced {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}



/* 功能区域样式 */
.function-section {
  margin-bottom: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.function-group {
  margin-bottom: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.group-title {
  font-size: 28rpx; /* 中字体 */
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding-left: 8px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  position: relative;
}

.group-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #0052d9, #1890ff);
  border-radius: 2px;
}

.function-list {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 28rpx 0; /* 增加内边距，让点击区域更大 */
  border-bottom: 1rpx solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  /* 更流畅的过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  /* 添加圆角，让每个功能项更独立 */
  border-radius: 12rpx;
  margin: 4rpx 0;
  /* 微妙的背景渐变 */
  background: linear-gradient(90deg, transparent 0%, rgba(0, 82, 217, 0.01) 100%);
}

.function-item:last-child {
  border-bottom: none;
}

/* 点击时的反馈效果 */
.function-item:active {
  background: linear-gradient(90deg, rgba(0, 82, 217, 0.05) 0%, rgba(0, 82, 217, 0.02) 100%);
  transform: translateX(4rpx) scale(0.98); /* 轻微缩放和位移 */
  /* 添加内阴影效果 */
  box-shadow: inset 0 2rpx 4rpx rgba(0, 82, 217, 0.1);
}

.function-item t-icon:first-child {
  margin-right: 12px;
  color: #0052d9;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.function-item text {
  flex: 1;
  font-size: 28rpx; /* 中字体 */
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-weight: 500;
}

.function-item-label {
  margin-left: 16rpx;
}

.function-item t-icon:last-child {
  color: #ccc;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 退出登录区域样式 */
.logout-section {
  margin-top: 32px;
  margin-bottom: 20px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.logout-btn {
  display: block;
  margin: 40rpx auto 0 auto;
  width: 80%;
  border-radius: 24rpx;
  font-weight: 700;
  font-size: 28rpx; /* 中字体 */
  box-shadow: 0 4rpx 16rpx rgba(227,77,89,0.08);
  background: #fff0f0;
  color: #e34d59;
  border: 2rpx solid #e34d59;
  transition: background 0.2s, color 0.2s;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}
.logout-btn:active {
  background: #e34d59;
  color: #fff;
}

.avatar-btn {
  padding: 0;
  border: none;
  background: none;
  outline: none;
  box-shadow: none;
  display: inline-block;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
}

/* 增强版头像包装器 */
.avatar-wrapper.enhanced {
  margin-right: 32rpx; /* 增加与右侧内容的间距 */
}

/* 增强版头像样式 */
.avatar.enhanced {
  /* 增强阴影效果 */
  box-shadow:
    0 8rpx 24rpx rgba(0, 0, 0, 0.12),
    0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  /* 增加边框 */
  border: 6rpx solid white;
  /* 添加渐变边框效果 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 4rpx;
}



/* 增强版头像按钮覆盖层 */
.avatar-btn-overlay.enhanced {
  width: 90rpx;
  height: 90rpx;
}
.avatar-btn-overlay {
  position: absolute;
  left: 0; top: 0;
  width: 60px; height: 60px;
  opacity: 0;
  padding: 0;
  border: none;
  background: none;
  z-index: 2;
}

.agreement-link {
  color: #0052d9;
  text-decoration: underline;
  text-underline-offset: 2px;
  text-decoration-thickness: 1px;
  cursor: pointer;
  font-size: 24rpx; /* 小字体 */
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.agreement-link:hover {
  color: #003d9e;
  text-decoration-color: #003d9e;
}

.agreement-link:active {
  color: #002a7a;
  text-decoration-color: #002a7a;
}

.agreement-section {
  background: transparent !important;
  padding: 0;
  margin-top: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.agreement-section t-checkbox,
.agreement-section .t-checkbox {
  background: transparent !important;
}

/*
  通知图标相关样式
  用于个人中心页面的通知入口
*/

/* 通知图标包装器 */
.notification-icon-wrapper {
  position: relative; /* 相对定位，为红点提供定位基准 */
  padding: 8rpx; /* 增加点击区域 */
  cursor: pointer; /* 鼠标指针样式 */
  transition: opacity 0.2s ease; /* 点击时的透明度过渡 */
}

/* 通知图标点击效果 */
.notification-icon-wrapper:active {
  opacity: 0.6; /* 点击时降低透明度，提供视觉反馈 */
}

/* 通知红点徽章 */
.notification-badge {
  position: absolute; /* 绝对定位，相对于父容器 */
  top: 0; /* 距离顶部0距离 */
  right: 0; /* 距离右侧0距离 */
  min-width: 32rpx; /* 最小宽度，确保圆形 */
  height: 32rpx; /* 固定高度 */
  background-color: #ff4757; /* 红色背景 */
  border-radius: 16rpx; /* 圆角，形成圆形或胶囊形 */
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  border: 2rpx solid #ffffff; /* 白色边框，与背景分离 */
  box-sizing: border-box; /* 边框计入尺寸 */
}

/* 红点内的数字文字 */
.badge-text {
  font-size: 24rpx; /* 小字体 */
  color: #ffffff;
  font-weight: 600;
  line-height: 1;
  padding: 0 6rpx;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  white-space: nowrap;
}

.agreement-section text {
  font-size: 24rpx; /* 小字体 */
  line-height: 1.5;
  color: #666666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-weight: 400;
}

/* 页面装饰元素 */
.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(135deg, rgba(0, 82, 217, 0.03) 0%, transparent 70%);
  pointer-events: none; /* 不影响用户交互 */
  z-index: 0;
}

.container::after {
  content: '';
  position: fixed;
  bottom: 0;
  right: 0;
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(54, 179, 126, 0.02) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

/* 确保内容在装饰元素之上 */
.login-section,
.profile-section {
  position: relative;
  z-index: 1;
}

/* 用户统计信息区域 */
.user-stats {
  margin-top: 32rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 统计数据网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 3列等宽布局 */
  gap: 24rpx;
  margin-bottom: 0; /* 移除底部边距，因为没有进度条了 */
}

/* 统计项目 */
.stat-item {
  text-align: center;
  padding: 16rpx 8rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border: 1rpx solid rgba(0, 82, 217, 0.1);
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 217, 0.15);
}

/* 统计数字 */
.stat-number {
  font-size: 32rpx; /* 大字体 */
  font-weight: 700;
  color: #0052d9;
  line-height: 1.2;
  margin-bottom: 8rpx;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 统计标签 */
.stat-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}



/* 角色标签美化 */
.user-role .t-tag {
  /* 添加微妙的渐变背景 */
  background: linear-gradient(135deg, rgba(0, 82, 217, 0.1) 0%, rgba(0, 82, 217, 0.05) 100%) !important;
  /* 增强边框 */
  border: 1rpx solid rgba(0, 82, 217, 0.2) !important;
  /* 添加阴影 */
  box-shadow: 0 2rpx 4rpx rgba(0, 82, 217, 0.1) !important;
}

/* 卡片入场动画 */
@keyframes cardSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-animate {
  animation: cardSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 为不同的卡片添加延迟，创造层次感 */
.profile-card:nth-child(1) { animation-delay: 0.1s; }
.profile-card:nth-child(2) { animation-delay: 0.2s; }
.profile-card:nth-child(3) { animation-delay: 0.3s; }
.profile-card:nth-child(4) { animation-delay: 0.4s; }
.profile-card:nth-child(5) { animation-delay: 0.5s; }

/* 功能项图标旋转动画 */
@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.function-item:active t-icon:first-child {
  animation: iconPulse 0.3s ease;
}

/* Logo呼吸动画 */
@keyframes logoBreath {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

.logo {
  animation: logoBreath 3s ease-in-out infinite;
}

/* 状态指示器样式 */
.status-indicator {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx); /* 毛玻璃效果 */
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 不同状态的颜色 */
.student-status .status-dot {
  background: #FFAA00;
  box-shadow: 0 0 10rpx rgba(255, 170, 0, 0.5);
}

.coach-status .status-dot {
  background: #36B37E;
  box-shadow: 0 0 10rpx rgba(54, 179, 126, 0.5);
}

.admin-status .status-dot {
  background: #0052D9;
  box-shadow: 0 0 10rpx rgba(0, 82, 217, 0.5);
}

/* 状态点脉冲动画 */
@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* 浮动装饰元素 */
.floating-decorations {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 不影响用户交互 */
  z-index: 0;
  overflow: hidden;
}

.floating-dot {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 82, 217, 0.1), rgba(54, 179, 126, 0.1));
  animation: float 6s ease-in-out infinite;
}

.dot-1 {
  width: 60rpx;
  height: 60rpx;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.dot-2 {
  width: 40rpx;
  height: 40rpx;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.dot-3 {
  width: 80rpx;
  height: 80rpx;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.dot-4 {
  width: 30rpx;
  height: 30rpx;
  top: 40%;
  right: 30%;
  animation-delay: 1s;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-20rpx) rotate(120deg);
    opacity: 0.6;
  }
  66% {
    transform: translateY(10rpx) rotate(240deg);
    opacity: 0.4;
  }
}