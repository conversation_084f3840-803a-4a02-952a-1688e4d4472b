/* index.wxss */
/*
 * 这是微信小程序的样式文件，相当于传统Web开发中的CSS文件
 *
 * WXSS (WeiXin Style Sheets) 语法说明：
 * WXSS基本与CSS相同，但有一些小程序特有的扩展和限制
 *
 * 主要区别对比：
 *
 * 1. 尺寸单位差异：
 *    - CSS: px, em, rem, %, vw, vh等
 *    - WXSS: 除了CSS单位外，还支持rpx（responsive pixel）
 *    - rpx: 响应式像素，会根据屏幕宽度自动调整
 *    - 换算：iPhone6屏幕宽度375px = 750rpx，即1px = 2rpx
 *
 * 2. 样式导入：
 *    - CSS: @import url("style.css");
 *    - WXSS: @import "style.wxss";
 *
 * 3. 选择器支持：
 *    - CSS: 支持所有选择器（伪类、属性选择器、兄弟选择器等）
 *    - WXSS: 只支持基础选择器（类、ID、元素、后代、子选择器等）
 *
 * 4. 全局样式：
 *    - CSS: 通过link标签引入或style标签定义
 *    - WXSS: app.wxss为全局样式，页面样式会覆盖全局样式
 *
 * 5. 内联样式：
 *    - HTML: <div style="color: red;">
 *    - WXML: <view style="color: red;">（用法相同）
 */

/**
 * .container: 根容器样式类
 * 作用类似于Web开发中的body或main容器
 *
 * 盒模型知识回顾：
 * - content: 内容区域
 * - padding: 内边距（内容与边框之间的距离）
 * - border: 边框
 * - margin: 外边距（元素与其他元素之间的距离）
 *
 * box-sizing属性：
 * - content-box: 默认值，width/height只包含content
 * - border-box: width/height包含content + padding + border
 */
.container {
  /*
   * padding: 内边距设置
   * 语法：padding: 上 右 下 左; 或 padding: 上下 左右; 或 padding: 全部;
   * 16px表示所有四个方向都是16像素的内边距
   *
   * 与您熟悉的C#对比：类似于WPF的Margin或Padding属性
   * 与Java对比：类似于Swing的Insets或Android的padding
   */
  padding: 16px;

  /*
   * padding-bottom: 底部内边距特殊设置
   * 为底部tabBar预留空间，避免内容被遮挡
   *
   * calc()函数：CSS计算函数，可以进行数学运算
   * 语法：calc(表达式)，支持 +、-、*、/ 运算
   * 注意：运算符前后必须有空格
   *
   * env()函数：CSS环境变量函数，获取系统环境信息
   * safe-area-inset-bottom：底部安全区域高度
   * 主要用于适配iPhone X等全面屏手机的底部安全区域
   */
  padding-bottom: calc(16px + 120rpx + env(safe-area-inset-bottom));

  /*
   * background-color: 背景颜色
   * 颜色值格式：
   * - 十六进制：#f5f5f5（最常用）
   * - RGB：rgb(245, 245, 245)
   * - RGBA：rgba(245, 245, 245, 1)（带透明度）
   * - 颜色名：lightgray
   *
   * #f5f5f5是浅灰色，常用于页面背景
   */
  background-color: #f5f5f5;

  /*
   * min-height: 最小高度
   * 100vh表示视口高度的100%（viewport height）
   * 确保容器至少占满整个屏幕高度，即使内容不足
   *
   * 视口单位：
   * - vw: 视口宽度的1%（1vw = 视口宽度/100）
   * - vh: 视口高度的1%（1vh = 视口高度/100）
   * - vmin: vw和vh中较小的值
   * - vmax: vw和vh中较大的值
   */
  min-height: 100vh;

  /*
   * font-family: 字体族设置
   * 定义字体优先级列表，浏览器会按顺序查找可用字体
   *
   * 字体说明：
   * - "PingFang SC": 苹果系统中文字体（简体中文）
   * - "Helvetica Neue": 苹果系统英文字体
   * - "Arial": Windows系统常用字体
   * - sans-serif: 无衬线字体族（兜底字体）
   *
   * 字体分类：
   * - serif: 衬线字体（如Times New Roman），适合正文阅读
   * - sans-serif: 无衬线字体（如Arial），适合界面显示
   * - monospace: 等宽字体（如Courier），适合代码显示
   */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * box-sizing: 盒模型计算方式
   *
   * 两种模式对比：
   * - content-box（默认）: width = content宽度
   *   实际占用宽度 = width + padding + border
   * - border-box: width = content + padding + border
   *   实际占用宽度 = width
   *
   * border-box更符合直觉，设置width:100px，元素就占用100px宽度
   * 现代CSS开发中通常全局设置为border-box
   */
  box-sizing: border-box;
}

/**
 * 视差滚动效果样式区域
 *
 * 视差滚动(Parallax Scrolling)原理：
 * 不同层级的元素以不同速度滚动，产生立体感和深度感
 * 常见于现代网站的首屏展示区域
 *
 * 实现方式：
 * 1. 背景图片固定或缓慢移动
 * 2. 前景内容正常滚动
 * 3. 通过transform: scale()实现缩放效果
 * 4. 使用z-index控制层级关系
 */

/**
 * .parallax-header: 视差滚动容器
 * 作为视差效果的外层容器，控制整体布局和尺寸
 */
.parallax-header {
  /*
   * position: relative - 相对定位
   * 作为绝对定位子元素的参考点
   * 子元素的absolute定位会相对于这个容器
   *
   * CSS定位类型对比：
   * - static: 默认定位，按文档流排列
   * - relative: 相对定位，相对于自身原始位置偏移
   * - absolute: 绝对定位，相对于最近的非static父元素
   * - fixed: 固定定位，相对于视口固定
   * - sticky: 粘性定位，滚动时在relative和fixed间切换
   */
  position: relative;

  /*
   * overflow: hidden - 隐藏溢出内容
   * 当子元素超出容器边界时，超出部分会被裁剪
   *
   * overflow属性值：
   * - visible: 默认值，溢出内容可见
   * - hidden: 隐藏溢出内容
   * - scroll: 始终显示滚动条
   * - auto: 需要时显示滚动条
   *
   * 这里用于裁剪缩放后的背景图片，保持容器边界整洁
   */
  overflow: hidden;

  /*
   * width: 100% - 宽度占满父容器
   * 百分比单位相对于父元素的对应属性
   */
  width: 100%;

  /*
   * height: 18vh - 高度为视口高度的18%
   * vh单位：viewport height，视口高度的百分比
   * 18vh约等于屏幕高度的1/5，适合作为头部展示区域
   *
   * 高度单位选择建议：
   * - px: 固定高度，不同屏幕显示效果可能差异较大
   * - vh: 相对视口高度，适配不同屏幕尺寸
   * - %: 相对父元素高度，需要父元素有明确高度
   */
  height: 18vh;

  /*
   * margin-bottom: 16px - 下外边距
   * 与下方元素保持16像素的间距
   * margin用于控制元素间的距离
   */
  margin-bottom: 16px;

  /*
   * border-radius: 8px - 圆角边框
   * 设置容器四个角的圆角半径为8像素
   *
   * border-radius语法：
   * - border-radius: 8px; (四个角相同)
   * - border-radius: 8px 4px; (上下8px，左右4px)
   * - border-radius: 8px 4px 2px 1px; (顺时针：上右下左)
   *
   * 现代UI设计中，适度的圆角可以让界面更加柔和友好
   */
  border-radius: 8px;

  /*
   * box-shadow: 阴影效果
   * 语法：box-shadow: x偏移 y偏移 模糊半径 扩展半径 颜色;
   *
   * 参数解析：
   * - 0: x轴偏移，0表示不向左右偏移
   * - 2px: y轴偏移，向下偏移2像素
   * - 8px: 模糊半径，数值越大阴影越模糊
   * - rgba(0, 0, 0, 0.08): 阴影颜色，黑色8%透明度
   *
   * rgba颜色格式：
   * - r: red红色值 (0-255)
   * - g: green绿色值 (0-255)
   * - b: blue蓝色值 (0-255)
   * - a: alpha透明度 (0-1)
   *
   * 这里创建了一个轻微的下方阴影，增加层次感
   */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

  /* 字体继承全局设置 */
}

/**
 * .parallax-bg: 视差背景图片样式
 * 实现视差滚动效果的核心元素
 */
.parallax-bg {
  /*
   * position: absolute - 绝对定位
   * 相对于最近的relative父元素(.parallax-header)定位
   * 脱离文档流，不占用空间
   */
  position: absolute;

  /* 宽高100%，填满父容器 */
  width: 100%;
  height: 100%;

  /*
   * top: 0; left: 0; - 定位到父容器的左上角
   * 与父容器完全重合
   */
  top: 0;
  left: 0;

  /*
   * z-index: 0 - 层级设置
   * 数值越大，层级越高，越在上方显示
   * 0是最低层级，作为背景图片
   *
   * 层级规划：
   * - z-index: 0 - 背景图片
   * - z-index: 1 - 遮罩层
   * - z-index: 2 - 文字内容
   */
  z-index: 0;

  /*
   * object-fit: contain - 图片适应方式
   * 控制图片如何适应容器尺寸
   *
   * object-fit属性值：
   * - contain: 保持比例，完整显示图片，可能有空白
   * - cover: 保持比例，填满容器，可能裁剪图片
   * - fill: 拉伸图片填满容器，可能变形
   * - scale-down: contain和none中较小的一个
   * - none: 保持原始尺寸
   *
   * contain确保图片完整显示，不会被裁剪或变形
   */
  object-fit: contain;

  /*
   * transition: transform 0.3s - 过渡动画
   * 当transform属性改变时，用0.3秒时间平滑过渡
   *
   * transition语法：
   * transition: 属性名 持续时间 时间函数 延迟时间;
   *
   * 这里让缩放效果更加平滑，避免突兀的变化
   */
  transition: transform 0.3s;

  /*
   * transform: translateZ(0) - 3D变换
   * translateZ(0)将元素移动到3D空间的z=0平面
   * 主要目的是启用硬件加速，提升动画性能
   *
   * 硬件加速原理：
   * 浏览器会将该元素交给GPU处理，而不是CPU
   * GPU专门处理图形运算，性能更好
   */
  transform: translateZ(0);
}

/**
 * .parallax-overlay: 半透明遮罩层
 * 在背景图片上添加渐变遮罩，提升文字可读性
 */
.parallax-overlay {
  /* 绝对定位，覆盖整个父容器 */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  /*
   * background: linear-gradient() - 线性渐变背景
   * 创建从一种颜色到另一种颜色的平滑过渡
   *
   * 语法：linear-gradient(方向, 颜色1, 颜色2, ...)
   *
   * 参数解析：
   * - to top: 渐变方向，从下到上
   * - rgba(0,0,0,0.5): 起始颜色，黑色50%透明度
   * - transparent: 结束颜色，完全透明
   *
   * 效果：底部较暗，顶部透明，让底部的文字更清晰可读
   *
   * 渐变方向选项：
   * - to top/bottom/left/right: 基本方向
   * - to top left: 对角线方向
   * - 45deg: 角度值（0deg向上，90deg向右）
   */
  background: linear-gradient(to top, rgba(0,0,0,0.5), transparent);

  /* z-index: 1 - 在背景图片之上，在文字内容之下 */
  z-index: 1;

  /* 保持与父容器相同的圆角 */
  border-radius: 8px;
}

/* 视差内容区样式 */
.parallax-content {
  position: absolute; /* 绝对定位 */
  bottom: 32rpx; /* 底部距离，根据需求设置 */
  left: 16px; /* 左侧距离 */
  display: flex; /* 弹性布局 */
  align-items: flex-end; /* 底部对齐 */
  z-index: 2; /* 在最上层 */
}

/* Logo图片样式 */
.logo {
  margin-right: 12px;  /* 右边距 */
  border: none;  /* 移除边框 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);  /* 增强阴影效果 */
  border-radius: 8px; /* 圆角8px */
  position: relative;
  z-index: 1; /* 确保在背景图之上 */
  padding: 3px;  /* 内边距 */
  background: rgba(255, 255, 255, 0.8);  /* 半透明白色背景 */
}

/* 标题文字样式 */
.title {
  font-size: 42rpx;  /* 自己用一种大小 */
  font-weight: 600;  /* 字体粗细，相当于CSS的font-weight */
  color: #ffffff;  /* 白色文字 */
  letter-spacing: 1px;  /* 字符间距，相当于CSS的letter-spacing */
  /* 字体继承全局设置 */
  position: relative;
  z-index: 1; /* 确保在背景图之上 */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);  /* 文字阴影增强可读性 */

}

/* 微交互：按压效果 */
.interactive-press {
  transform: scale(0.98);
  filter: brightness(0.95);
}

/* 联系信息容器样式 */
.contact-info-container {
  width: 100%; /* 与parallax-header保持一致的宽度 */
  margin-bottom: 4px;
  padding-top: 4px;
  padding-bottom: 4px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

  opacity: 0; /* 初始状态设置为透明 */
  animation: image-fade-in 0.6s ease-out forwards; /* 应用动画 */
  animation-delay: 0.05s; /* 设置一个微小的延迟 */
  transition: transform 0.2s ease-out, filter 0.2s ease-out; /* 添加过渡效果 */
}

/* 联系信息行样式 */
.contact-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px; /* 减少底部外边距 */
  gap: 8px; /* 增加间距，确保分离 */
  padding: 6px 10px 0 10px; /* 减少顶部内边距 */
  width: 100%;
  box-sizing: border-box;
}

/* 联系信息项样式 */
.contact-item {
  display: flex;
  align-items: flex-start;
  flex: 1;
  gap: 4px;
  min-width: 0; /* 允许flex项目缩小 */
  max-width: 50%; /* 限制最大宽度，确保两个项目能并排显示 */
}

/* 联系信息图标样式 */
.contact-icon {
  color: #0052d9;
  font-size: 32rpx; /* 使用大字体规范 */
  margin-top: 2px;
  flex-shrink: 0;
}

/* 联系信息内容样式 */
.contact-content {
  position: relative;
  padding-right: 28px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 联系信息样式 */
.contact-label {
  font-size: 32rpx; /* 标题文字大一号，加粗 */
  color: #999;
  margin-bottom: 4px;
  font-weight: 600; /* 加粗 */
  flex-shrink: 0;
}

.contact-value {
  font-size: 32rpx; /* 与标签保持一致的大字体 */
  color: #888888;
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
  min-width: 0; /* 允许flex项目缩小 */
}

/* 电话号码专用样式 - 强制不换行 */
.contact-item-phone .contact-value {
  white-space: nowrap; /* 电话号码强制不换行 */
  overflow: visible; /* 允许溢出显示 */
  word-break: keep-all; /* 不断开单词 */
}

/* 地址文本样式 - 允许换行 */
.contact-item-address .contact-value {
  word-break: break-word; /* 地址允许长文本换行 */
  overflow-wrap: break-word; /* 兼容性更好的换行 */
}

/* 公告容器样式 */
.announcement-container {
  display: flex;
  align-items: flex-start;
  gap: 8rpx; /* 统一使用rpx单位 */
  padding: 12rpx 20rpx; /* 统一使用rpx单位，适当增加内边距确保文字显示完整 */
  border-top: 2rpx solid #f0f0f0; /* 统一使用rpx单位 */
  cursor: pointer; /* 添加手型光标 */
  transition: background-color 0.2s ease; /* 添加过渡效果 */
}

.announcement-container:active {
  background-color: #f5f5f5; /* 点击时的背景色 */
}

/* 公告图标样式 */
.announcement-icon {
  color: #ff6b35;
  font-size: 32rpx; /* 统一使用rpx单位 */
  margin-top: 4rpx; /* 统一使用rpx单位 */
  flex-shrink: 0;
}

/* 公告内容样式 */
.announcement-content {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1; /* 让内容区域占据剩余空间 */
  min-width: 0; /* 防止flex子元素溢出 */
}

/* 公告标题区域样式 */
.announcement-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx; /* 统一使用rpx单位 */
}

/* 公告样式 */
.announcement-label {
  font-size: 32rpx; /* 标题文字大一号，加粗 */
  color: #999;
  font-weight: 600; /* 加粗 */
  flex-shrink: 0; /* 防止标题被压缩 */
}

.announcement-text {
  font-size: 32rpx; /* 与标签保持一致的大字体 */
  color: #888888;
  line-height: 1.5;
  text-align: justify;
  width: 100%; /* 确保文本占据全宽 */
  word-wrap: break-word; /* 长文本换行 */
}

/* 通用的2行文字限制样式 */
.text-limited {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 公告文本限制样式 */
.announcement-text-limited {
  word-break: break-all;
  margin-right: 48rpx; /* 为更多按钮留出空间 */
  width: 100%; /* 确保文本占据全宽 */
}

/* 更多按钮样式 */
.announcement-more {
  position: absolute;
  right: 0;
  /* 让右箭头在多行文本的视觉中线居中 */
  top: 50%;
  transform: translateY(-50%) translateY(1em);
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx; /* 统一使用rpx单位 */
  height: 40rpx; /* 统一使用rpx单位 */
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  padding: 4rpx; /* 统一使用rpx单位 */
  z-index: 2;
}

/* ===================================================================
 * 多条公告样式 - Multiple Announcements Styles
 * ================================================================= */

/* 单条公告容器 */
.single-announcement {
  width: 100%;
}

/* 多条公告容器 */
.multiple-announcements {
  position: relative;
  width: 100%;
  display: flex;
  align-items: flex-start; /* 改为顶部对齐，确保内容正确显示 */
  gap: 16rpx; /* 统一使用rpx单位 */
  min-height: 130rpx; /* 增加高度，适配32rpx字体 */
}

/* 公告swiper样式 */
.announcement-swiper {
  flex: 1;
  height: 130rpx; /* 增加高度，确保能显示2行文字（32rpx × 1.5 × 2 + 余量 = 96rpx + 34rpx余量）*/
}

.announcement-swiper-item {
  display: flex;
  align-items: flex-start; /* 改为顶部对齐，避免文字被居中压缩 */
  height: 100%;
  padding-top: 4rpx; /* 添加少量顶部内边距，让文字位置更自然 */
}



/* 弹窗中的公告导航样式 */
.popup-announcement-nav {
  margin-top: 32rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.nav-info {
  text-align: center;
  margin-bottom: 16rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #999999;
}

.nav-buttons {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #333333;
  transition: all 0.2s ease;
}

.nav-btn:not(.disabled):active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.nav-btn.disabled {
  opacity: 0.5;
  color: #999999;
}

.nav-btn.disabled .t-icon {
  color: #999999;
}

/* 静态图片容器样式 */
.static-images-container {
  width: 100%;  /* 宽度100% */
  margin: 8px 0 20rpx 0;  /* 上边距使用px单位，更明显 */
  display: flex;  /* 弹性布局 */
  flex-direction: column;  /* 垂直排列 */
  gap: 16px;  /* 图片间距 */
}

/* 图片淡入动画 */
@keyframes image-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px); /* 增加移动距离，效果更明显 */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 静态图片样式 */
.static-image-wrapper {
  width: 100%;
  opacity: 0; /* 初始状态设置为透明 */
  animation: image-fade-in 0.6s ease-out forwards; /* 动画效果 */
  transition: transform 0.2s ease-out, filter 0.2s ease-out; /* 添加过渡效果 */
}

/* 为每个图片错开动画开始时间，产生更生动的效果 */
.static-image-wrapper:nth-child(1) {
  animation-delay: 0.1s;
}
.static-image-wrapper:nth-child(2) {
  animation-delay: 0.2s;
}
.static-image-wrapper:nth-child(3) {
  animation-delay: 0.3s;
}

.static-image {
  width: 100%;
  /* 移除固定高度和 object-fit，保证高度自适应 */
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: block;
  background-color: #f5f5f5;
}

/*
 * 高端功能按钮容器样式
 * 采用现代化设计理念，营造高端大气的视觉效果
 */
.function-buttons {
  gap: 40rpx;  /* 统一使用rpx单位，自动适配 */
  width: 100%;
  margin-bottom: 40rpx;  /* 增加底部间距 */
  display: flex;
  justify-content: space-between;
  padding: 0 32rpx;  /* 统一使用rpx单位，自动适配 */

}

.function-buttons.fixed-bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 32rpx; /* 统一使用rpx单位，自动适配 */
  width: 100vw;
  max-width: 100vw;
  box-sizing: border-box;
  left: 0;
  right: 0;
  padding-left: 32rpx; /* 统一使用rpx单位，自动适配 */
  padding-right: 32rpx; /* 统一使用rpx单位，自动适配 */
}
/*
 * 功能按钮基础样式 - 照搬profile页面微信登录按钮样式
 */
.function-btn {
  flex: 1 1 0;
  min-width: 0;
  max-width: 100%;

  /* 照搬profile页面样式 */
  border-radius: 24rpx; /* 统一圆角 */
  font-weight: 600;
  height: 104rpx; /* 统一使用rpx单位，自动适配（52px * 2 = 104rpx） */
  line-height: 104rpx; /* 统一使用rpx单位，自动适配 */


  /* 更流畅的过渡动画 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  /* 增强阴影效果 */
  box-shadow:
    0 6px 20px rgba(0, 82, 217, 0.3),
    0 2px 8px rgba(0, 82, 217, 0.15);

  /* 默认渐变背景 */
  background: linear-gradient(135deg, #0052D9 0%, #1890ff 100%) !important;

  /* 微妙的边框 */
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* 重置button默认样式 - 照搬profile页面 */
.function-btn::after {
  border: none;
}

/*
 * 按钮点击效果 - 照搬profile页面样式
 */
.function-btn:active {
  transform: translateY(3px) scale(0.98); /* 增强按压效果 */
  box-shadow:
    0 3px 12px rgba(0, 82, 217, 0.4),
    0 1px 6px rgba(0, 82, 217, 0.2);
  /* 按下时稍微改变渐变 */
  background: linear-gradient(135deg, #003d9e 0%, #1677cc 100%) !important;
}

/*
 * 按钮内容样式 - 照搬profile页面
 */
.function-btn .t-button__content {
  height: 100%;
  display: flex;
  flex-direction: row;  /* 改为水平排列，像profile页面一样 */
  justify-content: center;
  align-items: center;
  gap: 8rpx;
  color: #ffffff !important;  /* 白色文字 */
  font-weight: 600;  /* 字体加粗 */
  font-size: 28rpx;  /* 使用profile页面的中字体 */
  letter-spacing: 1rpx;  /* 字符间距，增加高级感 */
  position: relative;
  z-index: 10;  /* 确保内容在背景之上 */
}

/*
 * 所有按钮统一使用profile页面的蓝色风格
 * 简洁、专业、统一
 */



/*
 * 按钮图标样式 - 简洁版
 */
.function-btn .t-icon {
  font-size: 32rpx !important;  /* 适中的图标大小 */
  color: #ffffff !important;
  margin-right: 8rpx;  /* 图标和文字的间距 */
}

/*
 * 简化的响应式适配 - 保持与profile页面一致
 */

/*
 * 移除了功能按钮的媒体查询适配
 * 原因：rpx单位会自动根据屏幕宽度进行适配
 * 不需要额外的媒体查询来破坏自然的适配机制
 */



/* 区块样式，用于课程区域 */
.section {
  margin-bottom: 24px;  /* 下边距 */
  /* 字体继承全局设置 */
}

/* 区块标题样式 */
.section-title {
  font-size: 18px;  /* 字体大小 */
  font-weight: 600;  /* 字体粗细 */
  margin-bottom: 16px;  /* 下边距 */
  color: #333;  /* 文字颜色 */
  /* 字体继承全局设置 */
}

/* 课程卡片容器样式 */
.course-cards {
  display: flex;  /* 弹性布局 */
  flex-direction: column;  /* 垂直排列 */
  gap: 16px;  /* 元素间距 */
}

/* 课程卡片样式 */
.course-card {
  background-color: #ffffff;  /* 背景色，白色 */
  border-radius: 12px;  /* 圆角 */
  padding: 16px;  /* 内边距 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);  /* 阴影效果 */
  /* 字体继承全局设置 */
}

/* 课程头部样式 */
.course-header {
  display: flex;  /* 弹性布局 */
  justify-content: space-between;  /* 两端对齐 */
  align-items: center;  /* 垂直居中 */
  margin-bottom: 12px;  /* 下边距 */
}

/* 课程标题样式 */
.course-title {
  font-size: 18px;  /* 字体大小 */
  font-weight: 600;  /* 字体粗细 */
  color: #333;  /* 文字颜色 */
  /* 字体继承全局设置 */
}

/* 课程状态样式 */
.course-status {
  padding: 4px 8px;  /* 内边距 */
  border-radius: 4px;  /* 圆角 */
  font-size: 12px;  /* 字体大小 */
  font-weight: 500;  /* 字体粗细 */
  /* 字体继承全局设置 */
  flex-shrink: 0;
  white-space: nowrap;
}

/* 可预约状态样式 */
.course-status.available {
  background-color: #e8f5e8;  /* 背景色，浅绿色 */
  color: #52c41a;  /* 文字颜色，绿色 */
}

/* 已满状态样式 */
.course-status.full {
  background-color: #fff2e8;  /* 背景色，浅橙色 */
  color: #fa8c16;  /* 文字颜色，橙色 */
}

/* 课程信息列表样式 */
.course-info-list {
  margin-bottom: 12px;  /* 下边距 */
}

/* 课程信息项样式 */
.course-info-item {
  display: flex;  /* 弹性布局 */
  align-items: center;  /* 垂直居中 */
  margin-bottom: 8px;  /* 下边距 */
  font-size: 14px;  /* 字体大小 */
  color: #666;  /* 文字颜色，灰色 */
  /* 字体继承全局设置 */
}

/* 课程信息项中的图标样式 */
.course-info-item t-icon {
  margin-right: 8px;  /* 右边距 */
  color: #0052d9;  /* 图标颜色，蓝色 */
}

/* 课程描述样式 */
.course-description {
  font-size: 14px;  /* 字体大小 */
  color: #666;  /* 文字颜色，灰色 */
  line-height: 1.5;  /* 行高，相当于CSS的line-height */
  margin-bottom: 16px;  /* 下边距 */
  /* 字体继承全局设置 */
}

/* 课程操作按钮容器样式 */
.course-actions {
  display: flex;  /* 弹性布局 */
  justify-content: flex-end;  /* 右对齐 */
}

/* 门店公告弹出层样式 */
.announcement-popup {
  --td-popup-border-radius: 20px;
}

.popup-content {
  background-color: #ffffff;
  border-radius: 20px;
  width: 80vw;
  max-width: 340px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  padding-bottom: 32px; /* 给下方留白 */
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 28px 24px 18px 24px;
  border-bottom: 1px solid #f0f0f0;
  gap: 10px;
}

.popup-title-icon {
  color: #faad14;
}

.indent-text {
  text-indent: 2em;
  display: block;
}

.popup-title {
  font-size: 20px;
  font-weight: 700;
  color: #333;

}

.close-btn {
  position: absolute;
  left: 50%;
  margin-left: -32rpx;
  bottom: -80rpx;
  cursor: pointer;
  transition: transform 0.2s ease;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
}

.close-btn:active {
  transform: scale(0.9);
}

.popup-body {
  padding: 28px 24px 0 24px;
  flex: 1;
  overflow-y: auto;
}

.popup-text {
  font-size: 17px;
  color: #333;
  line-height: 1.5;
  text-align: justify;
  word-break: break-all;
  white-space: pre-wrap;

}

.text-unify {
  font-size: 15px;
  font-weight: 500;
  color: #222;

}



/* 电话信息项 - 左侧显示，固定宽度，不换行 */
.contact-item-phone {
  flex: 0 0 auto; /* 根据内容自动调整宽度 */
  white-space: nowrap; /* 强制不换行 */
  min-width: fit-content; /* 最小宽度适应内容 */
  max-width: none; /* 不限制最大宽度，让电话号码完整显示 */
}

/* 地址信息项 - 右侧显示，占用剩余全部空间 */
.contact-item-address {
  flex: 1; /* 占用剩余的所有空间 */
  min-width: 0; /* 允许缩小 */
  max-width: none; /* 不限制最大宽度 */
}

/* 强制左右分布 - 防止在任何屏幕尺寸下贴在一起 */
.contact-row {
  flex-wrap: nowrap !important; /* 强制不换行 */
}

.contact-item-phone,
.contact-item-address {
  flex-shrink: 1 !important; /* 允许缩小但保持比例 */
  overflow: hidden; /* 隐藏溢出内容 */
}

/*
 * 移除了小屏幕媒体查询适配
 * 原因：rpx单位本身就是为了自适应而设计的，会根据屏幕宽度自动缩放
 * 额外的媒体查询会破坏rpx的自然适配机制
 *
 * rpx自适应原理：
 * - 所有设备屏幕宽度固定为750rpx
 * - 1rpx = (设备屏幕宽度 / 750) px
 * - 在320px宽度设备上：28rpx = 28 × (320/750) = 11.95px（自动适配）
 * - 在375px宽度设备上：28rpx = 28 × (375/750) = 14px（自动适配）
 */

/* 联系信息限制样式 */
.contact-value-limited {
  word-break: break-word; /* 避免强制断开单词 */
  min-width: 0; /* 允许flex项目缩小 */
  max-width: 100%; /* 确保不超出容器 */
}

.address-more {
  position: absolute;
  right: 0;
  /* 让箭头在多行文本的视觉中线居中（多行文本的垂直视觉居中） */
  top: 50%;
  transform: translateY(-50%) translateY(1em); /* 0.35em 可根据实际视觉微调 */
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  padding: 2px;
  z-index: 2;
}

.address-popup {
  --td-popup-border-radius: 20px;
}

.contact-divider {
  width: 1px;
  background: #e0e0e0;
  margin: 0 0 0 24rpx;
  align-self: stretch;
}

/* 相册popup美化升级 */
.album-popup {
  background: transparent !important;
}
.album-popup-content {
  background: transparent;
  border-radius: 0;
  width: 96vw;
  max-width: 100vw;
  max-height: 90vh;
  min-height: 0;
  box-shadow: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.album-popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #222;
  margin-bottom: 12px;
  letter-spacing: 1px;
  text-align: center;
}
.album-popup-content swiper {
  width: 100vw;
  height: 90vh;
}
.album-popup-content swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.album-popup-content image {
  width: 100vw;
  height: 90vh;
  object-fit: contain;
  background: transparent;
}
.album-popup-index {
  margin-top: 14px;
  font-size: 14px;
  color: #888;
  text-align: center;
  letter-spacing: 1px;
}
/* 关闭按钮（右上角悬浮） */
.album-popup .album-close-btn {
  position: absolute;
  top: 14px;
  right: 14px;
  z-index: 10001;
  width: auto;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255,255,255,0.92);
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  font-size: 15px;
  color: #333;
  padding: 0 12px 0 4px;
  cursor: pointer;
}
.album-popup .album-close-btn t-icon {
  margin-right: 4px;
}
.album-popup-close {
  position: absolute;         /* 绝对定位，固定在弹窗右上角 */
  top: 18px;
  right: 24px;
  z-index: 10;
  width: 48px;                /* 按钮宽高，圆形 */
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(30, 30, 30, 0.65); /* 深色半透明背景，提升高级感（增加不透明度替代毛玻璃效果） */
  border-radius: 50%;         /* 圆形按钮 */
  box-shadow: 0 4px 16px rgba(0,0,0,0.18); /* 阴影，增强立体感 */
  /* backdrop-filter: blur(6px); 毛玻璃效果在小程序中可能不支持，已移除 */
  transition: background 0.2s, box-shadow 0.2s;
  cursor: pointer;
}
.album-popup-close:active {
  background: rgba(30, 30, 30, 0.7);  /* 按下时背景更深，提升交互感 */
  box-shadow: 0 2px 8px rgba(0,0,0,0.28);
}
.album-popup-close t-icon {
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.25)); /* 图标阴影，保证白色图标在浅色图片上也清晰 */
}












/*
 * Loading Animation Styles - 加载动画样式
 *
 * 设计说明:
 * - 借鉴了`profile`页面的登录加载样式，提供一个居中、带有呼吸动画的Logo，提升品牌感和用户等待体验。
 * - 动画效果旨在吸引用户的注意力，同时传达系统正在积极工作的状态。
 */

/*
 * .loading-container: 加载动画的根容器
 *
 * CSS属性详解:
 * - display: flex;
 *   - 作用: 启用Flexbox布局模型。这是一种一维布局模型，可以轻松地对容器内的项目进行对齐和分布。
 *   - 对比: 类似于建筑设计中的“模块化网格系统”，可以灵活地安排元素位置。
 *
 * - flex-direction: column;
 *   - 作用: 设置主轴方向为垂直。容器内的项目（Logo和文字）会从上到下排列。
 *   - 对比: 就像在一栋建筑里，将房间垂直堆叠起来。
 *
 * - align-items: center;
 *   - 作用: 在交叉轴（这里是水平方向）上居中对齐项目。
 *   - 效果: Logo和文字都会在水平方向上居中。
 *
 * - justify-content: center;
 *   - 作用: 在主轴（这里是垂直方向）上居中对齐项目。
 *   - 效果: Logo和文字的整体会在垂直方向上居中。
 *
 * - position: fixed;
 *   - 作用: 将元素相对于浏览器窗口进行定位，即使页面滚动，它也会停留在同一的位置。
 *   - 常用值: `static`(默认), `relative`, `absolute`, `fixed`, `sticky`。
 *   - `fixed`常用于创建模态框、固定的导航栏或加载遮罩。
 *
 * - top: 0; left: 0; width: 100%; height: 100%;
 *   - 作用: 这四行代码组合在一起，让`.loading-container`完全覆盖整个屏幕。
 *
 * - background-color: #f5f5f5;
 *   - 作用: 设置背景颜色为浅灰色 (`#f5f5f5`)，与页面的主背景色保持一致。
 *
 * - z-index: 9999;
 *   - 作用: 设置元素的堆叠顺序。拥有更高`z-index`的元素会显示在拥有较低`z-index`的元素之上。
 *   - `9999`是一个非常大的值，确保加载动画会覆盖在页面所有其他内容之上。
 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  z-index: 9999;
  opacity: 1; /* 新增：默认状态为不透明，为淡出做准备 */

  /*
   * 新增: transition 属性
   * - 作用: 为`opacity`（透明度）属性添加过渡效果。
   * - 当`opacity`的值发生变化时（例如，在`.hiding`状态下变为0），它不会立即改变，
   *   而是会在0.5秒内平滑地过渡到新值。这就是实现“淡出”效果的关键。
   * - `ease-in-out`: 时间函数，动画慢速开始和结束，中间加速，效果更自然。
   */
  transition: opacity 0.5s ease-in-out;
}

/*
 * 新增: .loading-container.hiding 状态
 * - 当JS给容器添加`hiding`类后，此样式生效。
 * - `opacity: 0;`: 将容器设置为完全透明。
 * - 由于我们在`.loading-container`中定义了`transition`，这个变化会触发淡出动画。
 */
.loading-container.hiding {
  opacity: 0;
}

/*
 * .loading-logo: 加载动画中的Logo图片样式
 *
 * CSS属性详解:
 * - animation: breathing 2.5s ease-in-out infinite;
 *   - 作用: 应用一个名为`breathing`的关键帧动画。
 *   - `2.5s`: 动画完成一个周期需要2.5秒。
 *   - `ease-in-out`: 动画的时间函数，表示动画以慢速开始，中间加速，然后以慢速结束。
 *   - `infinite`: 表示动画将无限次重复播放。
 *   - 对比: 就像建筑物的呼吸灯效果，通过光线强弱变化营造生命感。
 */
.loading-logo {
  animation: breathing 2.5s ease-in-out infinite;
}

/*
 * @keyframes breathing: 定义名为`breathing`的动画
 *
 * 动画详解:
 * - `@keyframes`规则用于定义动画的各个阶段。
 * - `0%, 100%`: 动画的开始和结束状态。
 *   - `transform: scale(0.95);`: 将元素缩小到其原始大小的95%。
 *   - `box-shadow`: 设置一个比较柔和的阴影。
 * - `50%`: 动画进行到一半时的状态。
 *   - `transform: scale(1.05);`: 将元素放大到其原始大小的105%。
 *   - `box-shadow`: 设置一个更明显、更弥散的阴影。
 * - 效果: Logo会平滑地放大和缩小，同时阴影也会随之变化，产生一种“呼吸”的动态效果。
 */
@keyframes breathing {
  0%, 100% {
    transform: scale(0.95);
    box-shadow: 0 0 20px 5px rgba(0, 82, 217, 0.1);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 40px 15px rgba(0, 82, 217, 0.3);
  }
}

/*
 * .loading-text-container: 加载文字和图标的容器
 *
 * CSS属性详解:
 * - display: flex; align-items: center;
 *   - 作用: 再次使用Flexbox，让内部的旋转图标和“正在加载...”文字在水平方向上对齐。
 * - margin-top: 40rpx;
 *   - 作用: 在Logo和这行文字之间增加40rpx的垂直间距。
 */
.loading-text-container {
  display: flex;
  align-items: center;
  margin-top: 40rpx; /* 之前是20px，现在用rpx单位并增加间距 */
}

/*
 * .loading-spinner: 旋转的加载图标
 *
 * CSS属性详解:
 * - font-size: 32rpx !important;
 *   - 作用: 设置图标的大小。`!important`会覆盖来自组件库的默认样式，确保我们的设置生效。
 * - color: #0052d9;
 *   - 作用: 设置图标的颜色为主色调蓝色。
 * - margin-right: 16rpx;
 *   - 作用: 在图标和右侧的文字之间增加16rpx的间距。
 */
.loading-spinner {
  font-size: 32rpx !important; /* 之前是16px，现在用rpx并增大 */
  color: #0052d9;
  margin-right: 16rpx; /* 之前是8px，现在用rpx并增大 */
}

/*
 * .loading-text: “正在加载...”文字样式
 *
 * CSS属性详解:
 * - font-size: 28rpx;
 *   - 作用: 设置文字的大小。
 * - color: #888;
 *   - 作用: 设置文字颜色为深灰色。
 * - letter-spacing: 1px;
 *   - 作用: 增加字符之间的间距，让文字看起来更清晰、更有设计感。
 */
.loading-text {
  font-size: 28rpx; /* 之前是14px，现在用rpx并增大 */
  color: #888;
  letter-spacing: 1px;
}
