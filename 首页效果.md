对 @/index 页面顶部实现微信朋友圈顶部视差图片效果进行改造，并支持下拉刷新，并让顶部背景图片（矮、宽的图片）尽量完整显示：

【需求目标】
将首页顶部区域改造成类似微信朋友圈的视差滚动大图效果，并且支持下拉刷新，且背景图片尽量完整展示，具体要求如下：

【实现要点】
1. 结构调整（index.wxml）：
   - 将原有.header区域升级为视差容器（parallax-header）。
   - 内部包含三层：背景大图（<image>）、半透明遮罩（<view>）、头像（<image>）+标题（<text>）内容区。
   - 背景大图的<image>标签需设置mode="contain"，以保证矮、宽图片尽量完整显示，不被裁剪。
   - 页面根节点需支持下拉刷新（如<scroll-view scroll-y enable-refresh ...> 或页面配置 "enablePullDownRefresh": true）。

2. 样式优化（index.wxss）：
   - 视差容器：position: relative; overflow: hidden; 高度建议18vh（原27vh基础上再压缩三分之一，若原为30vh~33vh则相应压缩为20vh~22vh，根据实际图片比例调整）。
   - 背景图：position: absolute; width: 100%; height: 100%; object-fit: contain; transition: transform 0.3s; 支持transform缩放。
   - 遮罩层：background: linear-gradient(to top, rgba(0,0,0,0.5), transparent)。
   - 头像区：头像底部对齐，建议bottom: 80rpx，头像60x60rpx，圆角8rpx，白色描边阴影。

3. 交互逻辑（index.js）：
   - 监听onPageScroll，计算scrollTop。
   - 动态调整背景图scale，公式：scale = 1 + scrollTop * 0.0005，最大不超过1.15。
   - 通过setData实时更新背景图样式。
   - 监听onPullDownRefresh事件，触发数据刷新逻辑，刷新完成后调用wx.stopPullDownRefresh()停止下拉动画。

4. 关键参数建议：
   - 最大缩放比例：1.15
   - 滚动缩放系数：0.0005
   - 头像底部距离：80rpx
   - 视差区最小高度：89rpx（原133rpx基础上再压缩三分之一）
   - 视差区高度建议根据图片比例调整，保证图片尽量完整显示（如图片比例为16:9，可设置高度为宽度的9/16，再压缩三分之一，然后再压缩三分之一）。

5. 异常与边界处理：
   - 图片加载失败时，显示默认渐变背景色，头像区可隐藏。
   - 下拉超阈值时触发微信回弹，上滑到导航栏时锁定背景图。
   - 下拉刷新时，顶部视差区可适当放大，刷新完成后恢复。

6. 性能优化建议：
   - 背景图transform加translateZ(0)启用硬件加速。
   - 滚动事件节流，必要时用wx.createSelectorQuery()。
   - 大图资源提前预加载（wx.preloadAssets）。
   - 下拉刷新时避免重复请求，刷新动画及时关闭。

【特别说明】
- 重点：背景图片的<image>标签请务必设置mode="contain"（或object-fit: contain），避免矮、宽图片被拉伸或裁剪，保证图片尽量完整显示。
- 如需更精细控制，可动态计算图片实际比例，调整视差区高度以适配不同图片，提升视觉体验。注意本次已将高度整体在原基础上连续压缩两次三分之一，请根据实际图片效果微调。
