<!--pages/course-detail/course-detail.wxml-->
<!--
  课程详情页面 - 美化版
  Designed by Gemini

  设计理念:
  1. 沉浸式头部: 使用课程图片作为背景，创建引人注目的视觉焦点。
  2. 信息卡片化: 将不同维度的信息（时间、讲师、详情）组织在独立的卡片中，结构清晰，易于阅读。
  3. 视觉层次: 利用背景渐变、卡片阴影、毛玻璃效果和优雅的间距，构建丰富的视觉层次感。
  4. 交互反馈: 为关键操作（如按钮点击）添加微妙的动画效果，提升用户体验。
  5. 状态驱动UI: 按钮和标签根据课程状态（可预约、已满、已结束）动态变化，提供清晰的指引。
-->

<!-- 页面根容器 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading && !courseDetail}}" class="loading-container">
    <view class="loading-content">
      <t-loading size="40rpx" text="加载中..." />
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{loadingError}}" class="error-container">
    <view class="error-icon-placeholder"></view>
    <view class="error-message">课程加载失败</view>
    <t-button theme="primary" size="large" bind:tap="onRetry">重新加载</t-button>
  </view>

  <!-- 课程详情内容 -->
  <block wx:else>
    <!-- 沉浸式头部区域 -->
    <view class="hero-section">
      <image 
        src="{{courseImages.length > 0 ? courseImages[0].tempFileURL : ''}}"
        class="hero-background-image" 
        mode="aspectFill"
      />
      <view class="hero-overlay"></view>
      <view class="hero-content">
        <view class="course-title-main">{{courseDetail.name}}</view>
        <view class="course-tags">
          <view class="tag-item status-{{courseStatus === '可预约' ? 'available' : (courseStatus === '已预约' ? 'booked' : (courseStatus === '已满' ? 'full' : 'ended'))}}">
            <t-icon name="{{courseStatus === '可预约' ? 'check-circle' : (courseStatus === '已预约' ? 'check-circle' : (courseStatus === '已满' ? 'error-circle' : 'time'))}}" size="14" class="tag-icon"/>
            {{courseStatus}}
          </view>
          <view class="tag-item">
            <t-icon name="time" size="14" class="tag-icon"/>
            {{courseDetail.duration}}
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 时间与地点卡片 -->
      <view class="info-card">
        <view class="info-item-large">
          <t-icon name="calendar" size="20" class="info-icon-large"/>
          <view class="info-text-large">
            <view class="info-label">日期</view>
            <view class="info-value">{{courseDetail.date}}</view>
          </view>
        </view>
        <view class="info-item-large">
          <t-icon name="time" size="20" class="info-icon-large"/>
          <view class="info-text-large">
            <view class="info-label">时间</view>
            <view class="info-value">{{courseDetail.time}}</view>
          </view>
        </view>
        <view class="info-item-large">
          <t-icon name="location" size="20" class="info-icon-large"/>
          <view class="info-text-large">
            <view class="info-label">地点</view>
            <view class="info-value">{{courseDetail.venue}}</view>
          </view>
        </view>
      </view>

      <!-- 课程详情卡片 -->
      <view class="content-card">
        <view class="card-header">
          <t-icon name="article" size="20" class="card-header-icon"/>
          <view class="card-title">课程详情</view>
        </view>
        <view class="card-body">
          <text class="description-text">{{courseDetail.activityDetail.description || '暂无详情介绍'}}</text>
        </view>
      </view>

      <!-- 授课讲师卡片 -->
      <view class="content-card" wx:if="{{courseDetail.coaches && courseDetail.coaches.length > 0}}">
        <view class="card-header">
          <t-icon name="usergroup" size="20" class="card-header-icon"/>
          <view class="card-title">授课讲师</view>
        </view>
        <view class="card-body">
          <view class="coach-list">
            <view class="coach-item" wx:for="{{courseDetail.coaches}}" wx:key="id">
              <t-avatar image="{{item.avatar || ''}}" size="48px"/>
              <view class="coach-info">
                <view class="coach-name">{{item.name}}</view>
                <view class="coach-specialty">{{item.specialty}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 预约信息卡片 -->
      <view class="content-card">
        <view class="card-header">
          <t-icon name="chart-bar" size="20" class="card-header-icon"/>
          <view class="card-title">预约信息</view>
        </view>
        <view class="card-body">
          <view class="booking-info-grid">
            <view class="booking-info-item">
              <view class="booking-info-value">{{courseDetail.capacity}}</view>
              <view class="booking-info-label">总名额</view>
            </view>
            <view class="booking-info-item">
              <view class="booking-info-value">{{courseDetail.bookedCount}}</view>
              <view class="booking-info-label">已预约</view>
            </view>
            <view class="booking-info-item remaining-{{courseDetail.remaining > 0 ? 'yes' : 'no'}}">
              <view class="booking-info-value">{{courseDetail.remaining}}</view>
              <view class="booking-info-label">剩余</view>
            </view>
          </view>
          <view class="cancel-policy">
            <t-icon name="info-circle" size="14" class="cancel-policy-icon"/>
            <text>取消政策: {{courseDetail.cancelPolicy}}内不可取消</text>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- 底部悬浮操作栏 -->
  <view class="sticky-footer" wx:if="{{courseDetail}}">
    <!-- 学员身份 -->
    <block wx:if="{{isStudent}}">
      <t-button 
        wx:if="{{isBooked && courseStatus !== '已结束'}}"
        theme="danger" 
        size="large"
        block
        bind:tap="cancelBooking"
        class="action-button"
      >取消预约</t-button>
      <t-button 
        wx:elif="{{courseDetail.available && courseStatus !== '已结束'}}"
        theme="primary" 
        size="large"
        block
        bind:tap="bookCourse"
        class="action-button"
      >立即预约</t-button>
      <t-button 
        wx:else
        theme="default" 
        size="large"
        block
        disabled
        class="action-button"
      >{{courseStatus}}</t-button>
    </block>

    <!-- 讲师身份 -->
    <block wx:elif="{{isCoach}}">
      <t-button 
        theme="primary" 
        variant="outline"
        size="large"
        block
        bind:tap="showStudentList"
        class="action-button"
      >
        <t-icon name="usergroup" slot="icon"/>
        查看学员名单
      </t-button>
    </block>

    <!-- 管理员身份 -->
    <block wx:elif="{{isAdmin}}">
      <view class="admin-actions">
        <t-button theme="primary" variant="outline" bind:tap="showStudentList" class="admin-btn">学员</t-button>
        <t-button theme="default" bind:tap="editCourse" class="admin-btn">编辑</t-button>
        <t-button theme="danger" bind:tap="deleteCourse" class="admin-btn">删除</t-button>
      </view>
    </block>

    <!-- 未登录 -->
    <block wx:else>
      <t-button 
        theme="primary" 
        size="large"
        block
        bind:tap="goToLogin"
        class="action-button"
      >登录后操作</t-button>
    </block>
  </view>

  <!-- 学员名单弹窗 (保持不变) -->
  <t-drawer 
    visible="{{showStudentList}}" 
    placement="bottom" 
    bind:close="closeStudentList"
    height="60%"
  >
    <view class="student-list-container">
      <view class="student-list-header">
        <text class="student-list-title">预约学员名单</text>
        <text class="student-count">共{{studentList.length}}人</text>
      </view>
      <view class="student-list-content">
        <view wx:if="{{studentList.length === 0}}" class="empty-student-list">
          <t-icon name="user" size="48" />
          <text>暂无预约学员</text>
        </view>
        <block wx:else>
          <view class="student-item" wx:for="{{studentList}}" wx:key="id">
            <t-avatar image="{{item.avatar || ''}}" size="40px"/>
            <view class="student-info">
              <view class="student-name">{{item.name}}</view>
              <view class="student-book-time">预约时间：{{item.bookTime}}</view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </t-drawer>

  <!-- 全局组件 -->
  <t-toast id="t-toast" />
</view>