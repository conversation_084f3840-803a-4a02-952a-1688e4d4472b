<view class="container">
  <!--
    视图切换栏区域 - 从schedule页面移植的TDesign标签栏设计
    使用专业的TDesign t-tabs组件替换原有的简单标签栏
    提供更美观的线条式选项卡设计和更流畅的交互体验
  -->
  <view class="view-section">
    <!--
      顶部选项卡区域容器 - 与schedule页面保持完全一致的设计
      包含主选项卡组件的容器，提供优雅的线条式设计
    -->
    <view class="top-tabs-section">
      <!--
        TDesign线条选项卡组件 - 从schedule页面完整移植

        属性说明：
        - value: 当前激活的选项卡值，对应activeView数据
        - bindchange: 选项卡切换事件，调用onViewChange方法
        - theme: 选项卡主题，"line"表示线条式选项卡
        - show-bottom-line: 是否显示底部分割线
        - t-class: 自定义样式类名，使用custom-top-tabs样式

        与原来的booking-tabs对比：
        - 原来：自定义div + CSS实现的简单tab
        - 现在：TDesign专业组件，样式更美观，交互更流畅

        标签内容：
        - "当前课程": 显示当前可查看的课程
        - "历史课程": 显示已结束的历史课程
      -->
      <t-tabs value="{{activeView}}" bindchange="onViewChange" theme="line" show-bottom-line="{{true}}" t-class="custom-top-tabs">
        <!--
          选项卡面板：定义具体的tab项 - 从schedule页面移植的数据驱动设计
          t-tab-panel: TDesign的选项卡面板组件

          使用wx:for循环渲染viewTabs数组中的每个选项
          这样可以保持数据驱动的设计模式，便于维护和扩展
        -->
        <t-tab-panel
          wx:for="{{viewTabs}}"
          wx:key="value"
          value="{{item.value}}"
          label="{{item.label}}"
        />
      </t-tabs>
    </view>
  </view>

  <!-- 横向日期选择器已移除 - 简化页面布局，让课程列表占据更多空间 -->

  <!-- 课程列表 -->
  <view class="course-list">
    <!-- 历史课程tab - 使用分页加载 -->
    <view wx:if="{{activeView === 'history' && historyCourses.length === 0 && !historyLoading}}" style="flex: 1; display: flex; align-items: center; justify-content: center;">
      <t-empty description="{{emptyDescription}}" />
    </view>
    <scroll-view
      wx:if="{{activeView === 'history' && (historyCourses.length > 0 || historyLoading)}}"
      scroll-y="true"
      style="flex: 1; min-height: 0;"
      bindscrolltolower="onReachBottom"
    >
      <block wx:for="{{historyCourses}}" wx:key="id">
        <!-- 日期分组分隔条 -->
        <view wx:if="{{index === 0 || historyCourses[index-1].date !== item.date}}" class="timeline-date">{{item.date}}</view>
        <view class="course-card" bind:tap="onCourseTap" data-course="{{item}}">
          <view class="course-header">
            <view class="course-title">{{item.name}}</view>
            <view class="course-status ended">已结束</view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.formattedDate}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <view class="info-item">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
            <view class="info-item">
              <t-icon name="chart" size="16" />
              <text>剩余名额：{{item.remaining}}/{{item.capacity}}</text>
            </view>
          </view>
        </view>
      </block>
      <view wx:if="{{historyLoading}}" class="loading-indicator">加载中...</view>
      <view wx:elif="{{!historyHasMore && historyCourses.length > 0}}" class="end-indicator">没有更多了</view>
    </scroll-view>

    <!-- 当前课程tab - 也使用scroll-view以保持一致性 -->
    <view wx:if="{{activeView === 'current' && filteredCourseList.length === 0 && !loading}}" style="flex: 1; display: flex; align-items: center; justify-content: center;">
      <t-empty description="{{emptyDescription}}" />
    </view>
    <scroll-view
      wx:if="{{activeView === 'current' && (filteredCourseList.length > 0 || loading)}}"
      scroll-y="true"
      style="flex: 1; min-height: 0;"
      bindscrolltolower="onCurrentReachBottom"
    >
      <view wx:if="{{filteredCourseList.length > 0}}">
        <view class="course-card" wx:for="{{filteredCourseList}}" wx:key="_id" bindtap="onCourseTap" data-course="{{item}}">
          <view class="course-header">
            <view class="course-title">{{item.name}}</view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.date}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
            <view class="info-item">
              <t-icon name="chart" size="16" />
              <text>剩余名额：{{item.capacity - item.bookedCount}}/{{item.capacity}}</text>
            </view>
            <view class="info-item">
              <t-icon name="help-circle" size="16" />
              <text class="activity-detail">活动详情：{{item.activityDetail.description}}</text>
            </view>
          </view>
          <!-- 已预约学员列表等原有内容保留 -->
          <view class="booked-students-section" catchtap="onStudentSectionTap">
            <view class="collapse-header" catchtap="toggleCollapse" data-course-id="{{item._id}}">
              <text>已预约学员（{{item.bookedStudents ? item.bookedStudents.length : 0}}人）</text>
              <t-icon name="{{item.collapseValue && item.collapseValue.length > 0 && item.collapseValue.indexOf('students') !== -1 ? 'chevron-up' : 'chevron-down'}}" size="16" />
            </view>
            <view class="collapse-content" wx:if="{{item.collapseValue && item.collapseValue.length > 0 && item.collapseValue.indexOf('students') !== -1}}">
              <view class="student-list">
                <view wx:if="{{!item.bookedStudents || item.bookedStudents.length === 0}}" class="no-students">
                  暂无预约学员
                </view>
                <view wx:else>
                  <view class="student-item" wx:for="{{item.bookedStudents}}" wx:key="openid" wx:for-item="student">
                    <t-icon name="user" size="14" />
                    <text class="student-name">{{student.nickName || student.name || '未知用户'}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="course-footer"></view>
        </view>
      </view>
      <view wx:if="{{loading}}" class="loading-indicator">加载中...</view>
    </scroll-view>
  </view>
  <t-dialog visible="{{showDetailDialog}}" title="课程详情" bind:close="onDialogClose">
    <!-- 详情弹窗内容可后续补充 -->
  </t-dialog>
  <t-toast id="t-toast" />
</view>
<!-- 底部导航栏占位符移到容器外部，确保布局计算正确 -->
<view id="tab-bar-placeholder"></view>