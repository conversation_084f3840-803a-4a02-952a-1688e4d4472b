<!--
  membership-card-management.wxml - 考勤卡管理页面结构文件

  页面功能概述：
  考勤卡管理系统的核心页面，提供考勤卡和模板的全生命周期管理功能

  主要功能模块：
  1. 双tab设计：考勤卡管理 + 模板管理
  2. 多维度筛选：按状态、关键词筛选考勤卡
  3. 搜索功能：支持按卡号、学员搜索
  4. 批量操作：编辑、绑定、删除等操作

  设计风格：照搬course-management页面的UI设计
-->

<!--
  根容器：页面的最外层容器
  采用与course-management相同的容器结构
-->
<view class="container">
  <!--
    顶部区域：包含主选项卡
    完全照搬schedule页面的结构设计
  -->
  <view class="view-section">
    <!--
      顶部选项卡区域容器
      使用与schedule页面相同的样式设计
    -->
    <view class="top-tabs-section">
      <!--
        TDesign线条选项卡组件

        属性说明：
        - value: 当前激活的选项卡值，对应topActiveTab数据
        - bindchange: 选项卡切换事件，调用onTopTabsChange方法
        - theme: 选项卡主题，"line"表示线条式选项卡
        - show-bottom-line: 是否显示底部分割线
        - t-class: 自定义样式类名，使用custom-top-tabs样式

        与schedule页面保持完全一致的设计：
        - 相同的组件和样式
        - 相同的交互体验
        - 统一的视觉风格
      -->
      <t-tabs value="{{topActiveTab}}" bindchange="onTopTabsChange" theme="line" show-bottom-line="{{true}}" t-class="custom-top-tabs">
        <!--
          选项卡面板：定义具体的tab项
          t-tab-panel: TDesign的选项卡面板组件
        -->
        <t-tab-panel value="card" label="考勤卡维护" />
        <t-tab-panel value="template" label="模板维护" />
      </t-tabs>
    </view>
  </view>

  <!--
    考勤卡筛选区域 - 固定在顶部，不参与滚动

    功能模块：
    1. 状态筛选：按考勤卡状态筛选（全部、已绑定、未绑定）
    2. 关键词搜索：按卡号、学员搜索

    设计：照搬course-management的筛选区域设计
  -->
  <view wx:if="{{topActiveTab === 'card'}}" class="filter-section">
    <!--
      考勤卡状态选项卡

      设计模式：照搬course-management的自定义选项卡实现
      样式：使用原生view实现，保持与course-management一致的视觉效果
    -->
    <view class="booking-tabs">
      <!--
        选项卡项目

        列表渲染：wx:for="{{cardTabList}}"
        数据源：cardTabList = [{label: '全部', value: 'all'}, {label: '已绑定', value: 'issued'}, {label: '未绑定', value: 'unissued'}]

        动态CSS类名：
        class="booking-tab {{cardActiveTab === item.value ? 'active' : ''}}"
        实现选中状态的视觉反馈
      -->
      <view
        class="booking-tab {{cardActiveTab === item.value ? 'active' : ''}}"
        wx:for="{{cardTabList}}"
        wx:key="value"
        bind:tap="onCardTabsChange"
        data-value="{{item.value}}"
      >
        {{item.label}}
      </view>
    </view>

    <!--
      搜索和操作区域

      功能：考勤卡搜索 + 操作按钮
      设计：照搬course-management的搜索和操作区域布局
      布局：搜索图标 + 按钮组合，点击搜索图标展开搜索框
    -->
    <view class="search-actions-section {{cardSearchExpanded ? 'expanded' : 'collapsed'}}">
      <!--
        收起状态：搜索图标 + 按钮紧贴排列
      -->
      <view wx:if="{{!cardSearchExpanded}}" class="collapsed-layout">
        <!--
          搜索图标
        -->
        <view class="search-icon-only" bindtap="onExpandCardSearch">
          <t-icon name="search" size="20" class="search-toggle-icon" />
        </view>

        <!--
          操作按钮区域
        -->
        <view class="actions-container">
          <!--
            新建考勤卡按钮
          -->
          <t-button theme="primary" size="small" bindtap="onCreateCard">
            新建考勤卡
          </t-button>
        </view>
      </view>

      <!--
        展开状态：搜索框占据全部宽度
      -->
      <view wx:else class="expanded-layout">
        <view class="search-input-container">
          <!--
            搜索图标
          -->
          <t-icon name="search" size="16" class="search-icon" />

          <!--
            搜索输入框
          -->
          <input
            class="search-input"
            placeholder="搜索会员卡号/所属学员"
            value="{{cardSearchKeyword}}"
            bindinput="onCardSearchInput"
            bindconfirm="onCardSearchSubmit"
            bindblur="onCardSearchBlur"
            focus="{{cardSearchExpanded}}"
          />

          <!--
            清空搜索图标
          -->
          <t-icon
            wx:if="{{cardSearchKeyword}}"
            name="close-circle-filled"
            size="16"
            class="clear-icon"
            bindtap="onCardSearchClear"
          />

          <!--
            收起搜索图标
          -->
          <t-icon
            name="chevron-left"
            size="16"
            class="collapse-icon"
            bindtap="onCollapseCardSearch"
          />
        </view>
      </view>
    </view>
  </view>

  <!--
    考勤卡维护内容区域 - 可滚动区域

    条件渲染：只有当topActiveTab为'card'时才显示
    布局：在筛选区域下方，可以滚动
  -->
  <view wx:if="{{topActiveTab === 'card'}}" class="course-content">

    <!--
      考勤卡列表区域

      功能：显示考勤卡数据的主要区域
      设计：照搬course-management的列表设计
      支持多种状态：加载中、空状态、正常列表
    -->
    <view class="course-list">
      <!--
        空状态显示

        条件：visibleCardList.length === 0
        组件：t-empty - TDesign的空状态组件
        描述：动态描述文字，根据搜索状态显示不同提示
      -->
      <t-empty wx:if="{{visibleCardList.length === 0}}" description="{{cardSearchKeyword ? '未找到匹配的考勤卡' : '暂无考勤卡'}}" />

      <!--
        考勤卡列表 - 滚动模式

        条件渲染：wx:elif="{{visibleCardList.length > 0}}"
        只有当有数据时才显示滚动列表

        scroll-view组件：照搬course-management的滚动设计
      -->
      <scroll-view
        wx:elif="{{visibleCardList.length > 0}}"
        scroll-y="true"
        style="height: 70vh; min-height: 0;"
        scroll-into-view="{{cardScrollIntoView}}"
      >
        <!--
          考勤卡列表渲染

          block标签：小程序的逻辑容器
          wx:for="{{visibleCardList}}": 列表渲染
          wx:key="_id": 列表项唯一标识
        -->
        <block wx:for="{{visibleCardList}}" wx:key="_id">
          <!--
            考勤卡卡片容器

            样式：复用course-card类名，保持与course-management一致的设计
            布局：照搬活动卡片的结构
          -->
          <view class="course-card" data-card="{{item}}" data-card-id="{{item._id}}">
            <!--
              考勤卡卡片头部

              布局：照搬course-management的卡片头部设计
              内容：卡号、状态标签
            -->
            <view class="course-header">
              <view class="course-title-row">
                <view class="course-title">{{item.cardNumber}}</view>
              </view>
              <!--
                考勤卡状态标签

                显示：根据考勤卡状态显示不同的标签
                样式：复用course-status类名
              -->
              <view class="course-status {{item.isExpired ? 'ended' : (item.isExpiring ? 'offline' : (item.userId ? 'online' : 'no-status'))}}">
                {{item.isExpired ? '已过期' : (item.isExpiring ? '即将到期' : (item.userId ? '已绑定' : '未绑定'))}}
              </view>
            </view>

            <!--
              考勤卡信息列表

              功能：显示考勤卡的详细信息
              布局：照搬course-management的信息列表设计
              样式：复用course-info-list类名
            -->
            <view class="course-info-list">
              <!--
                所属学员信息

                条件显示：只有当有学员信息时才显示此项
              -->
              <view class="info-item" wx:if="{{item.userNickName}}">
                <t-icon name="user" size="16" />
                <text>{{item.userNickName}}</text>
              </view>

              <!--
                有效期信息
              -->
              <view class="info-item">
                <t-icon name="time" size="16" />
                <text>{{item.validFrom}} ~ {{item.validTo}}</text>
              </view>

              <!--
                次数信息
              -->
              <view class="info-item">
                <t-icon name="chart" size="16" />
                <text>剩余次数：{{item.remainingTimes}}/{{item.totalTimes}}</text>
              </view>

              <!--
                绑定日期信息

                条件显示：只有当有绑定日期时才显示此项
              -->
              <view class="info-item" wx:if="{{item.issueDate}}">
                <t-icon name="calendar" size="16" />
                <text>绑定日期：{{item.issueDate}}</text>
              </view>

              <!--
                来源模板信息

                条件显示：只有当有模板信息时才显示此项
              -->
              <view class="info-item" wx:if="{{item.templateName}}">
                <t-icon name="help-circle" size="16" />
                <text>来源模板：{{item.templateName}}</text>
              </view>
            </view>

            <!--
              考勤卡操作区域

              功能：提供考勤卡管理的操作按钮
              布局：照搬course-management的操作按钮布局
              样式：复用course-footer和action-buttons类名
            -->
            <view class="course-footer">
              <!--
                操作按钮容器

                布局：照搬course-management的按钮布局
              -->
              <view class="action-buttons">
                <!--
                  未绑定的考勤卡操作按钮
                -->
                <block wx:if="{{(cardActiveTab==='unissued') || (cardActiveTab==='all' && (!item.userId || item.userId===''))}}">
                  <t-button size="small" theme="primary" catchtap="onEditCard" data-id="{{item._id}}">编辑</t-button>
                  <t-button size="small" theme="success" catchtap="onIssueCard" data-id="{{item._id}}">绑定</t-button>
                  <t-button size="small" theme="danger" catchtap="onDeleteCard" data-id="{{item._id}}">删除</t-button>
                </block>
                <!--
                  已绑定的考勤卡操作按钮
                -->
                <block wx:else>
                  <t-button size="small" theme="primary" catchtap="onEditCard" data-id="{{item._id}}">编辑</t-button>
                  <t-button size="small" theme="warning" catchtap="onRevokeCard" data-id="{{item._id}}">解绑</t-button>
                  <t-button size="small" theme="default" catchtap="onDelayCard" data-id="{{item._id}}">延期</t-button>
                  <t-button size="small" theme="default" catchtap="onFreezeCard" data-id="{{item._id}}">冻结</t-button>
                </block>
              </view>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
  </view>

  <!--
    模板筛选区域 - 固定在顶部，不参与滚动

    功能模块：
    1. 状态筛选：按模板状态筛选（全部、首页显示）
    2. 关键词搜索：按模板名称搜索

    设计：照搬course-management的筛选区域设计
  -->
  <view wx:if="{{topActiveTab === 'template'}}" class="filter-section">
    <!--
      模板状态选项卡

      设计模式：照搬course-management的自定义选项卡实现
      样式：使用原生view实现，保持与course-management一致的视觉效果
    -->
    <view class="booking-tabs">
      <!--
        选项卡项目

        列表渲染：wx:for="{{templateTabList}}"
        数据源：templateTabList = [{label: '全部', value: 'all'}, {label: '首页显示', value: 'homepage'}]
      -->
      <view
        class="booking-tab {{templateActiveTab === item.value ? 'active' : ''}}"
        wx:for="{{templateTabList}}"
        wx:key="value"
        bind:tap="onTemplateTabsChange"
        data-value="{{item.value}}"
      >
        {{item.label}}
      </view>
    </view>

    <!--
      搜索和操作区域

      功能：模板搜索 + 操作按钮
      设计：照搬course-management的搜索和操作区域布局
    -->
    <view class="search-actions-section {{templateSearchExpanded ? 'expanded' : 'collapsed'}}">
      <!--
        收起状态：搜索图标 + 按钮紧贴排列
      -->
      <view wx:if="{{!templateSearchExpanded}}" class="collapsed-layout">
        <!--
          搜索图标
        -->
        <view class="search-icon-only" bindtap="onExpandTemplateSearch">
          <t-icon name="search" size="20" class="search-toggle-icon" />
        </view>

        <!--
          操作按钮区域
        -->
        <view class="actions-container">
          <!--
            新建模板按钮
          -->
          <t-button theme="primary" size="small" bindtap="onAddTemplate">
            新建模板
          </t-button>
        </view>
      </view>

      <!--
        展开状态：搜索框占据全部宽度
      -->
      <view wx:else class="expanded-layout">
        <view class="search-input-container">
          <!--
            搜索图标
          -->
          <t-icon name="search" size="16" class="search-icon" />

          <!--
            搜索输入框
          -->
          <input
            class="search-input"
            placeholder="搜索模板名称"
            value="{{templateSearchKeyword}}"
            bindinput="onTemplateSearchInput"
            bindconfirm="onTemplateSearchSubmit"
            bindblur="onTemplateSearchBlur"
            focus="{{templateSearchExpanded}}"
          />

          <!--
            清空搜索图标
          -->
          <t-icon
            wx:if="{{templateSearchKeyword}}"
            name="close-circle-filled"
            size="16"
            class="clear-icon"
            bindtap="onTemplateSearchClear"
          />

          <!--
            收起搜索图标
          -->
          <t-icon
            name="chevron-left"
            size="16"
            class="collapse-icon"
            bindtap="onCollapseTemplateSearch"
          />
        </view>
      </view>
    </view>
  </view>

  <!--
    模板维护内容区域 - 可滚动区域

    条件渲染：只有当topActiveTab为'template'时才显示
    布局：在筛选区域下方，可以滚动
  -->
  <view wx:if="{{topActiveTab === 'template'}}" class="course-content">

    <!--
      模板列表区域

      功能：显示模板数据的主要区域
      设计：照搬course-management的列表设计
      支持多种状态：加载中、空状态、正常列表
    -->
    <view class="course-list">
      <!--
        空状态显示

        条件：visibleTemplateList.length === 0
        组件：t-empty - TDesign的空状态组件
        描述：动态描述文字，根据搜索状态显示不同提示
      -->
      <t-empty wx:if="{{visibleTemplateList.length === 0}}" description="{{templateSearchKeyword ? '未找到匹配的模板' : '暂无模板'}}" />

      <!--
        模板列表 - 滚动模式

        条件渲染：wx:elif="{{visibleTemplateList.length > 0}}"
        只有当有数据时才显示滚动列表

        scroll-view组件：照搬course-management的滚动设计
      -->
      <scroll-view
        wx:elif="{{visibleTemplateList.length > 0}}"
        scroll-y="true"
        style="height: 70vh; min-height: 0;"
        scroll-into-view="{{templateScrollIntoView}}"
      >
        <!--
          模板列表渲染

          block标签：小程序的逻辑容器
          wx:for="{{visibleTemplateList}}": 列表渲染
          wx:key="id": 列表项唯一标识
        -->
        <block wx:for="{{visibleTemplateList}}" wx:key="id">
          <!--
            模板卡片容器

            样式：复用course-card类名，保持与course-management一致的设计
            布局：照搬活动卡片的结构
          -->
          <view class="course-card" data-template="{{item}}" data-template-id="{{item.id}}">
            <!--
              模板卡片头部

              布局：照搬course-management的卡片头部设计
              内容：模板名称、模板标签
            -->
            <view class="course-header">
              <view class="course-title-row">
                <view class="course-title">{{item.name}}</view>
              </view>
              <!--
                模板状态标签

                显示：固定显示"模板"标识
                样式：复用course-status类名，使用template-status样式
              -->
              <view class="course-status template-status">
                模板
              </view>
            </view>

            <!--
              模板信息列表

              功能：显示模板的详细配置信息
              布局：照搬course-management的信息列表设计
              样式：复用course-info-list类名
            -->
            <view class="course-info-list">
              <!--
                总次数信息
              -->
              <view class="info-item">
                <t-icon name="chart" size="16" />
                <text>总次数：{{item.totalTimes}}次</text>
              </view>

              <!--
                有效期信息
              -->
              <view class="info-item">
                <t-icon name="time" size="16" />
                <text>有效期：{{item.validDays}}天</text>
              </view>

              <!--
                首页展示信息
              -->
              <view class="info-item">
                <t-icon name="home" size="16" />
                <text>首页展示：{{item.showOnHomepage ? '是' : '否'}}</text>
              </view>

              <!--
                描述信息

                条件显示：只有当有描述时才显示此项
              -->
              <view class="info-item" wx:if="{{item.description}}">
                <t-icon name="help-circle" size="16" />
                <text>描述：{{item.description}}</text>
              </view>
            </view>

            <!--
              模板操作区域

              功能：提供模板管理的操作按钮
              布局：照搬course-management的操作按钮布局
              样式：复用course-footer和action-buttons类名
            -->
            <view class="course-footer">
              <!--
                操作按钮容器

                布局：照搬course-management的按钮布局
              -->
              <view class="action-buttons">
                <t-button size="small" theme="primary" catchtap="onEditTemplate" data-id="{{item.id}}">编辑</t-button>
                <t-button size="small" theme="danger" catchtap="onDeleteTemplate" data-id="{{item.id}}">删除</t-button>
              </view>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
  </view>
<!-- 新建考勤卡弹窗 -->
<view wx:if="{{showCreateDialog}}" class="custom-dialog-overlay" bind:tap="onCreateDialogClose">
  <view class="custom-dialog" catch:tap="stopPropagation">
    <view class="dialog-header">
      <text class="dialog-title">新建考勤卡</text>
      <t-icon name="close" size="20" bind:tap="onCreateDialogClose" />
    </view>
    <view class="dialog-content">
      <view class="form-row">
        <t-input 
          label="总次数" 
          type="number" 
          placeholder="请输入总次数" 
          value="{{createForm.totalTimes}}" 
          bind:change="onInputChange" 
          data-field="totalTimes" 
        />
      </view>
      <view class="form-row">
        <t-input 
          label="有效期天数" 
          type="number" 
          placeholder="请输入有效期天数" 
          value="{{createForm.validDays}}" 
          bind:change="onValidDaysChange" 
        />
      </view>
      <view class="form-row">
        <t-input 
          label="有效期开始" 
          placeholder="请选择日期" 
          value="{{createForm.validFrom}}" 
          readonly
          suffix-icon="calendar"
          bind:tap="onCreateDatePickerTap"
          data-field="validFrom"
        />
      </view>
      <view class="form-row">
        <t-input 
          label="有效期结束" 
          placeholder="请选择日期" 
          value="{{createForm.validTo}}" 
          readonly
          suffix-icon="calendar"
          bind:tap="onCreateDatePickerTap"
          data-field="validTo"
        />
      </view>

    </view>
    <view class="dialog-footer">
      <t-button theme="default" bind:tap="onCreateDialogClose">取消</t-button>
      <t-button theme="primary" bind:tap="onCreateDialogConfirm" loading="{{creating}}">确认</t-button>
    </view>
  </view>
</view>
  <t-dialog 
  visible="{{showActionDialog}}" 
  title="操作确认" 
  content="{{actionType==='issue' ? '确定要重新绑定该考勤卡吗？有效期将延长一年。' : (actionType==='revoke' ? '确定要解绑该考勤卡吗？解绑后将解除用户关联，考勤卡可重新绑定。' : (actionType==='delay' ? '确定要为该考勤卡延期？' : (actionType==='freeze' ? '确定要冻结该考勤卡吗？冻结后不可用。' : (actionType==='delete' ? '确定要删除该考勤卡吗？此操作不可恢复。' : ''))))}}"
  confirm-btn="确认"
  cancel-btn="取消"
  bind:close="onActionDialogClose" 
  bind:confirm="onActionDialogConfirm"
>
  <block wx:if="{{actionType==='delay'}}">
    <view style="margin-top:12rpx;display:flex;align-items:center;">
      <text>延期天数：</text>
      <t-input style="width:120rpx;" type="number" value="{{delayDays}}" bind:change="onDelayDaysChange" />
      <text>天</text>
    </view>
  </block>
</t-dialog>

<!-- 绑定考勤卡弹窗 -->
<view wx:if="{{showIssueDialog}}" class="custom-dialog-overlay" bind:tap="onIssueDialogClose">
  <view class="custom-dialog" catch:tap="stopPropagation">
    <view class="dialog-header">
      <text class="dialog-title">绑定考勤卡</text>
      <t-icon name="close" size="20" bind:tap="onIssueDialogClose" />
    </view>
    <view class="dialog-content">
      <!-- 用户搜索框 -->
      <view class="form-row">
        <view class="search-input-container">
          <t-icon name="search" size="16" class="search-icon" />
          <input
            class="search-input"
            placeholder="输入用户昵称或openid进行搜索"
            value="{{userSearchKeyword}}"
            bindinput="onUserSearch"
            confirm-type="search"
          />
          <t-icon
            wx:if="{{userSearchKeyword}}"
            name="close-circle-filled"
            size="16"
            class="clear-icon"
            bindtap="onUserSearchClear"
          />
        </view>
      </view>

      <!-- 用户列表 -->
      <view class="user-selector">
        <view wx:if="{{loadingUsers}}" class="loading-container">
          <t-loading theme="circular" size="20px" />
          <text class="loading-text">加载用户列表中...</text>
        </view>

        <view wx:elif="{{filteredUserList.length === 0 && !loadingUsers}}" class="empty-container">
          <t-icon name="user" size="24" color="#ccc" />
          <text class="empty-text">{{userSearchKeyword ? '未找到匹配的用户' : '暂无用户'}}</text>
        </view>

        <view wx:else class="user-list">
          <view
            wx:for="{{filteredUserList}}"
            wx:key="openid"
            class="user-item {{issueForm.selectedUser && issueForm.selectedUser.openid === item.openid ? 'selected' : ''}}"
            bind:tap="onSelectUser"
            data-user="{{item}}"
          >
            <view class="user-avatar">
              <t-avatar
                wx:if="{{item.avatarUrl}}"
                image="{{item.avatarUrl}}"
                size="small"
              />
              <t-icon wx:else name="user" size="20" color="#999" />
            </view>
            <view class="user-info">
              <view class="user-name">{{item.nickName || '未设置昵称'}}</view>
              <view class="user-openid">{{item.openid}}</view>
              <view wx:if="{{item.roles && item.roles.length > 0}}" class="user-roles">
                <text wx:for="{{item.roles}}" wx:key="*this" class="role-tag">{{item}}</text>
              </view>
            </view>
            <view wx:if="{{issueForm.selectedUser && issueForm.selectedUser.openid === item.openid}}" class="selected-icon">
              <t-icon name="check-circle-filled" size="20" color="#0052d9" />
            </view>
          </view>
        </view>
      </view>

      <!-- 选中用户提示 -->
      <view wx:if="{{issueForm.selectedUser}}" class="selected-user-tip">
        <t-icon name="check-circle" size="16" color="#00a870" />
        <text class="tip-text">已选择用户：{{issueForm.selectedUser.nickName || '未设置昵称'}}</text>
      </view>
    </view>
    <view class="dialog-footer">
      <t-button theme="default" bind:tap="onIssueDialogClose">取消</t-button>
      <t-button theme="primary" bind:tap="onIssueDialogConfirm" loading="{{issuing}}">确定绑定</t-button>
    </view>
  </view>
</view>

<!-- 绑定确认弹窗 -->
<view wx:if="{{showIssueConfirmDialog}}" class="custom-dialog-overlay" bind:tap="onIssueConfirmDialogClose">
  <view class="custom-dialog" catch:tap="stopPropagation">
    <view class="dialog-header">
      <text class="dialog-title">确认绑定考勤卡</text>
      <t-icon name="close" size="20" bind:tap="onIssueConfirmDialogClose" />
    </view>
    <view class="dialog-content">
      <view class="confirm-info">
        <view class="info-row">
          <text class="info-label">目标用户：</text>
          <text class="info-value">{{targetUserInfo.nickName || '未知用户'}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">用户openid：</text>
          <text class="info-value openid-text">{{targetUserInfo.openid}}</text>
        </view>
      </view>
      <view class="confirm-tip">
        <t-icon name="help-circle" size="16" color="#999" />
        <text class="tip-text">确认要将考勤卡绑定给该用户吗？</text>
      </view>
    </view>
    <view class="dialog-footer">
      <t-button theme="default" bind:tap="onIssueConfirmDialogClose">取消</t-button>
      <t-button theme="primary" bind:tap="onIssueConfirmDialogConfirm" loading="{{issuing}}">确认绑定</t-button>
    </view>
  </view>
</view>

<!-- 解绑确认弹窗 -->
<view wx:if="{{showRevokeConfirmDialog}}" class="custom-dialog-overlay" bind:tap="onRevokeConfirmDialogClose">
  <view class="custom-dialog" catch:tap="stopPropagation">
    <view class="dialog-header">
      <text class="dialog-title">确认解绑考勤卡</text>
      <t-icon name="close" size="20" bind:tap="onRevokeConfirmDialogClose" />
    </view>
    <view class="dialog-content">
      <view class="confirm-info">
        <view class="info-row">
          <text class="info-label">考勤卡号：</text>
          <text class="info-value">{{revokeCardInfo.cardNumber}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">当前用户：</text>
          <text class="info-value">{{revokeCardInfo.userNickName || '未知用户'}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">绑定日期：</text>
          <text class="info-value">{{revokeCardInfo.issueDate}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">剩余次数：</text>
          <text class="info-value">{{revokeCardInfo.remainingTimes}}/{{revokeCardInfo.totalTimes}}</text>
        </view>
      </view>
      <view class="confirm-tip revoke-tip">
        <t-icon name="help-circle" size="16" color="#fa8c16" />
        <text class="tip-text">解绑后将解除用户关联，考勤卡可重新绑定给其他用户</text>
      </view>
    </view>
    <view class="dialog-footer">
      <t-button theme="default" bind:tap="onRevokeConfirmDialogClose">取消</t-button>
      <t-button theme="danger" bind:tap="onRevokeConfirmDialogConfirm" loading="{{issuing}}">确认解绑</t-button>
    </view>
  </view>
</view>

<!-- 编辑考勤卡弹窗 -->
<view wx:if="{{showEditDialog}}" class="custom-dialog-overlay" bind:tap="onEditDialogClose">
  <view class="custom-dialog" catch:tap="stopPropagation">
    <view class="dialog-header">
      <text class="dialog-title">编辑考勤卡</text>
      <t-icon name="close" size="20" bind:tap="onEditDialogClose" />
    </view>
    <view class="dialog-content">
      <!-- 使用情况提示 -->
      <view wx:if="{{editCardUsageInfo}}" class="usage-info-tip">
        <t-icon name="info-circle" size="16" color="#0052d9" />
        <text class="tip-text">当前状态：{{editCardUsageInfo}}</text>
      </view>

      <view class="form-row">
        <t-input
          label="总次数"
          type="number"
          placeholder="请输入总次数"
          value="{{editForm.totalTimes}}"
          bind:change="onEditInputChange"
          data-field="totalTimes"
        />
      </view>

      <!-- 总次数限制提示 -->
      <view wx:if="{{editCardMinTotalTimes > 1}}" class="form-tip">
        <t-icon name="help-circle" size="16" color="#ff9500" />
        <text class="tip-text">注意：总次数不能小于已使用次数（{{editCardMinTotalTimes}}次）</text>
      </view>
      <view class="form-row">
        <t-input 
          label="有效期天数" 
          type="number" 
          placeholder="请输入有效期天数" 
          value="{{editForm.validDays}}" 
          bind:change="onEditValidDaysChange" 
        />
      </view>
      <view class="form-row">
        <t-input 
          label="有效期开始" 
          placeholder="请选择日期" 
          value="{{editForm.validFrom}}" 
          readonly
          suffix-icon="calendar"
          bind:tap="onEditDatePickerTap"
          data-field="validFrom"
        />
      </view>
      <view class="form-row">
        <t-input 
          label="有效期结束" 
          placeholder="请选择日期" 
          value="{{editForm.validTo}}" 
          readonly
          suffix-icon="calendar"
          bind:tap="onEditDatePickerTap"
          data-field="validTo"
        />
      </view>
    </view>
    <view class="dialog-footer">
      <t-button theme="default" bind:tap="onEditDialogClose">取消</t-button>
      <t-button theme="primary" bind:tap="onEditDialogConfirm" loading="{{editing}}">确认</t-button>
    </view>
  </view>
</view>

<!-- 创建考勤卡日期选择器 -->
<t-date-time-picker
  title="{{createDatePickerField === 'validFrom' ? '选择有效期开始日期' : '选择有效期结束日期'}}"
  visible="{{createDatePickerVisible}}"
  mode="date"
  value="{{createDatePickerValue}}"
  format="YYYY-MM-DD"
  show-week="{{true}}"
  bindchange="onCreateDatePickerConfirm"
  bindcancel="onCreateDatePickerCancel"
/>

<!-- 编辑考勤卡日期选择器 -->
<t-date-time-picker
  title="{{editDatePickerField === 'validFrom' ? '选择有效期开始日期' : '选择有效期结束日期'}}"
  visible="{{editDatePickerVisible}}"
  mode="date"
  value="{{editDatePickerValue}}"
  format="YYYY-MM-DD"
  show-week="{{true}}"
  bindchange="onEditDatePickerConfirm"
  bindcancel="onEditDatePickerCancel"
/>

  <!-- 新建模板弹窗 -->
  <view wx:if="{{showCreateTemplateDialog}}" class="custom-dialog-overlay" bind:tap="onCreateTemplateDialogClose">
    <view class="custom-dialog" catch:tap="stopPropagation">
      <view class="dialog-header">
        <text class="dialog-title">新建模板</text>
        <t-icon name="close" size="20" bind:tap="onCreateTemplateDialogClose" />
      </view>
      <view class="dialog-content">
        <view class="form-row">
          <t-input 
            label="模板名称" 
            placeholder="请输入模板名称" 
            value="{{createTemplateForm.name}}" 
            bind:change="onCreateTemplateInputChange" 
            data-field="name" 
          />
        </view>
        <view class="form-row">
          <t-input 
            label="总次数" 
            type="number" 
            placeholder="请输入总次数" 
            value="{{createTemplateForm.totalTimes}}" 
            bind:change="onCreateTemplateInputChange" 
            data-field="totalTimes" 
          />
        </view>
        <view class="form-row">
          <t-input 
            label="有效期天数" 
            type="number" 
            placeholder="请输入有效期天数" 
            value="{{createTemplateForm.validDays}}" 
            bind:change="onCreateTemplateInputChange" 
            data-field="validDays" 
          />
        </view>
        <view class="form-row">
          <view class="switch-container">
            <text class="switch-label">是否在首页展示</text>
            <t-switch 
              value="{{createTemplateForm.showOnHomepage}}" 
              bind:change="onCreateTemplateSwitchChange"
            />
          </view>
        </view>
        <view class="form-row">
          <t-textarea 
            label="描述" 
            placeholder="请输入模板描述（可选）" 
            value="{{createTemplateForm.description}}" 
            bind:change="onCreateTemplateInputChange" 
            data-field="description"
            maxlength="200"
            indicator
          />
        </view>
      </view>
      <view class="dialog-footer">
        <t-button theme="default" bind:tap="onCreateTemplateDialogClose">取消</t-button>
        <t-button theme="primary" bind:tap="onCreateTemplateDialogConfirm" loading="{{creatingTemplate}}">确认</t-button>
      </view>
    </view>
  </view>

  <!-- 编辑模板弹窗 -->
  <view wx:if="{{showEditTemplateDialog}}" class="custom-dialog-overlay" bind:tap="onEditTemplateDialogClose">
    <view class="custom-dialog" catch:tap="stopPropagation">
      <view class="dialog-header">
        <text class="dialog-title">编辑模板</text>
        <t-icon name="close" size="20" bind:tap="onEditTemplateDialogClose" />
      </view>
      <view class="dialog-content">
        <view class="form-row">
          <t-input 
            label="模板名称" 
            placeholder="请输入模板名称" 
            value="{{editTemplateForm.name}}" 
            bind:change="onEditTemplateInputChange" 
            data-field="name" 
          />
        </view>
        <view class="form-row">
          <t-input 
            label="总次数" 
            type="number" 
            placeholder="请输入总次数" 
            value="{{editTemplateForm.totalTimes}}" 
            bind:change="onEditTemplateInputChange" 
            data-field="totalTimes" 
          />
        </view>
        <view class="form-row">
          <t-input 
            label="有效期天数" 
            type="number" 
            placeholder="请输入有效期天数" 
            value="{{editTemplateForm.validDays}}" 
            bind:change="onEditTemplateInputChange" 
            data-field="validDays" 
          />
        </view>
        <view class="form-row">
          <view class="switch-container">
            <text class="switch-label">是否在首页展示</text>
            <t-switch 
              value="{{editTemplateForm.showOnHomepage}}" 
              bind:change="onEditTemplateSwitchChange"
            />
          </view>
        </view>
        <view class="form-row">
          <t-textarea 
            label="描述" 
            placeholder="请输入模板描述（可选）" 
            value="{{editTemplateForm.description}}" 
            bind:change="onEditTemplateInputChange" 
            data-field="description"
            maxlength="200"
            indicator
          />
        </view>
      </view>
      <view class="dialog-footer">
        <t-button theme="default" bind:tap="onEditTemplateDialogClose">取消</t-button>
        <t-button theme="primary" bind:tap="onEditTemplateDialogConfirm" loading="{{editingTemplate}}">确认</t-button>
      </view>
    </view>
  </view>



<!-- 轻提示组件 -->
<t-toast id="t-toast" />
</view> 