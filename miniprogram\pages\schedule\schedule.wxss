/* schedule.wxss */
/*
 * 课程表页面样式文件 - 统一rpx响应式单位版本
 *
 * 设计规范：
 * 1. 字体：统一使用苹方字体 (PingFang SC)
 * 2. 尺寸单位：【已重构】全面采用rpx作为主要尺寸单位，确保在不同宽度的屏幕上布局能等比缩放。
 *    - px单位仅用于需要固定像素的场景，如最细的1px边框线。
 * 3. 字体大小：统一使用rpx，并遵循三级标准
 *    - 大字体: 32rpx (主要标题、重要信息)
 *    - 中字体: 28rpx (正文、功能项)
 *    - 小字体: 24rpx (辅助信息、标签)
 * 4. 响应式适配：通过rpx实现基础响应式，通过@media查询对特殊屏幕尺寸进行微调。
 * 5. 固定头部滚动：顶部固定，内容区域滚动
 */

/* ==================== 全局样式和工具类 ==================== */

/* 统一字体 */
.pingfang-font {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 标准字体大小 */
.font-large { font-size: 32rpx; }   /* 大字体 - 主要标题 */
.font-medium { font-size: 28rpx; }  /* 中字体 - 正文内容 */
.font-small { font-size: 24rpx; }   /* 小字体 - 辅助信息 */


/*
 * 页面根元素样式
 * 设置页面的基础高度和渐变背景
 */
page {
  height: 100%;
  /* 保留渐变背景 */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  background-attachment: fixed;
}

/*
 * 页面容器样式
 * 确保页面容器占满整个可用高度
 */
.page {
  height: 100%;
  background: transparent;
}

/**
 * .container: 页面根容器样式
 *
 * 设计说明：
 * - 使用较小的内边距(24rpx)，为列表内容留出更多空间
 * - 底部预留tabBar和安全区域空间
 * - 统一的字体族设置
 *
 * 与profile页面的差异：
 * - profile页面：32rpx内边距，适合卡片式布局
 * - schedule页面：24rpx内边距，适合列表式布局
 */
.container {
  /*
   * 固定布局设置
   * 禁止根容器滚动，让顶部区域固定，只有内容区域滚动
   */
  height: 100%; /* 占满父容器高度 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 禁止根容器滚动 */

  /* 内边距：【已修改】12px -> 24rpx */
  padding: 24rpx;

  /*
   * 底部内边距：为tabBar和安全区域预留空间
   * calc(24rpx + 120rpx + env(safe-area-inset-bottom))
   * - 24rpx: 【已修改】与顶部内边距保持一致
   * - 120rpx: tabBar高度
   * - env(safe-area-inset-bottom): 底部安全区域
   */
  padding-bottom: calc(24rpx + 120rpx + env(safe-area-inset-bottom));

  /* 透明背景，让page的渐变背景显示 */
  background: transparent;

  /* 统一字体族：与整个应用保持一致 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /* 盒模型：padding包含在总宽度内 */
  box-sizing: border-box;
}

/**
 * 视图切换栏样式
 *
 * 功能说明：
 * 包含Tab切换按钮和搜索框的容器
 * 为页面的主要交互区域提供布局基础
 */

/**
 * .view-section: 视图切换区域容器
 *
 * 布局特点：
 * - 100%宽度：占满父容器
 * - 无底部内边距：与下方内容紧密连接
 * - 统一字体：保持文字样式一致性
 */
.view-section {
  width: 100%;                      /* 宽度：占满父容器 */
  padding-bottom: 0;              /* 【已修改】0px -> 0 */
  /* 字体族：继承统一的字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;                   /* 防止视图切换区域被压缩 */
}

/**
 * .filter-section: 筛选区域容器
 *
 * 预留样式：
 * 为未来可能的筛选功能预留的样式定义
 * 目前与view-section样式相同
 */
.filter-section {
  width: 100%;                      /* 宽度：占满父容器 */
  padding-bottom: 0;              /* 【已修改】0px -> 0 */
  /* 字体族：统一字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * Tab切换相关样式
 *
 * 设计原理：
 * 1. 使用Flexbox实现等宽分布
 * 2. 圆角背景增强视觉层次
 * 3. 内边距提供点击区域
 * 4. 状态切换通过CSS类名控制
 */

/**
 * 顶部选项卡区域样式 - 与course-management页面保持一致
 *
 * 功能：包含主选项卡组件的容器
 * 布局：优雅的线条式设计，增强视觉层次感
 * 设计理念：从简单的booking-tabs升级为专业的TDesign选项卡
 */
.top-tabs-section {
  position: relative;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  /* 【已修改】1px -> 1rpx */
  border: 1rpx solid #f0f0f0;
  /* 【已修改】8px -> 16rpx */
  border-radius: 16rpx;
  box-shadow:
    0 2rpx 6rpx rgba(0, 0, 0, 0.02),
    0 0 2rpx rgba(0, 0, 0, 0.01);
  /* 【已修改】padding: 0 16px 4px; -> padding: 0 32rpx 8rpx; */
  padding: 0 32rpx 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* 【已修改】1px -> 1rpx */
  border-bottom: 1rpx solid #e7e7e7;
  z-index: 10;
}

/**
 * 自定义线条选项卡样式 - 与course-management页面完全一致
 *
 * 功能：覆盖TDesign组件的默认样式
 * 目的：实现优雅的线条选项卡设计
 */
.custom-top-tabs {
  background-color: transparent;
  border: none;
  border-radius: 0;
  overflow: visible;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 28rpx;
  margin: 0;
  width: 100%;
  height: auto;
  /* 【已修改】1px -> 1rpx */
  min-height: 1rpx;
}

/**
 * TDesign线条选项卡导航区域样式
 */
.custom-top-tabs .t-tabs__nav {
  padding: 0;
  height: auto;
  /* 【已修改】1px -> 1rpx */
  min-height: 1rpx;
  border-bottom: none;
  display: flex;
  align-items: center;
  background: transparent;
}

/**
 * 线条选项卡项目样式
 */
.custom-top-tabs .t-tabs__item {
  font-size: 28rpx !important;
  font-weight: 500;
  /* 【已修改】padding: 14px 20px !important; -> padding: 28rpx 40rpx !important; */
  padding: 28rpx 40rpx !important;
  height: auto;
  line-height: 1.4;
  /* 【已修改】44px -> 88rpx */
  min-height: 88rpx;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 0;
  background: transparent !important;
  /* 【已修改】3px -> 6rpx */
  border-bottom: 6rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #666666 !important;
  position: relative;
}

/*
 * 选项卡项目的装饰效果
 */
.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  /* 【已修改】2px -> 4rpx */
  height: 4rpx;
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/**
 * 激活状态的线条选项卡
 */
.custom-top-tabs .t-tabs__item--active {
  color: #0052d9 !important;
  font-weight: 600 !important;
  border-bottom-color: #0052d9 !important;
  background: transparent !important;
  /* 【已修改】1px -> 1rpx */
  text-shadow: 0 0 2rpx rgba(0, 82, 217, 0.1);
}

/*
 * 选项卡项目的装饰效果
 */
.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  /* 【已修改】2px -> 4rpx */
  height: 4rpx;
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/*
 * 激活状态的顶部装饰线
 */
.custom-top-tabs .t-tabs__item--active::before {
  width: 60%; /* 激活时显示顶部装饰线 */
}

/**
 * 非激活状态的选项卡悬停效果
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover {
  color: #333333 !important;
  background: transparent !important;
  border-bottom-color: rgba(0, 82, 217, 0.3) !important;
  /* 【已修改】1px -> 1rpx */
  text-shadow: 0 0 2rpx rgba(51, 51, 51, 0.1);
}

/*
 * 悬停时的顶部装饰线
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover::before {
  width: 30%; /* 悬停时显示较短的顶部装饰线 */
}

/**
 * 底部指示线容器
 */
.custom-top-tabs .t-tabs__track {
  display: none; /* 隐藏默认的滑动指示器，使用border-bottom代替 */
}

/**
 * 搜索区域样式 - 与上方t-tabs协调设计
 *
 * 功能：包含搜索框的区域容器
 * 设计：与t-tabs的样式风格保持一致，形成统一的视觉语言
 */
.search-section {
  /* 【已修改】12px -> 24rpx */
  margin-top: 24rpx;
  /* 【已修改】12px -> 24rpx */
  margin-bottom: 24rpx;
}

/**
 * 搜索和操作区域样式 - 与t-tabs风格协调
 */
.search-actions-section {
  width: 100%;
  display: flex;
  align-items: center;
  /* 【已修改】8px -> 16rpx */
  gap: 16rpx;
  flex-wrap: nowrap;
  justify-content: flex-start;
  box-sizing: border-box;
  overflow: visible;
  /* 【已修改】44px -> 88rpx */
  min-height: 88rpx;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  /* 【已修改】1px -> 1rpx */
  border: 1rpx solid #f0f0f0;
  /* 【已修改】8px -> 16rpx */
  border-radius: 16rpx;
  box-shadow:
    0 2rpx 6rpx rgba(0, 0, 0, 0.02),
    0 0 2rpx rgba(0, 0, 0, 0.01);
  /* 【已修改】6px 16px -> 12rpx 32rpx */
  padding: 12rpx 32rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/**
 * 展开状态布局 - 与course-management页面完全一致
 */
.search-actions-section.expanded {
  justify-content: flex-start;
}

.expanded-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex: 1; /* 占据search-actions-section的全部空间 */
}

/**
 * 展开的搜索输入框 - 与t-tabs风格协调
 */
.search-input-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  /* 【已修改】6px -> 12rpx */
  border-radius: 12rpx;
  /* 【已修改】8px 12px -> 16rpx 24rpx */
  padding: 16rpx 24rpx;
  /* 【已修改】1px -> 1rpx */
  border: 1rpx solid #e7e7e7;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: searchExpand 0.3s ease-out;
  width: 100%;
  flex: 1;
  box-sizing: border-box;
  /* 【已修改】36px -> 72rpx */
  height: 72rpx;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.02);
}

.search-input-container:focus-within {
  border-color: #0052d9;
  box-shadow:
    0 0 0 4rpx rgba(0, 82, 217, 0.1),
    inset 0 2rpx 4rpx rgba(0, 0, 0, 0.02);
}

.search-icon {
  color: #0052d9;
  /* 【已修改】8px -> 16rpx */
  margin-right: 16rpx;
  flex-shrink: 0;
  font-size: 28rpx;
  opacity: 0.8;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 28rpx;
  color: #333333;
  background: transparent;
  min-width: 0;
  /* 【已修改】20px -> 40rpx */
  height: 40rpx;
  /* 【已修改】20px -> 40rpx */
  line-height: 40rpx;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-weight: 400;
}

.search-input::placeholder {
  color: #999999;
  font-size: 28rpx;
  font-weight: 400;
}

.clear-icon {
  color: #999999;
  /* 【已修改】8px -> 16rpx */
  margin-left: 16rpx;
  flex-shrink: 0;
  cursor: pointer;
  font-size: 28rpx;
  /* 【已修改】4px -> 8rpx */
  padding: 8rpx;
  /* 【已修改】4px -> 8rpx */
  border-radius: 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.8;
}

.clear-icon:active {
  color: #0052d9;
  background: rgba(0, 82, 217, 0.1);
  opacity: 1;
  transform: scale(0.95);
}

/**
 * 搜索展开动画 - 与course-management页面完全一致
 */
@keyframes searchExpand {
  from {
    opacity: 0;
    transform: scaleX(0.8);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

/* 日期选择器样式 */
.date-section {
  /* 【已修改】12px -> 24rpx */
  margin-bottom: 24rpx;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;
}

.date-tabs-scroll {
  display: flex;
  flex-direction: row;
  white-space: nowrap;
  overflow-x: auto;
  /* 【已修改】4px 0 6px 0 -> 8rpx 0 12rpx 0 */
  padding: 8rpx 0 12rpx 0;
  flex-shrink: 0;
  background: #fff;
  /* 【已修改】8px -> 16rpx */
  border-radius: 16rpx;
  /* 【已修改】4px 0 -> 8rpx 0 */
  margin: 8rpx 0;
  flex-shrink: 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.date-tabs-scroll::-webkit-scrollbar {
  display: none;
}

/*
 * 通用滚动条美化样式
 * 适用于所有scroll-view组件
 */

/* 所有scroll-view的垂直滚动条 */
scroll-view::-webkit-scrollbar {
  /* 【已修改】6px -> 12rpx */
  width: 12rpx;
}

scroll-view::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  /* 【已修改】3px -> 6rpx */
  border-radius: 6rpx;
  /* 【已修改】8px 0 -> 16rpx 0 */
  margin: 16rpx 0;
}

scroll-view::-webkit-scrollbar-thumb {
  background: rgba(0, 82, 217, 0.3);
  /* 【已修改】3px -> 6rpx */
  border-radius: 6rpx;
}

scroll-view::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 82, 217, 0.6);
}

scroll-view::-webkit-scrollbar-thumb:active {
  background: rgba(0, 82, 217, 0.8);
}

/* 所有scroll-view的水平滚动条 */
scroll-view::-webkit-scrollbar:horizontal {
  /* 【已修改】4px -> 8rpx */
  height: 8rpx;
}

scroll-view::-webkit-scrollbar-track:horizontal {
  background: rgba(0, 0, 0, 0.05);
  /* 【已修改】2px -> 4rpx */
  border-radius: 4rpx;
  /* 【已修改】0 8px -> 0 16rpx */
  margin: 0 16rpx;
}

scroll-view::-webkit-scrollbar-thumb:horizontal {
  background: rgba(0, 82, 217, 0.3);
  /* 【已修改】2px -> 4rpx */
  border-radius: 4rpx;
}

scroll-view::-webkit-scrollbar-thumb:horizontal:hover {
  background: rgba(0, 82, 217, 0.6);
}

scroll-view::-webkit-scrollbar-thumb:horizontal:active {
  background: rgba(0, 82, 217, 0.8);
}

/* 滚动条角落 */
scroll-view::-webkit-scrollbar-corner {
  background: transparent;
}
.date-tab {
  display: inline-block;
  /* 【已修改】50px -> 100rpx */
  width: 100rpx;
  text-align: center;
  /* 【已修改】8px -> 16rpx */
  margin-right: 16rpx;
  /* 【已修改】6px 0 0 0 -> 12rpx 0 0 0 */
  padding: 12rpx 0 0 0;
  cursor: pointer;
  /* 【已修改】38px -> 76rpx */
  min-height: 76rpx;
}
.date-tab:last-child {
  margin-right: 0;
}
.date-tab .tab-label {
  display: block;
  /* 【已修改】18px -> 36rpx */
  height: 36rpx;
  /* 【已修改】18px -> 36rpx */
  line-height: 36rpx;
}
.date-tab .tab-date {
  display: block;
  font-size: 24rpx;
  /* 【已修改】2px -> 4rpx */
  margin-top: 4rpx;
  /* 【已修改】16px -> 32rpx */
  height: 32rpx;
  /* 【已修改】16px -> 32rpx */
  line-height: 32rpx;
  color: #bbb;
}


/* 课程列表样式 - 使用原生滚动，彻底解决滚动响应性问题 */
.course-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  height: 0;
  min-height: 0;
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* 隐藏滚动条但保持滚动功能 */
.course-list::-webkit-scrollbar {
  width: 0; /* 【已修改】0px -> 0 */
  background: transparent;
}

.course-list::-webkit-scrollbar-track {
  background: transparent;
}

.course-list::-webkit-scrollbar-thumb {
  background: transparent;
}

.course-list::-webkit-scrollbar-corner {
  background: transparent;
}

/* 时间轴样式 - 参考course-management */
.timeline-date {
  /* 【已修改】16px 0 8px 0 -> 32rpx 0 16rpx 0 */
  margin: 32rpx 0 16rpx 0;
  font-size: 28rpx;
  color: #0052d9;
  font-weight: bold;
  text-align: left;
  position: relative;
  /* 【已修改】16px -> 32rpx */
  padding-left: 32rpx;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.timeline-date::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  /* 【已修改】8px -> 16rpx */
  width: 16rpx;
  /* 【已修改】8px -> 16rpx */
  height: 16rpx;
  background: #0052d9;
  border-radius: 50%;
  transform: translateY(-50%);
}

.course-card {
  background-color: #ffffff;
  /* 【已修改】12px -> 24rpx */
  border-radius: 24rpx;
  /* 【已修改】16px -> 32rpx */
  padding: 32rpx;
  /* 【已修改】16px -> 32rpx */
  margin-bottom: 32rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;
  pointer-events: auto;
  touch-action: manipulation;
}

/* 新加载卡片的优雅提示动画 - 方案1：滑入 + 顶部指示条 */
.course-card.new-loaded {
  animation: slide-in-up 0.5s ease-out;
  position: relative;
}

.course-card.new-loaded::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  /* 【已修改】3px -> 6rpx */
  height: 6rpx;
  background: linear-gradient(90deg, #0052d9, #40a9ff, #0052d9);
  /* 【已修改】12px 12px 0 0 -> 24rpx 24rpx 0 0 */
  border-radius: 24rpx 24rpx 0 0;
  animation: new-content-indicator 2s ease-out;
}

/* 方案2：仅滑入动画（更简洁）*/
.course-card.slide-in {
  animation: slide-in-up 0.6s ease-out;
}

/* 方案3：淡入 + 轻微缩放 */
.course-card.fade-in {
  animation: fade-in-scale 0.7s ease-out;
}

/* 从下方滑入的动画 */
@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(40rpx); /* 20px -> 40rpx */
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 淡入缩放动画 */
@keyframes fade-in-scale {
  0% {
    opacity: 0;
    transform: scale(0.95) translateY(20rpx); /* 10px -> 20rpx */
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 顶部蓝色指示条动画 */
@keyframes new-content-indicator {
  0% {
    opacity: 1;
    background: linear-gradient(90deg, #0052d9, #40a9ff, #0052d9);
  }
  70% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* 【已修改】12px -> 24rpx */
  margin-bottom: 24rpx;
}

.course-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.course-status {
  /* 【已修改】4px 8px -> 8rpx 16rpx */
  padding: 8rpx 16rpx;
  /* 【已修改】4px -> 8rpx */
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;
  white-space: nowrap;
}

.course-status.available {
  background-color: #e8f5e8;
  color: #52c41a;
}

.course-status.booked {
  background-color: #e6f3ff;
  color: #0052d9;
}

.course-status.in-progress {
  background-color: #fff7e6;
  color: #d46b08;
}

.course-status.full {
  background-color: #fff2e8;
  color: #fa8c16;
}

.course-status.ended {
  background-color: #f0f0f0;
  color: #888;
}

.course-date {
  font-size: 28rpx;
  color: #0052d9;
  /* 【已修改】2px -> 4rpx */
  margin-top: 4rpx;
  /* 【已修改】2px -> 4rpx */
  margin-bottom: 4rpx;
  font-weight: 500;
}

.course-info-list {
  /* 【已修改】12px -> 24rpx */
  margin-bottom: 24rpx;
}

.info-item {
  display: flex;
  align-items: center;
  /* 【已修改】8px -> 16rpx */
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item t-icon {
  /* 【已修改】8px -> 16rpx */
  margin-right: 16rpx;
  color: #0052d9;
}

.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* 【已修改】10px -> 20rpx */
  padding-top: 20rpx;
  /* 【已修改】1px -> 1rpx */
  border-top: 1rpx solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 操作按钮容器样式 */
.action-buttons {
  display: flex;
  align-items: center;
  /* 【已修改】8px -> 16rpx */
  gap: 16rpx;
  justify-content: flex-end;
  margin-left: auto;
}

/* 统一 t-button 字体大小，保证禁用按钮和可预约按钮一致 */
.t-button {
  font-size: 28rpx !important;
}

/* 加载指示器样式 - 参考course-management */
.loading-indicator {
  text-align: center;
  color: #888;
  font-size: 28rpx;
  /* 【已修改】16px 0 8px 0 -> 32rpx 0 16rpx 0 */
  padding: 32rpx 0 16rpx 0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /* 【已修改】8px -> 16rpx */
  gap: 16rpx;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  /* 【已修改】4px -> 8rpx */
  gap: 8rpx;
}

.loading-dot {
  /* 【已修改】6px -> 12rpx */
  width: 12rpx;
  /* 【已修改】6px -> 12rpx */
  height: 12rpx;
  border-radius: 50%;
  background-color: #0052d9;
  animation: loading-dot-bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

.end-indicator {
  text-align: center;
  color: #b0b0b0;
  font-size: 28rpx;
  /* 【已修改】12px 0 8px 0 -> 24rpx 0 16rpx 0 */
  padding: 24rpx 0 16rpx 0;
  /* 【已修改】1px -> 2rpx */
  letter-spacing: 2rpx;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  position: relative;
  opacity: 0.8;
}

.end-indicator::before,
.end-indicator::after {
  content: '';
  position: absolute;
  top: 50%;
  /* 【已修改】60px -> 120rpx */
  width: 120rpx;
  /* 【已修改】1px -> 1rpx */
  height: 1rpx;
  background: linear-gradient(to right, transparent, #d0d0d0, transparent);
}

.end-indicator::before {
  /* 【已修改】20px -> 40rpx */
  left: 40rpx;
}

.end-indicator::after {
  /* 【已修改】20px -> 40rpx */
  right: 40rpx;
}

/* 加载动画 */
@keyframes loading-dot-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 骨架屏样式 */
.skeleton-container {
  /* 【已修改】16px -> 32rpx */
  padding: 32rpx;
}

.skeleton-item {
  background-color: #f0f0f0;
  /* 【已修改】8px -> 16rpx */
  border-radius: 16rpx;
  /* 【已修改】16px -> 32rpx */
  margin-bottom: 32rpx;
}

.skeleton-header {
  /* 【已修改】24px -> 48rpx */
  height: 48rpx;
  width: 70%;
  /* 【已修改】12px -> 24rpx */
  margin-bottom: 24rpx;
}

.skeleton-info {
  /* 【已修改】16px -> 32rpx */
  height: 32rpx;
  width: 90%;
  /* 【已修改】8px -> 16rpx */
  margin-bottom: 16rpx;
}

.skeleton-info-last {
  width: 60%;
}

.skeleton-footer {
  /* 【已修改】32px -> 64rpx */
  height: 64rpx;
  /* 【已修改】80px -> 160rpx */
  width: 160rpx;
  /* 【已修改】12px -> 24rpx */
  margin-top: 24rpx;
  float: right;
}

/* 响应式布局调整 */
/* 针对小屏幕进行优化，确保标签栏在不同尺寸下都能正确显示 */
@media (max-width: 390px) {
  .custom-top-tabs .t-tabs__item {
    /* 【已修改】12px 14px -> 24rpx 28rpx */
    padding: 24rpx 28rpx !important;
    font-size: 26rpx !important;
    /* 【已修改】42px -> 84rpx */
    min-height: 84rpx;
  }
}

@media (max-width: 375px) {
  .container {
    /* 【已修改】8px -> 16rpx */
    padding: 16rpx;
    /* 【已修改】8px -> 16rpx */
    padding-bottom: calc(16rpx + 120rpx + env(safe-area-inset-bottom));
  }

  .course-card {
    /* 【已修改】12px -> 24rpx */
    padding: 24rpx;
  }

  .course-title {
    font-size: 30rpx;
  }

  .info-item {
    font-size: 26rpx;
  }

  .custom-top-tabs .t-tabs__item {
    /* 【已修改】12px 12px -> 24rpx 24rpx */
    padding: 24rpx 24rpx !important;
    font-size: 26rpx !important;
  }
}
