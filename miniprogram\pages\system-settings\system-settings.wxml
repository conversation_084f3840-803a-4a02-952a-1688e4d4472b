<!--system-settings.wxml-->
<!-- High-end Redesign -->
<view class="container">
  <!-- 页面内容区域 -->
  <view class="content-wrapper">
    <!-- 预约取消时间设置卡片 -->
  <view class="setting-card">
    <view class="card-header">
      <t-icon name="time" size="24" />
      <text class="card-title">预约取消时间设置</text>
    </view>
    <view class="card-body">
      <view class="setting-item">
        <view class="setting-label">当前设置</view>
        <view class="time-setting-value">
          <text class="time-value">{{formattedTime}}</text>
          <text class="time-unit">{{timeUnit}}</text>
        </view>
      </view>
      <view class="input-row">
        <text class="input-label">活动开始前</text>
        <t-input
          type="number"
          placeholder="分钟"
          value="{{cancelTimeLimit}}"
          bind:change="onCancelTimeChange"
          bind:blur="onCancelTimeBlur"
          class="time-input"
        />
        <text class="input-suffix">分钟内不可取消</text>
      </view>
      <view class="setting-description">
        设置学员可在活动开始前多少分钟取消预约。例如，输入180，则表示活动开始前3小时内无法取消。
      </view>
    </view>
  </view>

  <!-- 联系信息设置卡片 -->
  <view class="setting-card">
    <view class="card-header">
      <t-icon name="call" size="24" />
      <text class="card-title">联系信息设置</text>
    </view>
    <view class="card-body">
      <view class="setting-item">
        <view class="setting-label">联系电话</view>
        <view class="setting-input">
          <t-input
            placeholder="请输入联系电话"
            value="{{contactPhone}}"
            bind:change="onContactPhoneChange"
            bind:blur="onContactPhoneBlur"
            class="contact-input"
            align="right"
          />
        </view>
      </view>
      <view class="setting-item">
        <view class="setting-label">门店地址</view>
        <view class="setting-input">
          <t-input
            placeholder="请输入门店地址"
            value="{{contactAddress}}"
            bind:change="onContactAddressChange"
            bind:blur="onContactAddressBlur"
            class="contact-input"
            align="right"
          />
        </view>
      </view>
      <!-- 多条公告编辑区域 -->
      <view class="setting-item">
        <view class="setting-label">门店公告</view>
        <view class="add-announcement-btn" bind:tap="onAddAnnouncement">
          <t-icon name="add" size="20" />
          <text>新增公告</text>
        </view>
      </view>

      <!-- 公告列表 -->
      <view class="announcements-container">
        <!-- 空状态提示 -->
        <view wx:if="{{announcements.length === 0}}" class="empty-announcements">
          <t-icon name="notification" size="48" color="#ddd" />
          <text class="empty-text">暂无公告，点击上方"新增公告"添加</text>
        </view>

        <!-- 公告列表 -->
        <view wx:else class="announcements-list">
          <view
            wx:for="{{announcements}}"
            wx:key="id"
            wx:for-item="announcement"
            wx:for-index="index"
            class="announcement-wrapper"
          >
            <!-- 公告内容 -->
              <view class="announcement-item">
                <view class="announcement-header">
                  <view class="announcement-order">第{{index + 1}}条</view>
                  <!-- 状态标签改为可点击，用于切换启用/禁用状态 -->
                  <view
                    class="announcement-status {{announcement.isActive ? 'active' : 'inactive'}} clickable"
                    data-index="{{index}}"
                    catchtap="onToggleAnnouncementStatus"
                  >
                    {{announcement.isActive ? '启用' : '禁用'}}
                  </view>
                </view>

                <!-- 非编辑状态：显示为只读文本，点击可进入编辑模式 -->
                <view
                  wx:if="{{announcement.editMode !== true}}"
                  class="announcement-content-display"
                  data-index="{{index}}"
                  catchtap="onEditAnnouncement"
                >
                  <view class="announcement-text-display">{{announcement.content || '请输入公告内容'}}</view>
                </view>

                <!-- 编辑状态：显示为可编辑文本框 -->
                <t-textarea
                  wx:else
                  placeholder="请输入公告内容"
                  value="{{announcement.content}}"
                  bind:change="onAnnouncementContentChange"
                  bind:blur="onAnnouncementContentBlur"
                  data-index="{{index}}"
                  class="announcement-textarea editing"
                  maxlength="500"
                  autosize
                  focus="{{true}}"
                />

                <view class="announcement-meta">
                  <text class="announcement-time">{{announcement.updateTime}}</text>
                  <text class="announcement-length">{{announcement.content.length}}/500</text>
                </view>

                <!-- 编辑模式下的浮动操作按钮 -->
                <view wx:if="{{announcement.editMode === true}}" class="edit-floating-actions">
                  <view
                    class="floating-btn delete-btn"
                    data-index="{{index}}"
                    catchtap="onDeleteAnnouncement"
                  >
                    <t-icon name="delete" size="16" color="#fff" />
                    <text>删除</text>
                  </view>
                  <view
                    class="floating-btn cancel-btn"
                    data-index="{{index}}"
                    catchtap="onCancelEdit"
                  >
                    <t-icon name="close" size="16" color="#666" />
                    <text>取消</text>
                  </view>
                  <view
                    class="floating-btn confirm-btn"
                    data-index="{{index}}"
                    catchtap="onConfirmEdit"
                  >
                    <t-icon name="check" size="16" color="#fff" />
                    <text>确认</text>
                  </view>
                </view>
              </view>
          </view>
        </view>
      </view>

      <view class="setting-description">
        公告将按顺序在首页显示。多条公告时会自动滚动播放，单条公告时静态显示。
      </view>

      <!-- 操作提醒 -->
      <view class="operation-tips">
        <view class="tips-icon">
          <t-icon name="info-circle" size="32" color="#0052D9" />
        </view>
        <view class="tips-content">
          <view class="tips-title">操作提示</view>
          <view class="tips-text">
            • 点击公告内容可直接编辑，失焦自动保存
          </view>
          <view class="tips-text">
            • 点击 <text class="status-demo active">启用</text> / <text class="status-demo inactive">禁用</text> 标签可切换公告状态
          </view>
          <view class="tips-text">
            • 编辑模式下可点击删除按钮移除公告
          </view>
        </view>
      </view>
    </view>
  </view>
  </view> <!-- 结束 content-wrapper -->

  <!-- 提示组件区域 -->
  <t-toast id="t-toast" />
  <t-message id="t-message" />
  <t-dialog id="t-dialog" />
</view>
