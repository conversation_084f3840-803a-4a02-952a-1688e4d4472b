// announcement-test.js
// 多条公告功能测试脚本

/**
 * 测试数据生成器
 */
class AnnouncementTestData {
  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  static createAnnouncement(content, order = 1, isActive = true) {
    return {
      id: this.generateUUID(),
      content: content,
      createTime: new Date(),
      updateTime: new Date(),
      order: order,
      isActive: isActive
    };
  }

  static createTestAnnouncements() {
    return [
      this.createAnnouncement("欢迎来到伽House！我们提供专业的瑜伽课程。", 1),
      this.createAnnouncement("本周特惠：新会员首次体验课程免费！", 2),
      this.createAnnouncement("请注意：周末课程时间有调整，详情请咨询前台。", 3)
    ];
  }

  static createLegacyData() {
    return {
      contact: {
        phone: "138-8888-8888",
        address: "测试地址",
        announcement: "这是一条传统的单条公告"
      }
    };
  }

  static createNewData() {
    return {
      contact: {
        phone: "138-8888-8888",
        address: "测试地址",
        announcement: "兼容字段内容",
        announcements: this.createTestAnnouncements()
      }
    };
  }
}

/**
 * 兼容性测试
 */
class CompatibilityTest {
  static testDataStructure() {
    console.log("=== 数据结构兼容性测试 ===");
    
    // 测试旧数据结构
    const legacyData = AnnouncementTestData.createLegacyData();
    console.log("旧数据结构:", legacyData);
    
    // 测试新数据结构
    const newData = AnnouncementTestData.createNewData();
    console.log("新数据结构:", newData);
    
    // 验证兼容字段存在
    const hasCompatField = newData.contact.hasOwnProperty('announcement');
    console.log("兼容字段存在:", hasCompatField);
    
    // 验证新字段存在
    const hasNewField = newData.contact.hasOwnProperty('announcements');
    console.log("新字段存在:", hasNewField);
    
    return hasCompatField && hasNewField;
  }

  static testDataSync() {
    console.log("=== 数据同步测试 ===");
    
    const announcements = AnnouncementTestData.createTestAnnouncements();
    
    // 模拟同步逻辑
    const activeAnnouncements = announcements.filter(item => item.isActive);
    activeAnnouncements.sort((a, b) => (a.order || 0) - (b.order || 0));
    
    const syncedAnnouncement = activeAnnouncements.length > 0 
      ? activeAnnouncements[0].content 
      : '';
    
    console.log("第一条公告内容:", syncedAnnouncement);
    console.log("同步成功:", syncedAnnouncement === announcements[0].content);
    
    return syncedAnnouncement === announcements[0].content;
  }
}

/**
 * 功能测试
 */
class FunctionalTest {
  static testAnnouncementProcessing() {
    console.log("=== 公告处理功能测试 ===");
    
    // 模拟 processAnnouncements 方法
    function processAnnouncements(contactInfo) {
      // 优先使用多条公告
      if (contactInfo.announcements && contactInfo.announcements.length > 0) {
        return contactInfo.announcements
          .filter(item => item.isActive)
          .sort((a, b) => (a.order || 0) - (b.order || 0));
      }
      
      // 兼容单条公告
      if (contactInfo.announcement) {
        return [{
          id: 'legacy',
          content: contactInfo.announcement,
          createTime: new Date(),
          updateTime: new Date(),
          order: 1,
          isActive: true
        }];
      }
      
      return [];
    }
    
    // 测试多条公告处理
    const newData = AnnouncementTestData.createNewData();
    const processedNew = processAnnouncements(newData.contact);
    console.log("多条公告处理结果:", processedNew.length, "条");
    
    // 测试单条公告处理
    const legacyData = AnnouncementTestData.createLegacyData();
    const processedLegacy = processAnnouncements(legacyData.contact);
    console.log("单条公告处理结果:", processedLegacy.length, "条");
    
    // 测试空数据处理
    const emptyData = { contact: {} };
    const processedEmpty = processAnnouncements(emptyData.contact);
    console.log("空数据处理结果:", processedEmpty.length, "条");
    
    return processedNew.length === 3 && 
           processedLegacy.length === 1 && 
           processedEmpty.length === 0;
  }

  static testUUIDGeneration() {
    console.log("=== UUID生成测试 ===");
    
    const uuid1 = AnnouncementTestData.generateUUID();
    const uuid2 = AnnouncementTestData.generateUUID();
    
    console.log("UUID1:", uuid1);
    console.log("UUID2:", uuid2);
    console.log("UUID唯一性:", uuid1 !== uuid2);
    console.log("UUID格式正确:", /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid1));
    
    return uuid1 !== uuid2;
  }

  static testAnnouncementValidation() {
    console.log("=== 公告验证测试 ===");
    
    // 测试正常公告
    const normalAnnouncement = AnnouncementTestData.createAnnouncement("正常公告内容");
    console.log("正常公告:", normalAnnouncement.content.length <= 500);
    
    // 测试超长公告
    const longContent = "a".repeat(600);
    const longAnnouncement = AnnouncementTestData.createAnnouncement(longContent);
    console.log("超长公告:", longAnnouncement.content.length);
    
    // 测试空公告
    const emptyAnnouncement = AnnouncementTestData.createAnnouncement("");
    console.log("空公告:", emptyAnnouncement.content === "");
    
    return true;
  }
}

/**
 * 性能测试
 */
class PerformanceTest {
  static testLargeDataProcessing() {
    console.log("=== 大数据量处理测试 ===");
    
    const startTime = Date.now();
    
    // 生成大量公告数据
    const largeAnnouncements = [];
    for (let i = 0; i < 1000; i++) {
      largeAnnouncements.push(
        AnnouncementTestData.createAnnouncement(`公告内容 ${i}`, i + 1)
      );
    }
    
    // 模拟过滤和排序
    const filtered = largeAnnouncements
      .filter(item => item.isActive)
      .sort((a, b) => (a.order || 0) - (b.order || 0));
    
    const endTime = Date.now();
    const processingTime = endTime - startTime;
    
    console.log("处理1000条公告耗时:", processingTime, "ms");
    console.log("处理结果数量:", filtered.length);
    
    return processingTime < 100; // 期望在100ms内完成
  }
}

/**
 * 主测试函数
 */
function runAllTests() {
  console.log("开始多条公告功能测试...\n");
  
  const results = {
    compatibility: {
      dataStructure: CompatibilityTest.testDataStructure(),
      dataSync: CompatibilityTest.testDataSync()
    },
    functional: {
      announcementProcessing: FunctionalTest.testAnnouncementProcessing(),
      uuidGeneration: FunctionalTest.testUUIDGeneration(),
      announcementValidation: FunctionalTest.testAnnouncementValidation()
    },
    performance: {
      largeDataProcessing: PerformanceTest.testLargeDataProcessing()
    }
  };
  
  console.log("\n=== 测试结果汇总 ===");
  console.log("兼容性测试:", results.compatibility);
  console.log("功能测试:", results.functional);
  console.log("性能测试:", results.performance);
  
  // 计算总体通过率
  const allTests = Object.values(results).flatMap(category => Object.values(category));
  const passedTests = allTests.filter(result => result === true);
  const passRate = (passedTests.length / allTests.length * 100).toFixed(2);
  
  console.log(`\n总体通过率: ${passRate}% (${passedTests.length}/${allTests.length})`);
  
  return results;
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    AnnouncementTestData,
    CompatibilityTest,
    FunctionalTest,
    PerformanceTest,
    runAllTests
  };
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.AnnouncementTest = {
    AnnouncementTestData,
    CompatibilityTest,
    FunctionalTest,
    PerformanceTest,
    runAllTests
  };
}

// 自动运行测试（如果直接执行此文件）
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests();
}
