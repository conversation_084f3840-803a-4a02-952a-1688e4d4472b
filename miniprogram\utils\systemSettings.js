// systemSettings.js
// 系统设置相关工具函数

/**
 * 加载联系信息
 * @returns {Promise<Object>} 联系信息对象
 */
export async function loadContactInfo() {
  try {
    const result = await wx.cloud.callFunction({
      name: 'adminManagement',
      data: {
        action: 'getSystemSettings'
      }
    });
    
    if (result.result.success) {
      const settings = result.result.data;
      const contactInfo = {
        phone: settings.contact?.phone || '',
        address: settings.contact?.address || '',
        announcement: settings.contact?.announcement || '',
        announcements: settings.contact?.announcements || []
      };

      console.log('从数据库获取的联系信息:', contactInfo);
      return contactInfo;
    } else {
      console.error('获取系统设置失败:', result.result.message);
      return null;
    }
  } catch (error) {
    console.error('加载联系信息失败:', error);
    return null;
  }
}

/**
 * 获取系统设置
 * @returns {Promise<Object>} 系统设置对象
 */
export async function getSystemSettings() {
  try {
    const result = await wx.cloud.callFunction({
      name: 'adminManagement',
      data: {
        action: 'getSystemSettings'
      }
    });
    
    if (result.result.success) {
      return result.result.data;
    } else {
      throw new Error(result.result.message);
    }
  } catch (error) {
    console.error('获取系统设置失败:', error);
    throw error;
  }
}

/**
 * 更新系统设置
 * @param {Object} settings - 系统设置对象
 * @returns {Promise<boolean>} 是否更新成功
 */
export async function updateSystemSettings(settings) {
  try {
    const result = await wx.cloud.callFunction({
      name: 'adminManagement',
      data: {
        action: 'updateSystemSettings',
        settings: settings
      }
    });

    if (result.result.success) {
      return true;
    } else {
      throw new Error(result.result.message);
    }
  } catch (error) {
    console.error('更新系统设置失败:', error);
    throw error;
  }
}

/**
 * 更新多条公告
 * @param {Array} announcements - 公告数组
 * @returns {Promise<boolean>} 是否更新成功
 */
export async function updateAnnouncements(announcements) {
  try {
    const result = await wx.cloud.callFunction({
      name: 'adminManagement',
      data: {
        action: 'updateAnnouncements',
        announcements: announcements
      }
    });

    if (result.result.success) {
      return true;
    } else {
      throw new Error(result.result.message);
    }
  } catch (error) {
    console.error('更新多条公告失败:', error);
    throw error;
  }
}

/**
 * 获取有效的公告列表
 * @returns {Promise<Array>} 有效公告数组
 */
export async function getActiveAnnouncements() {
  try {
    const contactInfo = await loadContactInfo();
    if (!contactInfo) {
      return [];
    }

    // 优先使用多条公告
    if (contactInfo.announcements && contactInfo.announcements.length > 0) {
      return contactInfo.announcements
        .filter(item => item.isActive)
        .sort((a, b) => (a.order || 0) - (b.order || 0));
    }

    // 兼容单条公告
    if (contactInfo.announcement) {
      return [{
        id: 'legacy',
        content: contactInfo.announcement,
        createTime: new Date(),
        updateTime: new Date(),
        order: 1,
        isActive: true
      }];
    }

    return [];
  } catch (error) {
    console.error('获取有效公告失败:', error);
    return [];
  }
}