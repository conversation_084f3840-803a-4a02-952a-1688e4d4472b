/**
 * 文件夹管理工具
 * 提供文件夹的增删改查和图片关联操作
 */

const db = wx.cloud.database();

/**
 * 获取所有文件夹（系统文件夹 + 自定义文件夹）
 */
export async function getAllFolders() {
  try {
    const result = await db.collection('album_folders')
      .orderBy('type', 'asc')  // 系统文件夹在前
      .orderBy('createTime', 'asc')
      .get();
    
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('获取文件夹列表失败:', error);
    return {
      success: false,
      message: '获取文件夹列表失败',
      error
    };
  }
}

/**
 * 创建自定义文件夹
 */
export async function createCustomFolder(name) {
  try {
    // 检查文件夹名称是否已存在
    const existCheck = await db.collection('album_folders')
      .where({ name: name.trim() })
      .get();
    
    if (existCheck.data.length > 0) {
      return {
        success: false,
        message: '文件夹名称已存在'
      };
    }
    
    // 创建新文件夹
    const result = await db.collection('album_folders').add({
      data: {
        name: name.trim(),
        type: 'custom',
        createTime: new Date(),
        updateTime: new Date(),
        imageCount: 0
      }
    });
    
    return {
      success: true,
      data: {
        _id: result._id,
        name: name.trim(),
        type: 'custom',
        imageCount: 0
      }
    };
  } catch (error) {
    console.error('创建文件夹失败:', error);
    return {
      success: false,
      message: '创建文件夹失败',
      error
    };
  }
}

/**
 * 重命名文件夹
 */
export async function renameFolder(folderId, newName) {
  try {
    // 检查是否为系统文件夹
    const folderResult = await db.collection('album_folders').doc(folderId).get();
    if (!folderResult.data) {
      return {
        success: false,
        message: '文件夹不存在'
      };
    }
    
    if (folderResult.data.type === 'system') {
      return {
        success: false,
        message: '系统文件夹不能重命名'
      };
    }
    
    // 检查新名称是否已存在
    const existCheck = await db.collection('album_folders')
      .where({ 
        name: newName.trim(),
        _id: db.command.neq(folderId)
      })
      .get();
    
    if (existCheck.data.length > 0) {
      return {
        success: false,
        message: '文件夹名称已存在'
      };
    }
    
    // 更新文件夹名称
    await db.collection('album_folders').doc(folderId).update({
      data: {
        name: newName.trim(),
        updateTime: new Date()
      }
    });
    
    return {
      success: true,
      message: '重命名成功'
    };
  } catch (error) {
    console.error('重命名文件夹失败:', error);
    return {
      success: false,
      message: '重命名失败',
      error
    };
  }
}

/**
 * 删除自定义文件夹
 */
export async function deleteCustomFolder(folderId) {
  try {
    // 检查是否为系统文件夹
    const folderResult = await db.collection('album_folders').doc(folderId).get();
    if (!folderResult.data) {
      return {
        success: false,
        message: '文件夹不存在'
      };
    }
    
    if (folderResult.data.type === 'system') {
      return {
        success: false,
        message: '系统文件夹不能删除'
      };
    }
    
    // 从所有图片中移除该文件夹关联
    await removeImageFromFolder(null, folderId, true);
    
    // 删除文件夹
    await db.collection('album_folders').doc(folderId).remove();
    
    return {
      success: true,
      message: '删除成功'
    };
  } catch (error) {
    console.error('删除文件夹失败:', error);
    return {
      success: false,
      message: '删除失败',
      error
    };
  }
}

/**
 * 将图片添加到文件夹
 */
export async function addImageToFolder(imageId, folderId) {
  try {
    // 获取图片当前信息
    const imageResult = await db.collection('album_images').doc(imageId).get();
    if (!imageResult.data) {
      return {
        success: false,
        message: '图片不存在'
      };
    }
    
    const image = imageResult.data;
    const currentFolderIds = image.folderIds || [];
    
    // 检查是否已经在该文件夹中
    if (currentFolderIds.includes(folderId)) {
      return {
        success: true,
        message: '图片已在该文件夹中'
      };
    }
    
    // 添加文件夹关联
    const newFolderIds = [...currentFolderIds, folderId];
    
    await db.collection('album_images').doc(imageId).update({
      data: {
        folderIds: newFolderIds,
        updateTime: new Date()
      }
    });
    
    // 更新文件夹图片数量
    await updateFolderImageCount(folderId);
    
    return {
      success: true,
      message: '添加成功'
    };
  } catch (error) {
    console.error('添加图片到文件夹失败:', error);
    return {
      success: false,
      message: '添加失败',
      error
    };
  }
}

/**
 * 从文件夹中移除图片
 */
export async function removeImageFromFolder(imageId, folderId, removeAll = false) {
  try {
    if (removeAll) {
      // 从所有图片中移除该文件夹关联（用于删除文件夹时）
      const imagesResult = await db.collection('album_images')
        .where({
          folderIds: db.command.in([folderId])
        })
        .get();
      
      for (const image of imagesResult.data) {
        const newFolderIds = image.folderIds.filter(id => id !== folderId);
        await db.collection('album_images').doc(image._id).update({
          data: {
            folderIds: newFolderIds,
            updateTime: new Date()
          }
        });
      }
    } else {
      // 从指定图片中移除文件夹关联
      const imageResult = await db.collection('album_images').doc(imageId).get();
      if (!imageResult.data) {
        return {
          success: false,
          message: '图片不存在'
        };
      }
      
      const image = imageResult.data;
      const currentFolderIds = image.folderIds || [];
      const newFolderIds = currentFolderIds.filter(id => id !== folderId);
      
      await db.collection('album_images').doc(imageId).update({
        data: {
          folderIds: newFolderIds,
          updateTime: new Date()
        }
      });
      
      // 更新文件夹图片数量
      await updateFolderImageCount(folderId);
    }
    
    return {
      success: true,
      message: '移除成功'
    };
  } catch (error) {
    console.error('从文件夹移除图片失败:', error);
    return {
      success: false,
      message: '移除失败',
      error
    };
  }
}

/**
 * 切换图片收藏状态
 */
export async function toggleImageFavorite(imageId) {
  try {
    const imageResult = await db.collection('album_images').doc(imageId).get();
    if (!imageResult.data) {
      return {
        success: false,
        message: '图片不存在'
      };
    }
    
    const image = imageResult.data;
    const newFavoriteStatus = !image.isFavorite;
    
    await db.collection('album_images').doc(imageId).update({
      data: {
        isFavorite: newFavoriteStatus,
        updateTime: new Date()
      }
    });
    
    // 更新收藏夹图片数量
    await updateFolderImageCount('folder_favorite');
    
    return {
      success: true,
      isFavorite: newFavoriteStatus,
      message: newFavoriteStatus ? '已收藏' : '已取消收藏'
    };
  } catch (error) {
    console.error('切换收藏状态失败:', error);
    return {
      success: false,
      message: '操作失败',
      error
    };
  }
}

/**
 * 更新文件夹图片数量
 */
async function updateFolderImageCount(folderId) {
  try {
    let count = 0;
    
    if (folderId === 'folder_favorite') {
      // 收藏夹：统计收藏的图片
      const result = await db.collection('album_images')
        .where({
          isFavorite: true,
          isDeleted: db.command.neq(true) // 兼容旧数据
        })
        .count();
      count = result.total;
    } else if (folderId === 'folder_banner') {
      // 首页展示：统计有bannerOrder的图片
      const result = await db.collection('album_images')
        .where({
          bannerOrder: db.command.neq(null),
          isDeleted: db.command.neq(true) // 兼容旧数据
        })
        .count();
      count = result.total;
    } else if (folderId === 'folder_trash') {
      // 回收站：统计已删除的图片
      const result = await db.collection('album_images')
        .where({
          isDeleted: true
        })
        .count();
      count = result.total;
    } else {
      // 自定义文件夹：统计包含该文件夹ID的图片
      const result = await db.collection('album_images')
        .where({
          folderIds: db.command.in([folderId]),
          isDeleted: db.command.neq(true) // 兼容旧数据
        })
        .count();
      count = result.total;
    }
    
    // 更新文件夹的图片数量
    await db.collection('album_folders').doc(folderId).update({
      data: {
        imageCount: count,
        updateTime: new Date()
      }
    });
    
    return count;
  } catch (error) {
    console.error('更新文件夹图片数量失败:', error);
    return 0;
  }
}
