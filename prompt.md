# 伽House活动室预约小程序需求文档（微信小程序）

> 说明：本需求文档基于当前 miniprogram/pages 目录下的页面结构，所有页面、功能、数据模型均与实际小程序保持一致。页面命名、入口、功能描述均以实际开发为准。

## 📋 目录

- [一、项目简介](#一项目简介)
- [二、页面结构与功能说明](#二页面结构与功能说明)
- [三、数据模型（微信云开发数据库）](#三数据模型微信云开发数据库)
- [四、用户角色与权限](#四用户角色与权限)
- [五、技术架构](#五技术架构)
- [六、全局交互与风格规范](#六全局交互与风格规范)
- [七、开发规范与注意事项](#七开发规范与注意事项)
- [八、部署与维护](#八部署与维护)

## 一、项目简介

本项目为"伽House"活动室预约管理微信小程序，基于微信云开发，服务于学员、讲师、管理员三类用户。支持活动预约、活动管理、用户与角色管理、考勤卡（会员卡）管理、相册管理等核心功能。所有功能均以易用性和高效性为核心，界面风格统一，交互友好。

**重要说明**：
- 原本按照健身房开发，后来客户说做的不是健身房，而是活动室！
- 教练应该是讲师、会员卡应该是考勤卡
- 前端的名字都改了，但是代码没改，所以代码中仍然使用原来的命名

### 项目特色
- 🎯 **多角色权限系统** - 学员、讲师、管理员三级权限管理
- 💳 **考勤卡系统** - 支持次卡、期卡等多种考勤卡类型
- 📅 **智能排课系统** - 讲师可视化排课，学员便捷预约
- 📸 **专业相册管理** - 瀑布流+时间轴双模式，文件夹系统，拖拽排序
- 🔔 **智能通知系统** - 预约提醒、课程通知等
- 🔄 **实时同步** - 基于云开发的实时数据同步
- 📱 **响应式设计** - 适配各种屏幕尺寸的移动设备

### 环境信息
- **环境ID**: `cloud1-1gm190n779af8083`
- **小程序AppID**: `wxf4efc4e381cce5e7`
- **基础库版本**: 2.20.1+
- **UI组件库**: TDesign小程序版

### 项目规模
- **页面数量**: 17个页面
- **云函数**: 5个
- **数据库集合**: 10个
- **核心功能模块**: 8个
- **用户角色**: 3种（学员、讲师、管理员）
- **开发周期**: 约3个月
- **代码行数**: 约15,000行

## 二、页面结构与功能说明

### 1. 首页（index）
- **功能**：展示品牌形象、门店信息、实景照片、快捷入口。
- **主要内容**：
  - 顶部视差背景图+Logo+"伽House"品牌名称
  - 联系电话、门店地址、公告信息（如有）
  - 实景照片展示区（动态加载，支持轮播查看）
  - 快捷操作按钮："约活动"（跳转活动表）、"考勤卡"（跳转会员卡）
  - 底部固定TabBar（首页/预约/我的）
- **交互**：所有按钮、图片均有点击反馈，支持图片轮播查看，信息区块风格统一。
- **特色**：支持骨架屏加载，图片兜底逻辑已删除（加载不成功就不显示）。

### 2. 预约页面（schedule）
- **功能**：学员浏览、预约、取消预约活动，讲师/管理员查看活动及预约情况。
- **主要内容**：
  - 顶部筛选栏（日期、讲师昵称）
  - "全部活动"视图（日历分组，展示status为online的活动）
  - "历史活动"视图（已结束活动列表）
  - 活动卡片（活动名称、时间、讲师昵称、剩余名额、预约状态）
  - 预约/取消预约按钮（学员专有，操作前弹窗确认，操作后Toast反馈）
  - 下拉刷新、分页加载
  - 底部TabBar
- **交互**：所有操作均有TDesign Toast反馈，风格统一。

### 3. 我的页面（profile）
- **功能**：用户登录、个人信息展示、分角色功能入口。
- **主要内容**：
  - 未登录：Logo、欢迎语、微信一键登录按钮、用户协议勾选
  - 已登录：头像、昵称、角色标签、功能分区（个人中心/学员功能/讲师功能/管理功能）
  - 个人中心：个人资料、关于
  - 学员功能：我的活动、考勤卡
  - 讲师功能：我的活动表
  - 管理员功能：用户管理、活动管理、考勤卡管理、相册管理、系统设置
  - 退出登录按钮、底部TabBar
- **交互**：分区、按钮根据用户角色动态显示，所有操作有反馈。

### 4. 个人资料编辑（profile-edit）
- **功能**：用户编辑头像、昵称、性别等基础信息。
- **主要内容**：
  - 头像、昵称、性别、注册时间、最近登录时间、角色等
  - 编辑按钮、保存/取消操作
- **交互**：所有修改有二次确认，操作结果Toast提示。

### 5. 讲师活动表（coach-schedule）
- **功能**：讲师查看自己主讲的已上线活动及预约学员名单。
- **主要内容**：
  - 活动卡片（活动名称、时间、地点、最大/已预约人数、可预约标签）
  - 预约学员名单（学员昵称、头像）
  - 下拉刷新、分页加载、空状态
  - 底部TabBar
- **交互**：仅展示"已上线活动"，无活动维护权限。

### 6. 活动管理（course-management）
- **功能**：管理员新增、编辑、删除、上下线活动，管理活动模板。
- **主要内容**：
  - 顶部Tabs（活动维护/模板维护）
  - 活动维护：添加活动按钮、筛选Tabs（全部/已上线/未上线/历史活动）、活动卡片（含编辑/删除/上下线操作）
  - 模板维护：模板卡片（编辑/删除/应用为新活动）
  - 下拉刷新、分页加载、底部TabBar
- **交互**：所有操作有确认弹窗和Toast反馈。

### 7. 活动编辑（course-edit）
- **功能**：新建/编辑活动，保存为模板。
- **主要内容**：
  - 表单区：活动名称、时间、地点、讲师（多选）、人数、难度、描述、适合人群、状态等
  - 操作按钮：保存、保存为模板、取消
- **交互**：必填项校验，所有操作有确认弹窗和Toast反馈。

### 8. 活动详情（course-detail）
- **功能**：展示活动详细信息，学员预约/取消预约，讲师/管理员查看预约名单，管理员编辑/删除活动。
- **主要内容**：
  - 活动名称、类型标签、活动ID（可复制）
  - 活动时间、地点、讲师、难度、适合人群、描述、剩余名额
  - 预约/取消预约按钮（学员）、预约学员名单（讲师/管理员）、编辑/删除按钮（管理员）
- **交互**：所有操作有确认弹窗和Toast反馈。

### 9. 考勤卡（membership-card）
- **功能**：学员查看本人考勤卡信息。
- **主要内容**：
  - 考勤卡卡片（编号、有效期、总次数、剩余次数、颁发日期、状态）
  - 空状态提示、下拉刷新、底部TabBar
- **交互**：所有操作有Toast反馈。

### 10. 考勤卡管理（membership-card-management）
- **功能**：管理员新建、颁发、吊销、延期、冻结考勤卡，查看考勤卡列表。
- **主要内容**：
  - 顶部Tabs（全部/已颁发/未颁发）
  - 新建按钮、考勤卡卡片（含操作按钮）
  - 空状态、下拉刷新、分页加载、底部TabBar
- **交互**：所有操作有确认弹窗和Toast反馈。

### 11. 我的预约（my-bookings）
- **功能**：学员查看、取消本人已预约活动，查看历史活动。
- **主要内容**：
  - 已预约活动列表（可取消）、历史活动列表
  - 活动卡片（活动名称、时间、讲师、状态等）
- **交互**：取消预约有确认弹窗和Toast反馈。

### 12. 用户管理（user-management）
- **功能**：管理员查看、搜索、筛选、修改用户角色，禁用/启用账号。
- **主要内容**：
  - 顶部Tabs（全部用户/学员/讲师/管理员）
  - 搜索栏、用户卡片（头像、昵称、openid、角色、注册/登录时间、操作按钮）
  - 空状态、下拉刷新、分页加载、底部TabBar
- **交互**：所有操作有确认弹窗和Toast反馈。

### 13. 系统设置（system-settings）
- **功能**：管理员设置预约取消时间、维护模式、联系电话、门店地址、公告信息。
- **主要内容**：
  - 预约取消时间（输入框/滑块）、维护模式开关、联系电话、门店地址、公告信息输入框
  - 保存按钮、下拉刷新、底部TabBar
- **交互**：所有设置项修改有二次确认和Toast反馈。

### 14. 相册管理（album-management）⭐ 核心功能
- **功能**：管理活动室实景照片、宣传图片等，支持高级相册功能。
- **主要内容**：
  - **双显示模式**：
    - 瀑布流模式：3列瀑布流布局，支持不同高度图片
    - 时间轴模式：按日期分组显示，便于查找
  - **文件夹系统**：
    - 系统文件夹：收藏夹、首页展示、回收站（固定）
    - 自定义文件夹：用户可创建、重命名、删除
    - 文件夹预览：显示前4张图片作为封面
  - **图片管理功能**：
    - 上传：支持相册选择和拍照，自动压缩
    - 删除：软删除到回收站，支持恢复
    - 批量操作：多选、批量收藏、批量归类、批量删除
    - 收藏系统：一键收藏/取消收藏
    - 图片归类：拖拽或选择移动到文件夹
  - **首页展示管理**：
    - 选择图片作为首页展示
    - 拖拽排序调整显示顺序
    - 支持按钮排序和拖拽排序两种模式
  - **回收站机制**：
    - 软删除：删除的图片进入回收站
    - 恢复功能：从回收站恢复图片
    - 永久删除：彻底删除图片和云存储文件
    - 自动清理：定期清理回收站（可配置）
- **交互特色**：
  - 平滑的瀑布流动画（cubic-bezier缓动，渐进式延迟）
  - 长按进入选择模式，支持批量操作
  - 拖拽排序（首页展示文件夹专用）
  - 悬停效果：图片轻微上浮+阴影
  - 所有操作有TDesign Toast反馈
- **技术亮点**：
  - 使用微信官方grid-view组件实现瀑布流
  - 三星相册风格的现代化UI设计
  - 性能优化：硬件加速、懒加载、智能缓存、分页加载
  - 已删除骨架屏代码，使用简洁的加载指示器
  - 支持图片临时链接管理和自动刷新
  - 文件夹数量统计和预览图自动更新

### 15. 通知管理（notifications）
- **功能**：用户查看系统通知、预约提醒等消息。
- **主要内容**：
  - 通知列表（全部/未读筛选）
  - 通知详情弹窗、标记已读功能
  - 下拉刷新、分页加载
- **交互**：支持通知详情查看，已读/未读状态管理。

## 三、数据模型（微信云开发数据库）

### 1. 用户（users）
- _id: string
- openid: string
- nickName: string
- avatarUrl: string
- gender: number
- language: string
- roles: array<string>
- createTime: date
- updateTime: date
- lastLoginTime: date

### 2. 活动（courses）
- _id: string
- name: string
- type: string
- coach: array<string>（讲师openid数组）
- coachName: array<string>（讲师名字）
- startTime: date
- endTime: date
- venue: string
- capacity: number
- difficulty: string
- description: string
- suitableFor: string
- status: string
- createTime: date
- updateTime: date

### 3. 活动模板（coursesTemplate）
- _id: string
- name: string
- type: string
- coach: array<string>（讲师openid数组）
- venue: string
- capacity: number
- difficulty: string
- description: string
- suitableFor: string

### 4. 预约记录（bookings）
- _id: string
- userId: string
- courseId: string
- courseName: string
- cardNumber: string
- createTime: date
- updateTime: date
- status: string

### 5. 考勤卡（membershipCard）
- _id: string
- userId: string
- cardNumber: string
- validFrom: date
- validTo: date
- issueDate: date
- totalTimes: number
- remainingTimes: number
- status: string

### 6. 考勤卡模板（membershipCardTemplate）
- _id: string
- name: string
- totalTimes: number
- validityDays: number
- description: string
- createTime: date

### 7. 系统设置（systemSettings）
- cancelBookingHours: number
- maintenanceMode: boolean
- contactPhone: string
- storeAddress: string
- announcement: string

### 8. 相册图片（album_images）
- _id: string
- fileID: string
- tempFileURL: string
- createTime: date
- updateTime: date
- isDeleted: boolean
- isFavorite: boolean
- bannerOrder: number
- folderIds: array<string>

### 9. 相册文件夹（album_folders）
- _id: string
- name: string
- type: string（system/custom）
- systemType: string（favorite/banner/trash）
- createTime: date
- updateTime: date
- imageCount: number

### 10. 通知（notifications）
- _id: string
- userId: string
- type: string
- title: string
- content: string
- isRead: boolean
- createTime: date
- relatedId: string

## 四、用户角色与权限

### 🎓 学员 (Student)
- ✅ 浏览活动信息和讲师介绍
- ✅ 预约和取消活动
- ✅ 查看个人预约历史
- ✅ 管理个人考勤卡
- ✅ 查看活动详情和预约状态
- ✅ 接收预约通知和提醒

### 🧘‍♀️ 讲师 (Instructor)
- ✅ 查看个人活动安排
- ✅ 管理活动学员名单
- ✅ 查看学员预约信息
- ✅ 更新个人资料和介绍
- ✅ 接收预约相关通知

### 👨‍💼 管理员 (Admin)
- ✅ 用户管理（学员、讲师权限分配）
- ✅ 活动管理（创建、编辑、上下线）
- ✅ 考勤卡管理（发放、充值、统计）
- ✅ 相册管理（图片上传、分类、展示）
- ✅ 通知管理（发送通知、查看统计）
- ✅ 系统设置和配置管理

## 五、技术架构

### 前端技术栈
- **微信小程序** - 原生小程序开发框架
- **TDesign** - 腾讯官方小程序UI组件库
- **JavaScript ES6+** - 现代JavaScript语法
- **WXSS** - 小程序样式语言
- **Grid-View** - 微信官方瀑布流组件

### 后端技术栈
- **微信云开发** - Serverless后端服务
- **云函数** - Node.js运行时
- **云数据库** - NoSQL文档数据库
- **云存储** - 文件存储服务
- **定时触发器** - 自动化任务调度

### 云函数详情
- **adminManagement** - 管理员功能云函数
  - 用户角色管理
  - 系统设置管理
  - 数据统计分析
- **bookingManagement** - 预约管理云函数
  - 预约创建和取消
  - 预约状态管理
  - 预约冲突检测
- **userManagement** - 用户管理云函数
  - 用户信息管理
  - 权限验证
  - 登录状态管理
- **notificationManagement** - 通知管理云函数
  - 通知发送
  - 通知状态管理
  - 批量通知处理
- **notificationScheduler** - 通知调度云函数（定时任务）
  - 预约提醒
  - 活动通知
  - 系统维护通知

### 数据库集合
- **users** - 用户信息
- **courses** - 活动信息
- **coursesTemplate** - 活动模板
- **bookings** - 预约记录
- **membershipCard** - 考勤卡信息
- **membershipCardTemplate** - 考勤卡模板
- **systemSettings** - 系统设置
- **album_images** - 相册图片
- **album_folders** - 相册文件夹
- **notifications** - 通知消息

## 六、全局交互与风格规范

### UI设计规范
- **统一TabBar**：所有页面底部均为统一风格TabBar（首页/预约/我的）
- **TDesign组件**：操作提示、弹窗、Toast均统一使用TDesign组件，风格一致
- **设计语言**：文字、标签、间距、按钮等界面元素风格统一，符合微信小程序与TDesign设计规范
- **响应式设计**：适配不同屏幕尺寸，支持横竖屏切换
- **无障碍设计**：支持无障碍访问，字体大小可调节

### 交互规范
- **数据同步**：数据变动自动同步，支持下拉刷新、分页加载
- **操作反馈**：所有操作均有明确反馈，重要操作需二次确认
- **加载状态**：使用TDesign Loading组件，统一加载样式
- **错误处理**：网络错误、数据错误等统一错误提示
- **权限控制**：界面根据用户角色动态显示功能

### 特色设计
- **相册管理**：采用三星相册风格的现代化设计
- **瀑布流动画**：平滑的cubic-bezier缓动效果
- **视差滚动**：首页背景图支持视差滚动效果
- **毛玻璃效果**：导航栏使用backdrop-filter毛玻璃效果

## 七、开发规范与注意事项

### 代码规范
- **命名约定**：
  - 页面文件：kebab-case（如：album-management）
  - 变量命名：camelCase（如：albumImages）
  - 常量命名：UPPER_SNAKE_CASE（如：BOOKING_STATUS）
- **注释规范**：
  - 函数必须有JSDoc注释
  - 复杂逻辑必须有行内注释
  - 数据结构必须有说明注释

### 性能优化
- **图片优化**：使用压缩图片，支持懒加载
- **分页加载**：大数据列表使用分页加载
- **缓存策略**：合理使用本地缓存和云端缓存
- **硬件加速**：关键动画使用transform3d硬件加速

### 安全规范
- **权限验证**：所有敏感操作必须验证用户权限
- **数据校验**：前后端双重数据校验
- **敏感信息**：不在前端存储敏感信息
- **API安全**：云函数调用需要权限验证

### 测试规范
- **功能测试**：每个功能模块需要完整测试
- **兼容性测试**：测试不同设备和系统版本
- **性能测试**：测试大数据量下的性能表现
- **用户体验测试**：测试用户操作流程的流畅性

## 八、部署与维护

### 部署流程
1. **代码审查**：提交前进行代码审查
2. **测试验证**：在测试环境完整测试
3. **版本发布**：使用微信开发者工具发布
4. **线上验证**：发布后进行线上功能验证

### 维护要点
- **数据备份**：定期备份云数据库数据
- **性能监控**：监控小程序性能指标
- **用户反馈**：及时处理用户反馈和问题
- **版本更新**：定期更新依赖库和基础库版本

---

> **文档说明**：本文档为当前"伽House"小程序实际页面与功能的权威说明，涵盖了所有核心功能、技术架构、开发规范等内容。后续如有页面结构或功能调整，请同步更新本文件。
>
> **最后更新**：2025年8月3日
>

