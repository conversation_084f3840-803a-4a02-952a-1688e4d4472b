// profile-edit.js
// 个人资料编辑页面逻辑文件
// 这是小程序的个人资料编辑页面，负责用户头像和昵称的设置与修改

/**
 * 页面功能说明：
 *
 * 1. 双模式支持：
 *    - 新用户注册模式：首次设置个人信息
 *    - 资料编辑模式：修改已有的个人信息
 *
 * 2. 核心功能：
 *    - 头像选择和上传：支持从相册选择或拍照
 *    - 昵称输入和验证：实时验证和格式检查
 *    - 数据保存：上传到云存储和云数据库
 *    - 错误处理：网络异常、上传失败等情况
 *
 * 3. 技术特点：
 *    - 使用微信小程序原生头像选择API
 *    - 云存储文件上传和管理
 *    - 云数据库用户信息更新
 *    - 响应式UI和加载状态管理
 *
 * 4. 用户体验：
 *    - 实时预览选择的头像
 *    - 加载状态和操作反馈
 *    - 错误提示和重试机制
 *    - 开发环境特殊处理
 */

/**
 * 模块导入说明
 *
 * Toast工具函数：用于显示用户操作反馈
 * - showToast: 显示消息提示（成功、错误、警告、信息）
 * - showLoading: 显示加载状态（阻塞用户操作）
 * - hideToast: 隐藏当前显示的提示
 *
 * 这些函数封装了小程序的消息提示逻辑，提供统一的用户反馈体验
 * 类似于Web开发中的notification库或移动应用的Toast组件
 */
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

/**
 * 默认头像URL配置
 *
 * 设计考虑：
 * - 设置为空字符串，使用TDesign的用户图标作为占位符
 * - 避免使用网络图片作为默认头像，减少网络请求
 * - 在WXML中通过条件渲染显示图标或用户头像
 *
 * 技术原理：
 * - 当avatarUrl为空时，WXML中的wx:else会显示t-icon组件
 * - 当用户选择头像后，avatarUrl有值，显示实际的头像图片
 * - 这种设计既节省资源又提供良好的视觉效果
 */
const defaultAvatarUrl = '' // 使用 TDesign Icon 替代默认头像

/**
 * Page()函数注册小程序页面
 *
 * 这是小程序页面的核心函数，类似于：
 * - Vue的组件选项对象
 * - React的类组件或函数组件
 * - Angular的组件装饰器
 *
 * 包含页面的数据、生命周期、事件处理等所有逻辑
 */
Page({
  /**
   * data: 页面数据对象
   *
   * 响应式数据管理：
   * - 类似于Vue的data()函数返回的对象
   * - 类似于React的state状态
   * - 当数据变化时，页面会自动重新渲染
   * - 通过this.setData()方法更新数据
   */
  data: {
    /**
     * avatarUrl: 用户头像URL
     * 类型：String
     * 默认值：空字符串（显示默认图标）
     * 用途：存储用户选择或上传的头像地址
     */
    avatarUrl: defaultAvatarUrl,

    /**
     * nickName: 用户昵称
     * 类型：String
     * 默认值：空字符串
     * 用途：存储用户输入的昵称，支持实时编辑
     */
    nickName: '',

    /**
     * isSubmitting: 提交状态标识
     * 类型：Boolean
     * 默认值：false
     * 用途：控制提交按钮的加载状态，防止重复提交
     *
     * 状态变化：
     * false → 用户可以点击提交按钮
     * true → 按钮显示加载动画，禁用点击
     */
    isSubmitting: false,

    /**
     * pageTitle: 页面主标题
     * 类型：String
     * 默认值：'完善个人信息'（新用户模式）
     * 用途：动态显示页面标题，区分不同使用场景
     *
     * 可能的值：
     * - '完善个人信息'：新用户首次设置
     * - '编辑个人资料'：已有用户修改信息
     */
    pageTitle: '完善个人信息',

    /**
     * pageSubtitle: 页面副标题
     * 类型：String
     * 默认值：'请设置您的头像和昵称'
     * 用途：提供操作指引，帮助用户理解页面功能
     */
    pageSubtitle: '请设置您的头像和昵称',

    /**
     * submitText: 提交按钮文字
     * 类型：String
     * 默认值：'完成注册'（新用户模式）
     * 用途：动态显示按钮文字，明确操作意图
     *
     * 可能的值：
     * - '完成注册'：新用户完成信息设置
     * - '保存'：已有用户保存修改
     * - '保存中...'：提交过程中的状态文字
     */
    submitText: '完成注册',

    /**
     * editProfile: 编辑模式标识
     * 类型：Boolean
     * 默认值：false（新用户模式）
     * 用途：区分页面使用场景，控制不同的业务逻辑
     *
     * 模式说明：
     * false → 新用户注册模式：首次设置个人信息
     * true → 资料编辑模式：修改已有的个人信息
     */
    editProfile: false
  },

  /**
   * onLoad: 页面生命周期函数 - 页面加载时调用
   *
   * 生命周期对比：
   * - 小程序：onLoad → onShow → onReady → onHide → onUnload
   * - Vue：created → mounted → updated → destroyed
   * - React：constructor → componentDidMount → componentDidUpdate → componentWillUnmount
   * - Android：onCreate → onStart → onResume → onPause → onStop → onDestroy
   *
   * onLoad特点：
   * 1. 页面首次加载时调用，只调用一次
   * 2. 可以接收页面参数（通过options参数）
   * 3. 适合进行初始化操作，如数据加载、状态设置等
   * 4. 此时页面还未显示，用户看不到内容
   *
   * @param {Object} options 页面参数对象
   *   - userInfo: 传入的用户信息（JSON字符串）
   *   - editProfile: 编辑模式标识（'1'表示编辑模式）
   */
  onLoad(options) {
    /**
     * 隐藏底部TabBar
     *
     * 设计考虑：
     * - 个人资料编辑是独立的功能页面，不需要显示底部导航
     * - 隐藏TabBar可以提供更大的编辑空间
     * - 用户专注于当前任务，不会被其他导航干扰
     *
     * 技术实现：
     * - setSelectedValue(-1): 设置为-1表示不选中任何tab
     * - 这会隐藏TabBar的高亮状态
     */
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setSelectedValue(-1);
    }

    // 调试日志：记录页面加载信息
    console.log('注册页面加载，参数:', options);
    console.log('初始头像URL:', this.data.avatarUrl);

    /**
     * 开发环境检测和提示
     *
     * 目的：
     * - 在开发者工具中，头像选择功能可能无法正常工作
     * - 提醒开发者在真机上测试头像功能
     * - 避免开发者误以为功能有问题
     *
     * 技术原理：
     * - wx.getSystemInfoSync(): 获取系统信息
     * - platform === 'devtools': 判断是否在开发者工具中运行
     * - 真机环境的platform值为'ios'或'android'
     */
    const systemInfo = wx.getSystemInfoSync();
    console.log('系统信息:', systemInfo);

    if (systemInfo.platform === 'devtools') {
      showToast(this, {
        message: '开发环境：头像可能无法正常显示，建议真机测试',
        theme: 'warning',
        duration: 4000
      });
    }

    /**
     * 处理传入的用户信息参数
     *
     * 使用场景：
     * - 从其他页面跳转时，可能会传入用户信息
     * - 避免重复输入，提升用户体验
     *
     * 技术实现：
     * - JSON.parse(): 解析JSON字符串为对象
     * - decodeURIComponent(): 解码URL编码的字符串
     * - 这是因为页面参数传递时会进行URL编码
     */
    if (options.userInfo) {
      try {
        const userInfo = JSON.parse(decodeURIComponent(options.userInfo));
        console.log('传入的用户信息:', userInfo);
        this.setData({
          avatarUrl: userInfo.avatarUrl || defaultAvatarUrl,
          nickName: userInfo.nickName || ''
        });
      } catch (error) {
        console.error('解析用户信息失败:', error);
      }
    }

    /**
     * 资料编辑模式处理
     *
     * 触发条件：options.editProfile === '1'
     * - 从个人中心点击"个人资料"进入时触发
     * - 区别于新用户注册时的信息设置
     *
     * 模式切换：
     * 1. 更新页面标题和提示文字
     * 2. 更改提交按钮文字
     * 3. 设置编辑模式标识
     * 4. 预填当前用户的信息
     *
     * 数据来源：
     * - 通过getApp()获取全局应用实例
     * - 调用app.getUserInfo()获取当前用户信息
     * - 预填到表单中，方便用户修改
     */
    if (options.editProfile === '1') {
      this.setData({
        pageTitle: '编辑个人资料',
        pageSubtitle: '修改头像和昵称',
        submitText: '保存',
        editProfile: true
      });

      // 预填当前用户信息
      const app = getApp();
      const userInfo = app.getUserInfo && app.getUserInfo();
      if (userInfo) {
        this.setData({
          avatarUrl: userInfo.avatarUrl || defaultAvatarUrl,
          nickName: userInfo.nickName || ''
        });
      }
    }
  },

  /**
   * onShow: 页面生命周期函数 - 页面显示时调用
   *
   * 触发时机：
   * 1. 页面首次加载显示时（在onLoad之后）
   * 2. 从其他页面返回到当前页面时
   * 3. 从后台切换到前台时
   *
   * 与onLoad的区别：
   * - onLoad只在页面首次加载时调用一次
   * - onShow每次页面显示时都会调用
   * - 适合放置需要每次显示时都执行的逻辑
   */
  onShow() {
    /**
     * 每次显示页面时都隐藏TabBar
     *
     * 重复执行的原因：
     * - 用户可能从其他页面返回
     * - 确保TabBar始终处于隐藏状态
     * - 保持页面的独立性和专注性
     */
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setSelectedValue(-1);
    }
  },

  /**
   * 头像加载成功处理函数
   *
   * 触发时机：
   * - 当image组件成功加载头像图片时触发
   * - 对应WXML中的bindload="onAvatarLoad"
   *
   * 功能：
   * - 记录加载成功的日志
   * - 可以在这里添加加载成功后的处理逻辑
   * - 例如：隐藏加载动画、显示成功提示等
   *
   * @param {Object} e 事件对象
   *   - e.detail: 包含图片加载的详细信息
   */
  onAvatarLoad(e) {
    console.log('头像加载成功:', e.detail);
    console.log('加载的头像URL:', this.data.avatarUrl);
  },

  /**
   * 头像加载错误处理函数
   *
   * 触发时机：
   * - 当image组件加载头像图片失败时触发
   * - 对应WXML中的binderror="onAvatarError"
   *
   * 错误场景：
   * - 网络连接问题
   * - 图片URL无效
   * - 图片文件损坏
   * - 服务器响应错误
   *
   * 处理策略：
   * - 记录错误日志，便于调试
   * - 自动切换到默认头像（空字符串）
   * - 避免显示破损的图片图标
   * - 提供良好的用户体验
   *
   * @param {Object} e 事件对象
   *   - e.detail: 包含错误的详细信息
   */
  onAvatarError(e) {
    console.log('头像加载错误:', e.detail);
    console.log('当前头像URL:', this.data.avatarUrl);

    /**
     * 错误恢复机制
     *
     * 逻辑：
     * - 检查当前头像是否不是默认头像
     * - 如果不是，则切换到默认头像（空字符串）
     * - 这会触发WXML中的wx:else，显示TDesign图标
     *
     * 好处：
     * - 避免显示破损的图片
     * - 提供一致的视觉体验
     * - 用户仍然可以重新选择头像
     */
    if (this.data.avatarUrl !== defaultAvatarUrl) {
      console.log('切换到默认头像');
      this.setData({
        avatarUrl: defaultAvatarUrl
      });
    }
  },

  /**
   * 头像选择处理函数
   *
   * 触发时机：
   * - 用户点击头像区域的透明按钮时触发
   * - 对应WXML中的bind:chooseavatar="onChooseAvatar"
   *
   * 功能流程：
   * 1. 接收用户选择的头像临时文件路径
   * 2. 上传头像到云存储
   * 3. 获取云存储的永久访问URL
   * 4. 更新页面显示的头像
   * 5. 提供用户反馈
   *
   * 技术特点：
   * - 使用async/await处理异步操作
   * - 完整的错误处理和用户反馈
   * - 详细的调试日志记录
   * - 文件名唯一性保证
   *
   * @param {Object} e 事件对象
   *   - e.detail.avatarUrl: 用户选择的头像临时文件路径
   */
  async onChooseAvatar(e) {
    console.log('=== 头像选择开始 ===');
    console.log('选择头像事件详情:', e);
    console.log('事件详情对象:', e.detail);

    /**
     * 提取头像临时文件路径
     *
     * 微信小程序头像选择API返回的数据结构：
     * {
     *   detail: {
     *     avatarUrl: "临时文件路径"
     *   }
     * }
     */
    const { avatarUrl } = e.detail;
    console.log('提取的头像URL:', avatarUrl);
    console.log('URL类型:', typeof avatarUrl);
    console.log('URL长度:', avatarUrl ? avatarUrl.length : 0);

    /**
     * 头像选择验证
     *
     * 验证场景：
     * - 用户取消选择
     * - 选择过程中出现错误
     * - API返回异常数据
     */
    if (!avatarUrl) {
      console.error('头像选择失败 - avatarUrl为空');
      showToast(this, { message: '头像选择失败', theme: 'error' });
      return;
    }

    /**
     * 显示上传进度
     *
     * 用户体验考虑：
     * - 上传过程可能需要几秒钟
     * - 显示加载状态防止用户重复操作
     * - 提供明确的操作反馈
     */
    showLoading(this, '上传头像中...');

    try {
      /**
       * 生成唯一的云存储文件路径
       *
       * 文件命名策略：
       * - timestamp: 时间戳，确保时间唯一性
       * - randomStr: 随机字符串，防止并发冲突
       * - 格式：avatars/时间戳_随机字符串.jpg
       *
       * 技术细节：
       * - Math.random().toString(36): 生成36进制随机字符串
       * - substr(2, 9): 去掉"0."前缀，取9位字符
       * - 这样可以生成类似"avatars/1640995200000_k2j8h9x1m.jpg"的路径
       */
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substr(2, 9);
      const cloudPath = `avatars/${timestamp}_${randomStr}.jpg`;

      console.log('开始上传头像到云存储:', cloudPath);

      /**
       * 上传文件到云存储
       *
       * wx.cloud.uploadFile API说明：
       * - cloudPath: 云存储中的文件路径
       * - filePath: 本地临时文件路径（用户选择的头像）
       * - 返回值包含fileID，这是云存储的永久访问地址
       *
       * 云存储优势：
       * - 自动CDN加速
       * - 高可用性和稳定性
       * - 自动图片压缩和格式转换
       * - 安全的访问控制
       */
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath,
        filePath: avatarUrl
      });

      console.log('云存储上传结果:', uploadResult);

      /**
       * 获取永久访问URL
       *
       * fileID说明：
       * - 这是云存储返回的永久访问地址
       * - 格式类似：cloud://环境ID.文件路径
       * - 可以直接在image组件中使用
       * - 支持自动CDN加速和图片处理
       */
      const publicUrl = uploadResult.fileID;
      console.log('获取到的公开URL:', publicUrl);

      /**
       * 更新页面头像显示
       *
       * setData回调函数：
       * - 确保数据更新完成后再执行后续操作
       * - 用于调试和确认数据更新状态
       * - 类似于Vue的$nextTick或React的setState回调
       */
      this.setData({
        avatarUrl: publicUrl
      }, () => {
        console.log('setData回调 - 设置后的avatarUrl:', this.data.avatarUrl);
      });

      // 显示成功提示
      showToast(this, { message: '头像上传成功', theme: 'success' });

    } catch (error) {
      /**
       * 头像上传错误处理
       *
       * 可能的错误场景：
       * - 网络连接问题
       * - 云存储配置错误
       * - 文件格式不支持
       * - 文件大小超限
       * - 云存储空间不足
       * - 权限配置问题
       *
       * 错误处理策略：
       * - 详细记录错误信息，便于调试
       * - 显示用户友好的错误提示
       * - 自动回退到默认头像
       * - 不阻塞用户继续使用其他功能
       */
      console.error('=== 头像上传失败 ===');
      console.error('错误详情:', error);
      console.error('错误堆栈:', error.stack);
      console.error('错误消息:', error.message);
      console.error('错误代码:', error.code);

      showToast(this, { message: '头像上传失败，请重试', theme: 'error' });

      /**
       * 错误恢复机制
       *
       * 设计原则：
       * - 上传失败不应该影响用户继续使用
       * - 回退到默认头像，保持界面一致性
       * - 用户可以重新尝试选择头像
       */
      console.log('设置默认头像:', defaultAvatarUrl);
      this.setData({
        avatarUrl: defaultAvatarUrl
      });
    } finally {
      /**
       * 清理加载状态
       *
       * finally块确保：
       * - 无论成功还是失败都会执行
       * - 隐藏加载提示，恢复用户操作
       * - 避免界面卡在加载状态
       */
      hideToast(this);
    }

    console.log('=== 头像选择结束 ===');
  },

  /**
   * 昵称输入处理函数
   *
   * 触发时机：
   * - 用户在昵称输入框中输入内容时触发
   * - 对应WXML中的bind:input="onNicknameInput"
   *
   * 功能：
   * - 实时更新昵称数据
   * - 实现双向数据绑定
   * - 为后续验证和保存做准备
   *
   * 技术实现：
   * - e.detail.value: 获取用户输入的最新内容
   * - this.setData(): 更新页面数据，触发界面重新渲染
   *
   * @param {Object} e 事件对象
   *   - e.detail.value: 用户输入的昵称内容
   */
  onNicknameInput(e) {
    console.log('昵称输入:', e.detail.value);
    this.setData({
      nickName: e.detail.value
    });
  },

  /**
   * 昵称失焦处理函数
   *
   * 触发时机：
   * - 用户点击昵称输入框外部时触发
   * - 对应WXML中的bind:blur="onNicknameBlur"
   *
   * 功能：
   * - 最终确认用户输入的昵称
   * - 处理微信的内容安全检测结果
   * - 为提交做最后的数据准备
   *
   * 微信安全检测：
   * - 微信会自动检测输入内容是否包含敏感信息
   * - 如果检测到违规内容，会自动清空输入框
   * - 这是微信小程序的安全机制，开发者无法控制
   * - 我们需要获取检测后的最终值
   *
   * @param {Object} e 事件对象
   *   - e.detail.value: 经过微信安全检测后的昵称内容
   */
  onNicknameBlur(e) {
    console.log('昵称失焦:', e.detail.value);

    /**
     * 更新经过安全检测的昵称
     *
     * 重要性：
     * - 微信可能会修改或清空用户输入的内容
     * - 我们需要获取最终的、安全的内容
     * - 确保提交的数据符合微信的内容规范
     */
    this.setData({
      nickName: e.detail.value
    });
  },

  /**
   * 提交注册/保存资料处理函数
   *
   * 双模式支持：
   * 1. 新用户注册模式：完成个人信息设置并注册账户
   * 2. 资料编辑模式：更新已有用户的个人信息
   *
   * 功能流程：
   * 1. 数据验证：检查必填项
   * 2. 设置提交状态：防止重复提交
   * 3. 根据模式执行不同的业务逻辑
   * 4. 调用云函数处理数据
   * 5. 更新本地存储
   * 6. 提供用户反馈
   * 7. 页面跳转或返回
   *
   * 错误处理：
   * - 完整的try-catch错误捕获
   * - 用户友好的错误提示
   * - 自动恢复提交状态
   *
   * 触发时机：
   * - 用户点击提交按钮时触发
   * - 对应WXML中的bind:tap="handleSubmit"
   */
  async handleSubmit() {
    try {
      console.log('开始提交注册...');
      console.log('当前头像URL:', this.data.avatarUrl);

      /**
       * 数据验证
       *
       * 验证规则：
       * - 昵称不能为空
       * - 使用trim()去除首尾空格
       * - 可以扩展更多验证规则（长度、特殊字符等）
       *
       * 用户体验：
       * - 验证失败时显示明确的错误提示
       * - 不进行后续的网络请求
       * - 保持用户在当前页面继续修改
       */
      if (!this.data.nickName.trim()) {
        showToast(this, { message: '请输入昵称', theme: 'error' });
        return;
      }

      /**
       * 设置提交状态
       *
       * 防重复提交机制：
       * - isSubmitting设为true后，按钮会显示加载状态
       * - 按钮的loading属性会禁用点击
       * - 防止用户在网络请求过程中重复点击
       * - 提供视觉反馈，告诉用户操作正在进行
       */
      this.setData({
        isSubmitting: true
      });

      /**
       * 资料编辑模式处理
       *
       * 触发条件：this.data.editProfile === true
       * - 从个人中心进入的编辑模式
       * - 用户已经注册，只是修改个人信息
       *
       * 处理流程：
       * 1. 获取当前用户信息
       * 2. 准备更新数据
       * 3. 调用云函数更新
       * 4. 同步本地存储
       * 5. 返回上一页
       */
      if (this.data.editProfile) {
        console.log('=== 资料编辑模式保存开始 ===');

        /**
         * 获取当前用户信息
         *
         * 数据来源：
         * - getApp(): 获取全局应用实例
         * - app.getUserInfo(): 获取当前登录用户的信息
         * - 用于合并更新数据，保留其他字段
         */
        const app = getApp();
        const userInfo = app.getUserInfo && app.getUserInfo();
        console.log('当前用户信息:', userInfo);

        /**
         * 准备更新数据
         *
         * 数据结构：
         * - nickName: 用户输入的新昵称（去除首尾空格）
         * - avatarUrl: 用户选择的新头像URL
         *
         * 设计考虑：
         * - 只更新用户修改的字段
         * - 保留其他用户信息不变
         * - 确保数据的完整性和一致性
         */
        const updateData = {
          nickName: this.data.nickName.trim(),
          avatarUrl: this.data.avatarUrl
        };

        console.log('准备更新的数据:', updateData);
        console.log('头像URL详情:', {
          url: updateData.avatarUrl,
          type: typeof updateData.avatarUrl,
          length: updateData.avatarUrl ? updateData.avatarUrl.length : 0,
          isCloudUrl: updateData.avatarUrl ? updateData.avatarUrl.startsWith('cloud://') : false
        });

        /**
         * 调用云函数更新用户信息
         *
         * 云函数调用：
         * - name: 'userManagement' - 用户管理云函数
         * - action: 'updateUserInfo' - 更新用户信息操作
         * - data: updateData - 要更新的数据
         *
         * 云函数优势：
         * - 服务端处理，安全可靠
         * - 统一的业务逻辑处理
         * - 数据验证和权限控制
         * - 事务处理和错误恢复
         */
        console.log('调用云函数 updateUserInfo...');
        const res = await wx.cloud.callFunction({
          name: 'userManagement',
          data: {
            action: 'updateUserInfo',
            data: updateData
          }
        });

        console.log('云函数调用结果:', res);
        console.log('云函数返回详情:', JSON.stringify(res.result, null, 2));

        /**
         * 处理云函数返回结果
         *
         * 成功处理：
         * - 检查res.result.success标识
         * - 更新本地存储的用户信息
         * - 同步全局应用状态
         * - 显示成功提示
         * - 延迟返回上一页
         */
        if (res.result && res.result.success) {
          console.log('云函数调用成功，开始更新本地存储...');

          /**
           * 更新本地存储
           *
           * 数据同步策略：
           * - 使用扩展运算符合并对象
           * - 保留原有用户信息的其他字段
           * - 只更新修改的字段
           * - 确保数据的完整性
           */
          const updatedUserInfo = { ...userInfo, ...updateData };
          console.log('更新后的用户信息:', updatedUserInfo);

          /**
           * 同步存储和全局状态
           *
           * 双重更新：
           * - wx.setStorageSync(): 更新本地持久化存储
           * - app.globalData.userInfo: 更新全局内存状态
           *
           * 目的：
           * - 确保数据在应用重启后仍然有效
           * - 确保其他页面能立即获取到最新数据
           */
          wx.setStorageSync('userInfo', updatedUserInfo);
          app.globalData.userInfo = updatedUserInfo;

          console.log('本地存储更新完成');
          showToast(this, { message: '保存成功', theme: 'success' });

          /**
           * 延迟返回上一页
           *
           * 用户体验考虑：
           * - 给用户足够时间看到成功提示
           * - 1200ms的延迟既不会太快也不会太慢
           * - 自动返回，无需用户手动操作
           */
          setTimeout(() => {
            console.log('准备返回上一页');
            wx.navigateBack({ delta: 1 });
          }, 1200);
        } else {
          /**
           * 云函数调用失败处理
           *
           * 错误处理：
           * - 记录详细的错误信息
           * - 抛出包含用户友好信息的错误
           * - 错误会被外层catch捕获并处理
           */
          console.error('云函数调用失败:', res.result);
          throw new Error(res.result?.message || '保存失败');
        }

        console.log('=== 资料编辑模式保存结束 ===');
        return;
      }

      /**
       * 新用户注册模式处理
       *
       * 触发条件：this.data.editProfile === false
       * - 用户首次使用应用
       * - 需要完成注册流程
       *
       * 注册流程：
       * 1. 获取微信登录凭证
       * 2. 准备完整的用户信息
       * 3. 调用云函数进行注册
       * 4. 保存用户信息到本地
       * 5. 更新全局登录状态
       * 6. 跳转到主页面
       */

      /**
       * 获取微信登录凭证
       *
       * wx.login() API说明：
       * - 获取用户的临时登录凭证code
       * - code用于在服务端换取用户的openid和session_key
       * - 这是微信小程序的标准登录流程
       *
       * 安全考虑：
       * - code只能使用一次，且有时效性（5分钟）
       * - 不能在客户端直接获取openid
       * - 必须通过服务端（云函数）进行换取
       */
      const loginResult = await wx.login();
      if (!loginResult.code) {
        throw new Error('获取登录凭证失败');
      }

      /**
       * 准备完整的用户信息
       *
       * 用户信息结构：
       * - nickName: 用户输入的昵称
       * - avatarUrl: 用户选择的头像URL
       * - role: 默认角色（由管理员后续调整）
       * - gender: 性别（0-未知，1-男，2-女）
       * - country/province/city: 地理位置信息
       * - language: 语言设置
       *
       * 设计考虑：
       * - 提供完整的用户信息结构
       * - 设置合理的默认值
       * - 为后续功能扩展预留字段
       */
      const userInfo = {
        nickName: this.data.nickName.trim(),
        avatarUrl: this.data.avatarUrl,
        role: '讲师', // 默认角色，由管理员维护
        gender: 0,
        country: '',
        province: '',
        city: '',
        language: 'zh_CN'
      };

      console.log('准备提交的用户信息:', userInfo);

      /**
       * 调用云函数进行用户注册
       *
       * 云函数调用：
       * - name: 'userManagement' - 用户管理云函数
       * - action: 'login' - 登录/注册操作
       * - data: { userInfo } - 用户信息
       *
       * 云函数处理：
       * - 使用code换取openid
       * - 检查用户是否已存在
       * - 创建新用户或更新现有用户
       * - 返回完整的用户信息和登录状态
       */
      const cloudResult = await wx.cloud.callFunction({
        name: 'userManagement',
        data: {
          action: 'login',
          data: {
            userInfo: userInfo
          }
        }
      });

      /**
       * 处理注册成功的情况
       *
       * 成功标识：cloudResult.result.success === true
       */
      if (cloudResult.result.success) {
        /**
         * 构建最终的用户信息
         *
         * 数据合并策略：
         * - 基础信息：用户输入的信息
         * - 系统信息：云函数返回的openid、role等
         * - 服务端信息：可能包含管理员设置的其他信息
         *
         * 优先级：
         * - 云函数返回的信息优先级最高
         * - 确保服务端数据的权威性
         */
        const finalUserInfo = {
          ...userInfo,
          openid: cloudResult.result.openid,
          role: cloudResult.result.role || '学员',
          ...cloudResult.result.userInfo
        };

        /**
         * 保存用户信息到本地存储
         *
         * wx.setStorageSync()：
         * - 同步保存到本地持久化存储
         * - 应用重启后数据仍然存在
         * - 用于维持用户的登录状态
         */
        wx.setStorageSync('userInfo', finalUserInfo);

        /**
         * 更新全局应用状态
         *
         * 全局状态管理：
         * - app.globalData.userInfo: 用户信息
         * - app.globalData.isLoggedIn: 登录状态标识
         *
         * 目的：
         * - 其他页面可以立即获取到用户信息
         * - 控制页面的访问权限
         * - 提供统一的用户状态管理
         */
        const app = getApp();
        app.globalData.userInfo = finalUserInfo;
        app.globalData.isLoggedIn = true;

        showToast(this, { message: '注册成功', theme: 'success' });

        console.log('注册成功，用户信息:', finalUserInfo);

        /**
         * 延迟返回上一页
         *
         * 用户体验考虑：
         * - 给用户足够时间看到成功提示
         * - 1500ms的延迟适中
         * - 自动跳转，提供流畅的用户体验
         *
         * 跳转逻辑：
         * - 通常返回到首页或个人中心
         * - delta: 1 表示返回上一页
         */
        setTimeout(() => {
          wx.navigateBack({
            delta: 1
          });
        }, 1500);

      } else {
        /**
         * 处理注册失败的情况
         *
         * 失败原因可能包括：
         * - 网络连接问题
         * - 服务器错误
         * - 数据验证失败
         * - 系统维护等
         */
        throw new Error(cloudResult.result.message || '注册失败');
      }
    } catch (error) {
      /**
       * 统一错误处理
       *
       * 错误处理策略：
       * - 记录详细的错误信息，便于调试
       * - 显示用户友好的错误提示
       * - 不暴露技术细节给用户
       * - 引导用户重试操作
       */
      console.error('注册失败:', error);
      showToast(this, { message: '注册失败，请重试', theme: 'error' });
    } finally {
      /**
       * 清理提交状态
       *
       * finally块确保：
       * - 无论成功还是失败都会执行
       * - 恢复按钮的可点击状态
       * - 隐藏加载动画
       * - 允许用户重新操作
       *
       * 用户体验：
       * - 避免按钮永远处于加载状态
       * - 提供重试的机会
       * - 保持界面的响应性
       */
      this.setData({
        isSubmitting: false
      });
    }
  }
});

/**
 * 文件总结和学习价值
 *
 * 这个文件展示了小程序开发的核心概念：
 *
 * 1. 页面生命周期管理：
 *    - onLoad: 页面初始化
 *    - onShow: 页面显示
 *    - 数据预处理和状态设置
 *
 * 2. 用户交互处理：
 *    - 头像选择和上传
 *    - 表单输入和验证
 *    - 按钮点击和状态管理
 *
 * 3. 数据流管理：
 *    - 双向数据绑定
 *    - 状态更新和界面刷新
 *    - 本地存储和全局状态同步
 *
 * 4. 异步操作处理：
 *    - async/await语法
 *    - Promise错误处理
 *    - 加载状态管理
 *
 * 5. 云开发集成：
 *    - 云存储文件上传
 *    - 云函数调用
 *    - 微信登录集成
 *
 * 6. 用户体验优化：
 *    - 加载状态提示
 *    - 错误处理和重试
 *    - 操作反馈和引导
 *
 * 对于有建筑师背景和C#/Java经验的开发者：
 * - 小程序的事件驱动模型类似于桌面应用开发
 * - 异步编程概念与C#的async/await相似
 * - 组件化思想与面向对象编程相通
 * - 数据绑定机制类似于WPF的数据绑定
 */