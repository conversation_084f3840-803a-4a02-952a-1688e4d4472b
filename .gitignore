# 微信小程序项目 .gitignore 文件

# 依赖包文件夹
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 微信开发者工具生成的文件
.idea/
.vscode/
*.swp
*.swo
*~

# 微信小程序特有文件
miniprogram_npm/
node_modules/

# 云函数依赖
cloudfunctions/*/node_modules/
cloudfunctions/*/package-lock.json
cloudfunctions/*/yarn.lock

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/
*.log.*

# 临时文件
*.tmp
*.temp
.tmp/
.temp/

# 构建输出
dist/
build/
out/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 微信开发者工具缓存
.wechat_devtools/

# 其他
*.bak
*.backup
*.old 