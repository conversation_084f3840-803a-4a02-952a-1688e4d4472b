Component({
  data: {
    selected: 0,
    color: "#666666",
    selectedColor: "#0052d9",
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页",
        icon: "home",
        badge: false
      },
      {
        pagePath: "/pages/schedule/schedule",
        text: "预约",
        icon: "calendar",
        badge: false
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的",
        icon: "user",
        badge: false
      }
    ]
  },
  methods: {
    onTabChange(e) {
      const { value } = e.detail;
      const tab = this.data.list[value];
      
      if (tab) {
        wx.switchTab({
          url: tab.pagePath
        });
        this.setData({
          selected: value
        });
      }
    },
    
    // 支持外部设置selected值
    setSelectedValue(value) {
      this.setData({
        selected: value
      });
    },

    // 设置Badge徽标状态
    setBadge(index, show) {
      const list = this.data.list;
      if (list[index]) {
        list[index].badge = show;
        this.setData({
          list: list
        });
      }
    },

    // 设置"我的"页面Badge（未读消息）
    setProfileBadge(show) {
      this.setBadge(2, show); // "我的"是第3个tab，索引为2
    },

    // 设置"预约"页面Badge（新课程）
    setScheduleBadge(show) {
      this.setBadge(1, show); // "预约"是第2个tab，索引为1
    }
  }
}); 