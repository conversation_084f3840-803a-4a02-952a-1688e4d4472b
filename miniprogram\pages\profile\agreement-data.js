/**
 * 用户协议数据文件
 * 
 * 这个文件包含了美化后的用户协议数据结构
 * 内容来源：ancement.md 文件
 * 
 * 数据结构说明：
 * - 使用对象数组存储协议的各个段落
 * - 每个对象包含type（类型）和content（内容）字段
 * - section类型还包含title字段用于章节标题
 * - 便于在WXML中应用不同的样式和布局
 * 
 * 段落类型说明：
 * - title: 主标题
 * - paragraph: 普通段落
 * - section: 章节（包含标题和内容）
 * - footer: 页脚信息
 */

const agreementParagraphs = [
  {
    type: 'title',
    content: '伽House小程序用户协议'
  },
  {
    type: 'paragraph',
    content: '欢迎您使用伽House小程序（以下简称"本小程序"）。在使用本小程序前，请您仔细阅读并充分理解本协议的全部内容，特别是免除或限制责任的条款。您一旦注册、登录、使用本小程序，即视为已阅读并同意本协议的全部内容。'
  },
  {
    type: 'section',
    title: '特别声明',
    content: '伽House小程序仅为一个小工具，仅供组织小范围的非商业活动使用。伽House的运营人并非本小程序的开发者。本小程序由开发者基于友情支持为伽House实际控制人开发，所有功能、规则、内容均由伽House实际控制人提出、制定并负责解释。小程序开发者未收取任何开发报酬，仅按伽House实际控制人要求提供开发服务。小程序开发者不参与运营管理，不对小程序的使用、内容、规则、服务及由此产生的任何后果承担任何法律或经济责任。如有疑问、争议或解释权，均归伽House实际控制人所有。本小程序及伽House组织的所有活动均为非商业性质，不涉及任何商业交易、盈利或收费行为。'
  },
  {
    type: 'section',
    title: '一、服务内容',
    content: '1. 本小程序为用户提供活动预约、日程查看、用户信息管理等基础服务，支持普通成员、活动组织者、管理员三种角色。\n2. 本小程序基于微信云开发平台，用户需通过微信一键登录方可使用全部功能。\n3. 本小程序部分功能可能会根据实际需求进行调整或扩展，具体以实际页面展示为准。'
  },
  {
    type: 'section',
    title: '二、用户注册与使用',
    content: '1. 用户需使用微信账号授权登录，系统将获取您的微信头像、昵称等基础信息用于身份识别和展示。\n2. 新注册用户默认角色为"普通成员"，如需成为活动组织者或管理员，需由管理员审核分配。\n3. 用户应保证所提交信息真实、准确、完整，如信息变更请及时更新。\n4. 用户应妥善保管个人微信账号及相关信息，因账号泄露造成的损失由用户自行承担。'
  },
  {
    type: 'section',
    title: '三、用户权利与义务',
    content: '1. 用户有权根据自身角色使用相应功能，包括但不限于活动预约、取消、查看日程、管理活动等。\n2. 用户应遵守伽House相关规定，合理预约活动，不得恶意占用资源或扰乱正常组织秩序。\n3. 用户不得利用本小程序从事任何违法违规活动，不得上传、传播违法或不良信息。\n4. 用户应尊重其他用户的合法权益，不得进行骚扰、侮辱、诽谤等行为。'
  },
  {
    type: 'section',
    title: '四、活动预约与取消',
    content: '1. 普通成员可在活动剩余名额范围内预约活动，预约成功后可在"我的活动"中查看。\n2. 如需取消预约，须在活动开始前操作，逾期将无法取消。\n3. 预约记录将保留，供用户和管理员查询。\n4. 如遇特殊情况（如不可抗力、活动变动等），伽House有权对预约进行调整或取消，并通过小程序公告或消息通知用户。'
  },
  {
    type: 'section',
    title: '五、信息保护与隐私政策',
    content: '1. 本小程序严格遵守国家相关法律法规，保护用户个人信息安全。\n2. 用户信息仅用于活动预约、身份识别、服务优化等与本小程序相关的用途，未经用户同意不会向第三方披露。\n3. 如因不可抗力或非本小程序原因导致信息泄露、丢失、被盗用等，本小程序将协助用户妥善处理，但不承担由此产生的法律责任。\n4. 用户有权随时查询、更正或删除自己的个人信息，具体操作可通过"个人资料"页面或联系客服实现。'
  },
  {
    type: 'section',
    title: '六、服务变更、中断与终止',
    content: '1. 本小程序有权根据实际需要随时变更、暂停或终止部分或全部服务，并提前通过公告等方式通知用户。\n2. 如用户违反本协议或相关规定，本小程序有权暂停或终止其使用资格。\n3. 如因不可抗力、政策调整、技术升级等原因导致服务中断或终止，本小程序将尽力提前通知用户并妥善处理相关事宜。'
  },
  {
    type: 'section',
    title: '七、免责声明及异常情况处理',
    content: '1. 本小程序致力于提供安全、稳定的服务，但不保证服务不会中断或无错误。\n2. 因网络、设备故障、第三方原因等导致的服务中断或数据丢失，本小程序不承担赔偿责任。\n3. 用户因自身原因造成的损失，由用户自行承担。\n4. 伽House实际控制人对本小程序的运营、内容、规则、服务等承担全部解释权和责任。小程序开发者仅提供技术开发支持，不对本小程序的任何运营、内容、规则、服务及由此产生的任何后果承担任何法律或经济责任。\n5. 由于程序可能存在缺陷或不可预见的问题，可能导致用户预约失败、信息出错或其他异常情况。对于因此产生的用户不当收益，伽House的运营人有权向用户追回相关收益。对于故意利用或滥用程序漏洞（bug）牟利的用户，伽House将坚决追究其法律责任，包括但不限于追究刑事责任。\n6. 用户如发现系统漏洞或异常情况，有义务及时反馈给伽House工作人员，不得恶意传播或利用。'
  },
  {
    type: 'section',
    title: '八、知识产权声明',
    content: '1. 本小程序及其内容（包括但不限于界面设计、图标、文字、图片、代码等）均受相关法律保护。小程序的全部版权归开发者所有，伽House仅拥有小程序的使用权和运营权。\n2. 小程序的源代码及整体著作权归开发者所有，伽House仅拥有小程序的使用权和运营权，未经开发者书面许可，任何单位和个人不得擅自复制、传播、修改、反编译、商用或以其他方式使用本小程序的全部或部分代码。\n3. 未经授权，任何单位和个人不得擅自复制、传播、修改、使用本小程序内容。\n4. 如需商业合作或内容授权，请联系伽House实际控制人及开发者。'
  },
  {
    type: 'section',
    title: '九、协议修改',
    content: '1. 本小程序有权根据实际情况对本协议内容进行修改，修改后的协议将通过小程序公告等方式通知用户。\n2. 用户如不同意修改内容，应立即停止使用本小程序，继续使用视为接受修改后的协议。'
  },
  {
    type: 'section',
    title: '十、法律适用与争议解决',
    content: '1. 本协议的订立、执行与解释及争议的解决均适用中华人民共和国法律。\n2. 因本协议产生的争议，双方应友好协商解决，协商不成时，任何一方可向伽House所在地有管辖权的人民法院提起诉讼。'
  },
  {
    type: 'section',
    title: '十一、未成年人保护',
    content: '1. 未满18周岁的未成年人应在监护人指导下使用本小程序。\n2. 伽House将根据国家相关法律法规保护未成年人合法权益，若发现未成年人违规使用，将有权采取限制措施。'
  },
  {
    type: 'section',
    title: '十二、其他',
    content: '1. 本协议部分条款无效或不可执行，不影响其他条款的效力。\n2. 本协议未尽事宜，参照国家相关法律法规及伽House相关规定执行。'
  },
  {
    type: 'footer',
    content: '如您对本协议有任何疑问，请联系伽House工作人员。\n\n本协议自2025年7月起生效。\n\n伽House'
  }
];

// 导出协议数据
module.exports = {
  agreementParagraphs
};
