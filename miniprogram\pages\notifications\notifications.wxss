/* notifications.wxss */
/* 通知列表页面样式文件 */

/*
  根容器样式
  设置页面的基础布局和背景
*/
.container {
  min-height: 100vh; /* 最小高度为视窗高度，确保页面填满屏幕 */
  background: linear-gradient(180deg, #f8fafb 0%, #f5f7fa 100%); /* 渐变背景，更现代的视觉效果 */
  display: flex; /* 弹性布局 */
  flex-direction: column; /* 垂直方向排列 */
}

/*
  页面头部样式
  包含选项卡和操作按钮的区域
*/
.header {
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  background-color: #ffffff; /* 白色背景 */
  border-bottom: 1rpx solid #e8eaec; /* 更柔和的分隔线颜色 */
  position: sticky; /* 粘性定位，滚动时固定在顶部 */
  top: 0; /* 距离顶部0距离 */
  z-index: 100; /* 层级较高，确保在其他内容之上 */
  padding: 0 32rpx; /* 左右内边距 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04); /* 轻微阴影，增加层次感 */
}

/*
  操作按钮区域样式
  包含"全部已读"和"清空消息"等操作按钮
*/
.actions {
  padding: 20rpx 0; /* 增加上下内边距 */
  display: flex; /* 弹性布局 */
  justify-content: flex-end; /* 右对齐 */
  align-items: center; /* 垂直居中 */
  gap: 24rpx; /* 按钮之间的间距 */
  border-top: 1rpx solid #f0f2f5; /* 更柔和的分隔线 */
  background-color: #fafbfc; /* 浅色背景，区分操作区域 */
}

/*
  内容区域样式
  包含通知列表的主要内容区域
*/
.content {
  flex: 1; /* 占据剩余空间 */
  padding: 16rpx 0 0 0; /* 顶部留出间距，其他方向无内边距 */
  width: 100%;
}

/*
  加载状态容器样式
  首次加载时的加载动画区域
*/
.loading-container {
  display: flex; /* 弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  padding: 160rpx 0; /* 增加上下内边距 */
  background: linear-gradient(135deg, #f8fafb 0%, #ffffff 100%); /* 渐变背景 */
  margin: 24rpx; /* 外边距 */
  border-radius: 24rpx; /* 圆角 */
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05); /* 轻微阴影 */
}

/*
  空状态容器样式
  没有通知时的空状态显示区域
*/
.empty-container {
  display: flex; /* 弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  padding: 160rpx 0; /* 增加上下内边距 */
  background: linear-gradient(135deg, #f8fafb 0%, #ffffff 100%); /* 渐变背景 */
  margin: 24rpx; /* 外边距 */
  border-radius: 24rpx; /* 圆角 */
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05); /* 轻微阴影 */
}

/*
  通知列表样式
  通知项的容器
*/
.notification-list {
  background-color: transparent; /* 透明背景，让每个通知项独立显示 */
  padding: 0 24rpx; /* 左右内边距，让通知项不贴边 */
  
}

/*
  SwipeCell 滑动操作相关样式
  使用TDesign的SwipeCell组件实现左滑删除功能
*/

/* 通知包装器 */
.notification-wrapper {
  margin-bottom: 16rpx; /* 通知项之间的间距 */
  border-radius: 16rpx; /* 圆角 */
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06); /* 轻微阴影 */
  background-color: #ffffff; /* 白色背景 */
  overflow: hidden; /* 确保圆角效果 */
}

/* SwipeCell组件样式覆盖 */
.notification-wrapper t-swipe-cell {
  border-radius: 16rpx; /* 继承圆角 */
  overflow: hidden; /* 确保圆角效果 */
}

/*
  SwipeCell右侧操作区域样式
  自定义的删除按钮区域
*/
.swipe-right-action {
  height: 100%; /* 占满高度 */
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
}

/* 删除操作按钮 */
.delete-action-btn {
  width: 120rpx; /* 按钮宽度 */
  height: 100%; /* 占满高度 */
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%); /* 渐变红色背景 */
  display: flex; /* 弹性布局 */
  flex-direction: column; /* 垂直方向排列 */
  align-items: center; /* 水平居中 */
  justify-content: center; /* 垂直居中 */
  gap: 8rpx; /* 图标和文字间距 */
  transition: all 0.2s ease; /* 过渡动画 */
  position: relative; /* 相对定位 */
  overflow: hidden; /* 隐藏溢出 */
}

/* 删除按钮点击效果 */
.delete-action-btn:active {
  background: linear-gradient(135deg, #ff5252 0%, #e53e3e 100%); /* 更深的红色 */
  transform: scale(0.95); /* 轻微缩放 */
}

/* 删除按钮文字 */
.delete-text {
  font-size: 24rpx; /* 小字体 */
  color: #ffffff; /* 白色文字 */
  font-weight: 500; /* 中等字重 */
  letter-spacing: 0.5rpx; /* 字间距 */
}

/* 删除按钮悬停效果（为了更好的视觉反馈） */
.delete-action-btn::before {
  content: ''; /* 伪元素 */
  position: absolute; /* 绝对定位 */
  top: 0; /* 顶部对齐 */
  left: 0; /* 左侧对齐 */
  right: 0; /* 右侧对齐 */
  bottom: 0; /* 底部对齐 */
  background: rgba(255, 255, 255, 0); /* 透明背景 */
  transition: background-color 0.2s ease; /* 背景色过渡 */
  pointer-events: none; /* 不影响点击事件 */
}

/* 删除按钮激活时的覆盖层 */
.delete-action-btn:active::before {
  background: rgba(255, 255, 255, 0.1); /* 半透明白色覆盖 */
}

/*
  通知项基础样式
  每个通知的容器样式
*/
.notification-item {
  padding: 32rpx; /* 内边距，提供点击区域 */
  background-color: #ffffff; /* 白色背景 */
  transition: all 0.2s ease; /* 所有属性过渡动画 */
  position: relative; /* 相对定位，为未读指示器提供定位基准 */
  min-height: 120rpx; /* 最小高度，确保一致性 */
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
}

/*
  通知项点击效果
  用户点击时的视觉反馈
*/
.notification-item:active {
  background-color: #f8fafb; /* 点击时的背景色 */
  transform: scale(0.995); /* 轻微缩放效果 */
}

/*
  未读通知项样式
  未读通知的特殊样式
*/
.notification-item.unread {
  background: linear-gradient(135deg, #fff9f0 0%, #fff5eb 100%); /* 温暖的渐变背景 */
  border-left: 6rpx solid #ff8c42; /* 左侧橙色边框标识 */
}

/*
  通知内容布局样式
  通知项内部的主要布局容器
*/
.notification-content {
  display: flex; /* 弹性布局 */
  justify-content: space-between; /* 两端对齐 */
  align-items: flex-start; /* 顶部对齐 */
  gap: 24rpx; /* 元素间距 */
  width: 100%; /* 确保占满宽度 */
}

/*
  通知主要内容区域样式
  包含标题和内容文字的区域
*/
.notification-main {
  flex: 1; /* 占据剩余空间 */
  min-width: 0; /* 允许内容收缩，防止溢出 */
  padding-right: 16rpx; /* 右侧内边距，避免与时间区域贴得太近 */
}

/*
  通知标题基础样式
  通知的主标题样式
*/
.notification-title {
  font-size: 32rpx; /* 字体大小 */
  color: #2c3e50; /* 深蓝灰色文字，更现代 */
  line-height: 1.4; /* 行高，提高可读性 */
  margin-bottom: 12rpx; /* 增加底部外边距 */
  font-weight: 500; /* 中等字重 */
  letter-spacing: 0.5rpx; /* 字间距，提升可读性 */
}

/*
  未读通知标题样式
  未读通知标题的特殊样式
*/
.notification-title.unread {
  font-weight: 600; /* 加粗字重，突出未读状态 */
  color: #1a202c; /* 更深的文字颜色 */
}

/*
  通知内容文字样式
  通知的详细内容文字
*/
.notification-text {
  font-size: 28rpx; /* 较小的字体大小 */
  color: #718096; /* 柔和的灰色文字 */
  line-height: 1.6; /* 增加行高，提升可读性 */
  word-break: break-all; /* 允许单词内换行，防止长文本溢出 */
  display: -webkit-box; /* WebKit盒子模型 */
  -webkit-line-clamp: 2; /* 限制显示2行 */
  -webkit-box-orient: vertical; /* 垂直方向 */
  overflow: hidden; /* 隐藏溢出内容 */
  letter-spacing: 0.3rpx; /* 字间距 */
}

/*
  通知元信息区域样式
  包含时间和状态指示器的区域
*/
.notification-meta {
  display: flex; /* 弹性布局 */
  flex-direction: column; /* 垂直方向排列 */
  align-items: flex-end; /* 右对齐 */
  gap: 12rpx; /* 增加元素间距 */
  flex-shrink: 0; /* 不允许收缩 */
  min-width: 120rpx; /* 最小宽度，确保时间显示完整 */
}

/*
  通知时间样式
  显示通知时间的文字
*/
.notification-time {
  font-size: 24rpx; /* 小字体 */
  color: #a0aec0; /* 更柔和的浅灰色文字 */
  white-space: nowrap; /* 不换行 */
  font-weight: 400; /* 正常字重 */
  letter-spacing: 0.2rpx; /* 字间距 */
}

/*
  未读状态指示器样式
  未读通知的红色圆点
*/
.unread-dot {
  width: 16rpx; /* 增加宽度 */
  height: 16rpx; /* 增加高度 */
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%); /* 渐变红色背景 */
  border-radius: 50%; /* 圆形 */
  flex-shrink: 0; /* 不允许收缩 */
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3); /* 红色阴影 */
  animation: pulse 2s infinite; /* 脉冲动画 */
}

/* 未读圆点脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/*
  加载更多状态样式
  分页加载时的加载动画
*/
.load-more {
  display: flex; /* 弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  padding: 48rpx 0; /* 增加上下内边距 */
  background: transparent; /* 透明背景 */
  margin: 24rpx; /* 外边距 */
}

/*
  没有更多数据提示样式
  当所有数据加载完成时的提示
*/
.no-more {
  display: flex; /* 弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  padding: 48rpx 0; /* 增加上下内边距 */
  background: transparent; /* 透明背景 */
  margin: 24rpx; /* 外边距 */
}

/*
  没有更多数据文字样式
  提示文字的样式
*/
.no-more-text {
  font-size: 26rpx; /* 稍大的字体 */
  color: #a0aec0; /* 更柔和的浅灰色文字 */
  font-weight: 400; /* 正常字重 */
  letter-spacing: 0.5rpx; /* 字间距 */
}

/*
  测试功能提示样式
  显示在页面底部的功能说明
*/

/* 测试提示容器 */
.test-notice {
  display: flex; /* 弹性布局 */
  align-items: flex-start; /* 顶部对齐，适应多行文字 */
  gap: 16rpx; /* 增加图标和文字间距 */
  padding: 32rpx; /* 内边距 */
  margin: 24rpx; /* 外边距，与其他内容分离 */
  background: linear-gradient(135deg, #fff8e1 0%, #fff3c4 100%); /* 温暖的渐变背景 */
  border-radius: 20rpx; /* 增加圆角 */
  border: 1rpx solid #ffd54f; /* 金色边框 */
  box-shadow: 0 4rpx 16rpx rgba(255, 193, 7, 0.1); /* 金色阴影 */
}

/* 测试提示文字 */
.test-notice-text {
  flex: 1; /* 占据剩余空间 */
  font-size: 26rpx; /* 稍大的字体 */
  color: #8d6e63; /* 温暖的棕色文字 */
  line-height: 1.6; /* 增加行高，提高可读性 */
  word-break: break-all; /* 允许单词内换行，防止溢出 */
  font-weight: 400; /* 正常字重 */
  letter-spacing: 0.3rpx; /* 字间距 */
}

/* 
  响应式设计 - 适配不同屏幕尺寸
  
  小屏幕设备优化：
  - 减少内边距
  - 调整字体大小
  - 优化间距
*/
@media (max-width: 375px) {
  .notification-item {
    padding: 24rpx; /* 减少内边距 */
  }
  
  .notification-title {
    font-size: 30rpx; /* 略小的标题字体 */
  }
  
  .notification-text {
    font-size: 26rpx; /* 略小的内容字体 */
  }
}

/*
  通知详情弹窗样式
  点击通知时显示的详情弹窗
*/

/* 弹窗容器 */
.notification-detail-popup {
  width: 680rpx; /* 增加弹窗宽度 */
  max-height: 80vh; /* 最大高度为视窗高度的80% */
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%); /* 渐变背景 */
  border-radius: 32rpx; /* 增加圆角 */
  overflow: hidden; /* 隐藏超出内容 */
  display: flex; /* 弹性布局 */
  flex-direction: column; /* 垂直方向排列 */
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15); /* 增强阴影效果 */
}

/* 弹窗头部 */
.popup-header {
  display: flex; /* 弹性布局 */
  justify-content: space-between; /* 两端对齐 */
  align-items: center; /* 垂直居中 */
  padding: 40rpx 32rpx; /* 增加上下内边距 */
  border-bottom: 1rpx solid #e8eaec; /* 更柔和的分隔线 */
  background: linear-gradient(135deg, #f8fafb 0%, #ffffff 100%); /* 渐变背景 */
}

/* 弹窗标题 */
.popup-title {
  font-size: 36rpx; /* 较大字体 */
  font-weight: 600; /* 加粗 */
  color: #2c3e50; /* 深蓝灰色文字 */
  letter-spacing: 0.5rpx; /* 字间距 */
}

/* 关闭按钮 */
.popup-close {
  width: 48rpx; /* 按钮宽度 */
  height: 48rpx; /* 按钮高度 */
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  border-radius: 50%; /* 圆形按钮 */
  background-color: transparent; /* 透明背景 */
  transition: background-color 0.2s ease; /* 背景色过渡 */
}

/* 关闭按钮悬停效果 */
.popup-close:active {
  background-color: #f0f0f0; /* 点击时的背景色 */
}

/* 弹窗内容区域 */
.popup-content {
  flex: 1; /* 占据剩余空间 */
  padding: 32rpx; /* 内边距 */
  overflow-y: auto; /* 垂直滚动 */
}

/* 详情标题 */
.detail-title {
  font-size: 36rpx; /* 较大字体 */
  font-weight: 600; /* 加粗 */
  color: #1a1a1a; /* 深色文字 */
  margin-bottom: 16rpx; /* 底部外边距 */
  line-height: 1.4; /* 行高 */
}

/* 详情时间 */
.detail-time {
  font-size: 28rpx; /* 中等字体 */
  color: #999999; /* 浅灰色文字 */
  margin-bottom: 24rpx; /* 底部外边距 */
}

/* 详情内容 */
.detail-content {
  font-size: 32rpx; /* 正常字体 */
  color: #333333; /* 深灰色文字 */
  line-height: 1.6; /* 较大行高，提高可读性 */
  margin-bottom: 24rpx; /* 底部外边距 */
  word-break: break-all; /* 允许单词内换行 */
}

/* 详情状态 */
.detail-status {
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  gap: 8rpx; /* 元素间距 */
}

/* 状态标签 */
.status-label {
  font-size: 28rpx; /* 小字体 */
  color: #666666; /* 中等灰色文字 */
}

/* 状态值 */
.status-value {
  font-size: 28rpx; /* 小字体 */
  font-weight: 500; /* 中等字重 */
}

/* 已读状态样式 */
.status-value.read {
  color: #52c41a; /* 绿色，表示已读 */
}

/* 未读状态样式 */
.status-value.unread {
  color: #ff4757; /* 红色，表示未读 */
}

/* 弹窗操作区域 */
.popup-actions {
  display: flex; /* 弹性布局 */
  gap: 16rpx; /* 按钮间距 */
  padding: 24rpx 32rpx 32rpx; /* 内边距 */
  border-top: 1rpx solid #f0f0f0; /* 顶部分隔线 */
  background-color: #fafbfc; /* 浅灰色背景 */
}
