import { showToast, showLoading, hideToast } from '../../utils/toast.js';

Page({
  data: {
    // 视图切换
    viewTabs: [
      { label: '当前课程', value: 'current' },
      { label: '历史课程', value: 'history' }
    ],
    activeView: 'current',

    // 日期选择器相关数据已移除 - 简化页面逻辑，专注于课程展示

    courseList: [],
    filteredCourseList: [], // 筛选后的课程列表
    emptyDescription: '暂无课程', // 空状态描述
    loading: false,
    showDetailDialog: false,
    selectedCourse: null,

    // 当前课程分页相关状态
    currentPage: 1,
    currentPageSize: 10,
    currentHasMore: true,

    // 历史课程分页相关状态
    historyCourses: [],
    historyPage: 1,
    historyPageSize: 10,
    historyHasMore: true,
    historyLoading: false,
  },

  onLoad() {
    this.setData({ activeView: 'current' }, () => {
      // 移除日期选择器初始化 - 简化页面加载逻辑
      showLoading(this, '加载中...');
      this.loadCourseList(true); // 重置加载
    });
  },

  onShow() {
    // 每次显示页面时自动刷新数据
    this.loadCourseList(true); // 重置加载
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.activeView === 'current') {
      this.loadCourseList(true); // 重置加载当前课程
    } else if (this.data.activeView === 'history') {
      // 重置历史课程
      this.setData({
        historyCourses: [],
        historyPage: 1,
        historyHasMore: true
      });
      this.loadHistoryCourses();
    }
    wx.stopPullDownRefresh();
  },

  // 加载课程列表 - 支持分页
  async loadCourseList(reset = false) {
    if (this.data.loading) return;

    if (reset) {
      this.setData({
        courseList: [],
        filteredCourseList: [],
        currentPage: 1,
        currentHasMore: true
      });
    }

    if (!this.data.currentHasMore && !reset) return;

    this.setData({ loading: true });
    try {
      const app = getApp();
      const openid = app.globalData?.userInfo?.openid;
      if (!openid) {
        showToast(this, { message: '未登录', theme: 'warning' });
        this.setData({ courseList: [], loading: false });
        return;
      }

      // 使用云函数进行分页查询，确保按时间排序
      const res = await wx.cloud.callFunction({
        name: 'bookingManagement',
        data: {
          action: 'getCoachCourses',
          data: {
            coachId: openid,
            page: this.data.currentPage,
            pageSize: this.data.currentPageSize,
            status: 'online' // 只获取已上线的课程
          }
        }
      });

      if (!res.result.success) {
        showToast(this, { message: res.result.message || '获取课程失败', theme: 'error' });
        return;
      }

      let newCourses = res.result.data.list || [];

      // 格式化数据
      newCourses = newCourses.map(item => ({
        ...item,
        date: item.startTime ? this.formatDate(item.startTime) : '',
        time: item.startTime ? this.formatTime(item.startTime) : '',
        bookedCount: item.bookedCount || 0,
        isHot: item.isHot || false,
        bookedStudents: item.bookedStudents || [],
        collapseValue: [] // 折叠面板状态
      }));

      const updatedCourseList = reset ? newCourses : [...this.data.courseList, ...newCourses];

      this.setData({
        courseList: updatedCourseList,
        currentHasMore: newCourses.length === this.data.currentPageSize,
        currentPage: this.data.currentPage + 1
      }, () => {
        this.filterCourses(); // 加载完课程后进行筛选
      });
    } catch (e) {
      console.error('获取课程失败:', e);
      showToast(this, { message: '获取课程失败', theme: 'error' });
      if (reset) {
        this.setData({ courseList: [], filteredCourseList: [] });
      }
    }
    this.setData({ loading: false });
    hideToast(this);
  },

  // 获取已预约学员信息
  async loadBookedStudents(courseList) {
    try {
      const db = wx.cloud.database();
      
      for (let course of courseList) {
        // 获取该课程的预约记录
        const bookings = await db.collection('bookings')
          .where({
            courseId: course._id,
            status: 'upcoming' // 只获取有效的预约
          })
          .get();
        
        // 如果没有找到预约记录，尝试不限制状态
        if (!bookings.data || bookings.data.length === 0) {
          const allBookings = await db.collection('bookings')
            .where({
              courseId: course._id
            })
            .get();
          
          // 使用所有预约记录，不限制状态
          if (allBookings.data && allBookings.data.length > 0) {
            bookings.data = allBookings.data;
          }
        }
        
        // 更新预约数量
        course.bookedCount = bookings.data ? bookings.data.length : 0;
        
        if (bookings.data && bookings.data.length > 0) {
          // 获取学员信息 - 尝试不同的字段名
          let userIds = bookings.data.map(booking => booking.userId);
          
          if (!userIds[0]) {
            userIds = bookings.data.map(booking => booking.user_id);
          }
          if (!userIds[0]) {
            userIds = bookings.data.map(booking => booking.userOpenid);
          }
          
          const users = await db.collection('users')
            .where({
              openid: db.command.in(userIds)
            })
            .get();
          
          // 如果没有找到用户，尝试其他字段
          if (!users.data || users.data.length === 0) {
            const usersByOpenid = await db.collection('users')
              .where({
                _openid: db.command.in(userIds)
              })
              .get();
            
            if (usersByOpenid.data && usersByOpenid.data.length > 0) {
              course.bookedStudents = usersByOpenid.data;
            } else {
              course.bookedStudents = [];
            }
          } else {
            course.bookedStudents = users.data;
          }
        } else {
          course.bookedStudents = [];
        }
      }
    } catch (error) {
      console.error('获取已预约学员信息失败:', error);
    }
  },

  formatDate(date) {
    if (!date) return '';
    const d = new Date(date);
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekday = weekdays[d.getDay()];
    return `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}-${d.getDate().toString().padStart(2,'0')} ${weekday}`;
  },
  formatTime(date) {
    if (!date) return '';
    const d = new Date(date);
    return `${d.getHours().toString().padStart(2,'0')}:${d.getMinutes().toString().padStart(2,'0')}`;
  },

  onCourseTap(e) {
    const course = e.currentTarget.dataset.course;
    if (course && course._id) {
      wx.navigateTo({
        url: `/pages/course-detail/course-detail?id=${course._id}`,
      });
    }
  },
  onDialogClose() {
    this.setData({ showDetailDialog: false });
  },

  // 处理折叠面板展开/收起
  toggleCollapse(e) {
    const courseId = e.currentTarget.dataset.courseId;
    
    // 更新对应课程的折叠状态
    const courseList = this.data.courseList.map(course => {
      if (course._id === courseId) {
        const isExpanded = course.collapseValue && course.collapseValue.includes('students');
        return { 
          ...course, 
          collapseValue: isExpanded ? [] : ['students']
        };
      }
      return course;
    });
    
    this.setData({ courseList }, () => {
      this.filterCourses(); // 重新筛选以更新显示
    });
  },

  // 处理学员区域点击，阻止冒泡
  onStudentSectionTap(e) {
    // 阻止事件冒泡，避免触发卡片的点击事件
    e.stopPropagation();
  },

  // 删除所有 this.data.dateTabs、this.data.selectedDate、this.initDateTabs、this.onDateTabChange 的引用

  // 视图切换处理 - 从schedule页面移植的TDesign t-tabs事件处理方式
  onViewChange(e) {
    // TDesign t-tabs组件的事件对象结构：
    // e.detail.value 包含选中的tab值
    // 与原来的 e.currentTarget.dataset.value 不同
    // 这是从schedule页面移植的标准TDesign事件处理方式
    const activeView = e.detail.value;

    this.setData({ activeView }, async () => {
      if (activeView === 'current') {
        // 切换到当前课程时，直接筛选课程（移除日期选择逻辑）
        this.filterCourses();
      } else if (activeView === 'history') {
        // 切换到历史课程时，重置历史课程数据
        this.setData({
          historyCourses: [],
          historyPage: 1,
          historyHasMore: true
        });
        await this.loadHistoryCourses();
      }
    });
  },

  // 分页加载讲师历史课程
  async loadHistoryCourses() {
    if (this.data.historyLoading || !this.data.historyHasMore) return;
    this.setData({ historyLoading: true });
    try {
      const app = getApp();
      const coachId = app.globalData?.userInfo?.openid;
      if (!coachId) return;
      const res = await wx.cloud.callFunction({
        name: 'bookingManagement',
        data: {
          action: 'getHistoryCourses',
          data: {
            coachId,
            page: this.data.historyPage,
            pageSize: this.data.historyPageSize
          }
        }
      });
      if (!res.result.success) {
        showToast(this, { message: res.result.message || '加载失败', theme: 'error' });
        return;
      }
      let list = res.result.data.list || [];
      // 按结束时间倒序，给每条加 date 字段（YYYY年M月D日 周X）
      list = list.map(item => {
        let dateObj = item.endTime ? new Date(item.endTime) : (item.formattedDate ? new Date(item.formattedDate) : null);
        if (!dateObj) dateObj = new Date();
        const y = dateObj.getFullYear();
        const m = dateObj.getMonth() + 1;
        const d = dateObj.getDate();
        const weekMap = ['周日','周一','周二','周三','周四','周五','周六'];
        const week = weekMap[dateObj.getDay()];
        return {
          ...item,
          date: `${y}年${m}月${d}日 ${week}`
        };
      });
      this.setData({
        historyCourses: this.data.historyCourses.concat(list),
        historyHasMore: list.length === this.data.historyPageSize,
        historyPage: this.data.historyPage + 1
      });
    } finally {
      this.setData({ historyLoading: false });
    }
  },

  // 滚动到底部事件，历史课程tab下分页加载
  onReachBottom() {
    if (this.data.activeView === 'history') {
      this.loadHistoryCourses();
    }
  },

  // 当前课程滚动到底部事件
  onCurrentReachBottom() {
    if (this.data.activeView === 'current') {
      this.loadCourseList(false); // 加载更多当前课程
    }
  },

  // 日期选择器相关方法已移除 - 简化页面逻辑，专注于课程展示功能

  // 筛选课程 - 移除日期筛选功能，简化筛选逻辑
  filterCourses() {
    const { courseList, activeView } = this.data;

    let filtered = [...courseList];

    // 根据视图筛选（当前课程 vs 历史课程）
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // 今天的开始时间

    if (activeView === 'current') {
      // 当前课程：显示当天及未来的课程
      filtered = filtered.filter(course => {
        const courseTime = new Date(course.startTime);
        const courseDate = new Date(courseTime.getFullYear(), courseTime.getMonth(), courseTime.getDate());
        return courseDate >= today;
      });
    } else {
      // 历史课程：显示已结束的课程
      filtered = filtered.filter(course => {
        const courseTime = new Date(course.startTime);
        const courseEndTime = new Date(courseTime.getTime() + (course.duration || 60) * 60 * 1000);
        return courseEndTime <= now;
      });
    }

    // 移除日期筛选逻辑 - 显示所有符合视图条件的课程

    // 生成空状态描述
    const emptyDescription = this.generateEmptyDescription();

    this.setData({
      filteredCourseList: filtered,
      emptyDescription: emptyDescription
    });
  },

  // 生成空状态描述 - 简化描述逻辑，移除日期相关描述
  generateEmptyDescription() {
    const { activeView, courseList } = this.data;

    // 如果没有课程数据，显示通用提示
    if (courseList.length === 0) {
      return '暂无课程';
    }

    // 根据当前视图生成相应的空状态描述
    if (activeView === 'current') {
      return '暂无当前课程';
    } else if (activeView === 'history') {
      return '暂无历史课程';
    }

    return '暂无课程';
  }
}); 