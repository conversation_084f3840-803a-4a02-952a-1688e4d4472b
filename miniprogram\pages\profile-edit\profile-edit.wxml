<!--profile-edit.wxml-->
<!--
  个人资料编辑页面结构文件
  这是小程序的个人资料编辑页面，负责用户信息的修改和保存

  页面功能：
  1. 头像选择和上传：支持从相册选择或拍照设置头像
  2. 昵称编辑：修改用户显示的昵称
  3. 信息保存：将修改后的信息保存到云数据库

  页面特点：
  - 响应式设计：适配不同屏幕尺寸
  - 实时预览：修改后立即显示效果
  - 错误处理：网络异常、上传失败等情况的处理
  - 用户体验：加载状态、成功反馈、操作引导

  技术实现：
  - 使用微信小程序原生头像选择API
  - 云存储上传头像文件
  - 云数据库保存用户信息
  - TDesign组件库提供UI组件
-->

<!--
  根容器
  包含整个页面的所有内容，设置基础布局和样式
-->
<view class="container">
  <!--
    页面头部区域
    显示页面标题和副标题，提供页面上下文信息
  -->
  <view class="header">
    <!--
      页面主标题
      动态显示，根据页面状态变化：
      - 新用户首次设置：显示"完善个人信息"
      - 已有用户编辑：显示"编辑个人资料"

      数据绑定：{{pageTitle}}
      - 双花括号表示数据绑定，类似于Vue的{{}}或React的{}
      - pageTitle在JS文件中定义，会根据用户状态动态设置
    -->
    <text class="title">{{pageTitle}}</text>

    <!--
      页面副标题
      提供额外的说明信息，帮助用户理解页面用途

      数据绑定：{{pageSubtitle}}
      - 同样是动态内容，根据页面状态显示不同的提示文字
      - 例如："请设置您的头像和昵称"或"修改您的个人信息"
    -->
    <text class="subtitle">{{pageSubtitle}}</text>
  </view>

  <!--
    表单区域
    包含所有可编辑的用户信息字段
    采用分区块的设计，每个信息类型独立成区块
  -->
  <view class="form-section">
    <!--
      头像选择区域
      用户可以选择和上传个人头像

      功能特点：
      1. 支持从相册选择图片
      2. 支持拍照设置头像
      3. 实时预览选择的头像
      4. 自动上传到云存储
      5. 错误处理和加载状态
    -->
    <view class="avatar-section">
      <!--
        区块标题
        明确标识当前区块的功能
      -->
      <text class="section-title">头像</text>

      <!--
        头像容器
        包含头像显示、占位符、选择按钮等所有头像相关元素
        使用相对定位，方便内部元素的绝对定位
      -->
      <view class="avatar-wrapper">
        <!--
          头像图片显示
          条件渲染：只有当用户已选择头像时才显示

          wx:if条件渲染：
          - {{avatarUrl}}: 当avatarUrl有值时显示图片
          - 类似于Vue的v-if或React的条件渲染
          - 如果avatarUrl为空、null、undefined，则不显示此元素
        -->
        <image
          wx:if="{{avatarUrl}}"
          src="{{avatarUrl}}"
          mode="aspectFill"
          class="avatar"
          binderror="onAvatarError"
          bindload="onAvatarLoad"
        />

        <!--
          头像占位符
          条件渲染：只有当用户未选择头像时才显示

          wx:else条件渲染：
          - 与上面的wx:if形成条件分支
          - 当avatarUrl为空时显示占位符
          - 提供视觉提示，告诉用户这里应该放头像
        -->
        <view wx:else class="avatar-placeholder">
          <!--
            用户图标作为占位符
            t-icon: TDesign的图标组件
            - name="user": 用户图标
            - size="60rpx": 图标大小60响应式像素
            - color="#ccc": 浅灰色，表示占位状态
          -->
          <t-icon name="user" size="60rpx" color="#ccc"/>
        </view>

        <!--
          头像选择按钮
          使用微信小程序的原生头像选择功能

          button组件特殊用法：
          1. open-type="chooseAvatar": 微信小程序提供的特殊按钮类型
             - 点击时会调起微信的头像选择器
             - 用户可以从相册选择或拍照设置头像
             - 这是微信小程序的原生能力，无需额外开发

          2. bind:chooseavatar: 头像选择完成后的回调事件
             - 当用户选择头像后，会调用onChooseAvatar方法
             - 方法会接收到选择的头像信息
             - 然后进行上传和保存操作

          3. 透明覆盖层设计：
             - 按钮设置为透明，覆盖在头像显示区域上方
             - 用户看到的是美化后的界面，点击的是功能按钮
             - 这样既保持了视觉效果，又实现了功能
        -->
        <button class="avatar-button" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
          <!--
            头像选择提示覆盖层
            显示在头像上方，提示用户可以点击选择头像
          -->
          <view class="avatar-overlay">
            <!--
              提示文字
              引导用户进行头像选择操作
            -->
            <text class="avatar-text">点击选择头像</text>
          </view>
        </button>
      </view>
    </view>

    <!--
      昵称编辑区域
      用户可以输入和修改个人昵称

      功能特点：
      1. 实时输入验证
      2. 字符长度限制
      3. 特殊字符过滤
      4. 失焦时自动保存
      5. 输入提示和引导
    -->
    <view class="nickname-section">
      <!--
        区块标题
        明确标识当前区块的功能
      -->
      <text class="section-title">昵称</text>

      <!--
        昵称输入框
        使用小程序原生input组件，支持昵称输入的特殊优化

        input组件属性详解：
        1. type="nickname": 小程序提供的特殊输入类型
           - 专门用于昵称输入，会有特殊的键盘优化
           - 自动过滤一些不适合昵称的特殊字符
           - 提供更好的用户体验

        2. class="nickname-input": CSS类名
           - 用于设置输入框的样式
           - 包括字体、颜色、边框、内边距等

        3. placeholder="请输入昵称": 占位符文字
           - 当输入框为空时显示的提示文字
           - 引导用户知道应该输入什么内容
           - 类似于HTML的placeholder属性

        4. value="{{nickName}}": 输入框的值
           - 双向数据绑定的一部分（显示数据）
           - nickName变量在JS中定义，会显示在输入框中
           - 支持初始值显示和数据回显

        5. bind:input="onNicknameInput": 输入事件绑定
           - 当用户输入内容时触发
           - 实现双向数据绑定的另一部分（更新数据）
           - 可以进行实时验证和字符过滤

        6. bind:blur="onNicknameBlur": 失焦事件绑定
           - 当用户点击输入框外部时触发
           - 通常用于最终验证和数据保存
           - 提供用户操作完成的时机

        事件处理流程：
        用户输入 → onNicknameInput → 更新nickName变量 → 界面自动更新
        用户完成 → onNicknameBlur → 验证数据 → 准备保存
      -->
      <input
        type="nickname"
        class="nickname-input"
        placeholder="请输入昵称"
        value="{{nickName}}"
        bind:input="onNicknameInput"
        bind:blur="onNicknameBlur"
      />
    </view>
  </view>

  <!--
    提交按钮区域
    用户完成信息编辑后，点击提交保存到服务器

    设计考虑：
    1. 独立区域：与表单区域分离，突出操作重要性
    2. 固定位置：通常固定在页面底部，方便用户操作
    3. 状态反馈：显示加载状态，防止重复提交
    4. 动态文字：根据操作类型显示不同的按钮文字
  -->
  <view class="submit-section">
    <!--
      提交按钮
      使用TDesign的按钮组件，提供丰富的交互效果

      t-button组件属性详解：
      1. theme="primary": 按钮主题样式
         - primary: 主要按钮（蓝色背景，白色文字）
         - secondary: 次要按钮（白色背景，蓝色边框）
         - danger: 危险按钮（红色背景，白色文字）
         - 主要按钮用于最重要的操作，如保存、提交等

      2. size="large": 按钮尺寸
         - small: 小尺寸按钮
         - medium: 中等尺寸按钮（默认）
         - large: 大尺寸按钮
         - 大尺寸按钮更容易点击，适合移动端

      3. block: 块级按钮
         - 按钮占满父容器的宽度
         - 在移动端提供更好的点击体验
         - 类似于CSS的display: block; width: 100%;

      4. bind:tap="handleSubmit": 点击事件绑定
         - 当用户点击按钮时调用handleSubmit方法
         - 方法中会进行数据验证、网络请求、结果处理

      5. loading="{{isSubmitting}}": 加载状态
         - 当isSubmitting为true时，按钮显示加载动画
         - 防止用户重复点击，避免重复提交
         - 提供视觉反馈，告诉用户操作正在进行

      按钮文字：{{submitText}}
      - 动态显示，根据操作状态变化
      - 正常状态：显示"保存"或"完成设置"
      - 加载状态：显示"保存中..."或"提交中..."
      - 提供清晰的操作指引
    -->
    <t-button
      theme="primary"
      size="large"
      block
      bind:tap="handleSubmit"
      loading="{{isSubmitting}}"
    >
      {{submitText}}
    </t-button>
  </view>

  <!--
    Toast消息提示组件
    用于显示操作结果和用户反馈

    功能说明：
    1. 成功提示：保存成功时显示绿色提示
    2. 错误提示：网络错误、保存失败时显示红色提示
    3. 警告提示：数据验证失败时显示橙色提示
    4. 信息提示：一般操作反馈时显示蓝色提示

    使用场景：
    - 头像上传成功/失败
    - 昵称保存成功/失败
    - 网络连接异常
    - 数据验证错误
    - 其他用户操作反馈

    技术特点：
    - id="t-toast": 组件唯一标识符
    - 在JS中通过this.selectComponent('#t-toast')获取组件实例
    - 支持多种主题和自定义样式
    - 自动消失，不干扰用户操作
    - 层级最高，确保用户能看到反馈

    与其他技术对比：
    - Web开发：类似于alert()、notification
    - Vue：类似于this.$message()
    - React：类似于antd的message组件
    - 原生小程序：类似于wx.showToast()，但功能更丰富
  -->
  <t-toast id="t-toast" />
</view>