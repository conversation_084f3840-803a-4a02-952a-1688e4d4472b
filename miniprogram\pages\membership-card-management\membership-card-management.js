// miniprogram/pages/membership-card-management/membership-card-management.js
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

Page({
  data: {
    // 顶部选项卡
    topActiveTab: 'card', // 'card'或'template'
    // 考勤卡维护子选项卡
    cardActiveTab: 'all', // 'all'、'issued'、'unissued'
    // 模板维护子选项卡
    templateActiveTab: 'all', // 'all'、'homepage'

    // 考勤卡相关数据
    cards: [],
    allCardList: [],
    issuedCardList: [],
    unissuedCardList: [],
    visibleCardList: [], // 当前显示的考勤卡列表（经过筛选和搜索）

    // 模板相关数据
    templateList: [],
    allTemplateList: [],
    homepageTemplateList: [],
    visibleTemplateList: [], // 当前显示的模板列表（经过筛选和搜索）

    // 考勤卡搜索相关
    cardSearchKeyword: '', // 考勤卡搜索关键词
    cardSearchExpanded: false, // 考勤卡搜索框展开状态
    cardScrollIntoView: '', // 考勤卡滚动定位

    // 模板搜索相关
    templateSearchKeyword: '', // 模板搜索关键词
    templateSearchExpanded: false, // 模板搜索框展开状态
    templateScrollIntoView: '', // 模板滚动定位

    // 选项卡数据
    cardTabList: [
      { label: '全部', value: 'all' },
      { label: '已绑定', value: 'issued' },
      { label: '未绑定', value: 'unissued' }
    ],
    templateTabList: [
      { label: '全部', value: 'all' },
      { label: '首页显示', value: 'homepage' }
    ],

    loading: true,
    
    // 新建考勤卡相关
    showCreateDialog: false,
    createForm: {
      totalTimes: '',
      validFrom: '',
      validTo: '',
      issueDate: ''
    },
    creating: false,
    
    // 新建模板相关
    showCreateTemplateDialog: false,
    createTemplateForm: {
      name: '',
      totalTimes: '',
      validDays: '',
      showOnHomepage: false,
      description: ''
    },
    creatingTemplate: false,
    
    // 编辑模板相关
    showEditTemplateDialog: false,
    editTemplateForm: {
      name: '',
      totalTimes: '',
      validDays: '',
      showOnHomepage: false,
      description: ''
    },
    editingTemplate: false,
    currentEditTemplateId: null,
    

    
    // 其他弹窗
    showActionDialog: false,
    actionType: '',
    actionCardId: '',
    delayDays: 30,
    showIssueDialog: false,
    issueForm: {
      userOpenid: '',
      selectedUser: null  // 新增：选中的用户对象
    },
    issuing: false,
    showIssueConfirmDialog: false,
    targetUserInfo: null,

    // 用户选择器相关
    userList: [],           // 用户列表
    filteredUserList: [],   // 过滤后的用户列表
    userSearchKeyword: '',  // 搜索关键词
    loadingUsers: false,    // 加载用户列表状态
    showRevokeConfirmDialog: false,
    revokeCardInfo: null,
    showEditDialog: false,
    editForm: {
      totalTimes: '',
      validDays: '',
      validFrom: '',
      validTo: ''
    },
    editing: false,
    currentEditCardId: null,
    createDatePickerVisible: false,
    createDatePickerValue: '',
    createDatePickerField: '',
    editDatePickerVisible: false,
    editDatePickerValue: '',
    editDatePickerField: ''
  },
  onLoad() {
    // 隐藏TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setSelectedValue(-1);
    }
    
    showLoading(this, '加载中...');
    this.loadCardList();
    this.loadTemplateList();
    hideToast(this);
  },

  onShow() {
    // 每次显示页面时都隐藏TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setSelectedValue(-1);
    }
    
    // 每次显示页面时自动刷新数据
    this.refreshData();
  },

  // 刷新数据方法
  async refreshData() {
    try {
      if (this.data.topActiveTab === 'card') {
        await this.loadCardList();
      } else {
        await this.loadTemplateList();
      }
    } catch (error) {
      console.error('刷新数据失败:', error);
      showToast(this, { message: '刷新数据失败', theme: 'error' });
    }
  },

  async onPullDownRefresh() {
    await this.refreshData();
    wx.stopPullDownRefresh();
  },

  // 顶部选项卡切换
  async onTopTabsChange(e) {
    const value = e.detail.value;
    this.setData({ topActiveTab: value });
    
    if (value === 'card') {
      await this.loadCardList();
    } else {
      await this.loadTemplateList();
    }
  },

  // 考勤卡维护子选项卡切换
  onCardTabsChange(e) {
    const value = e.currentTarget.dataset.value || e.detail.value;
    console.log('切换考勤卡选项卡:', value);

    this.setData({
      cardActiveTab: value,
      cardSearchKeyword: '', // 切换选项卡时清空搜索
      cardSearchExpanded: false // 收起搜索框
    });

    this.updateDisplayCardList();
    this.updateVisibleCardList();
  },

  // 模板维护子选项卡切换
  onTemplateTabsChange(e) {
    const value = e.currentTarget.dataset.value || e.detail.value;
    console.log('切换模板选项卡:', value);

    this.setData({
      templateActiveTab: value,
      templateSearchKeyword: '', // 切换选项卡时清空搜索
      templateSearchExpanded: false // 收起搜索框
    });

    this.updateDisplayTemplateList();
    this.updateVisibleTemplateList();
  },

  // 考勤卡搜索相关方法
  onExpandCardSearch() {
    console.log('展开考勤卡搜索框');
    this.setData({ cardSearchExpanded: true });
  },

  onCollapseCardSearch() {
    console.log('收起考勤卡搜索框');
    this.setData({
      cardSearchExpanded: false,
      cardSearchKeyword: '' // 收起时清空搜索
    });
    this.updateVisibleCardList();
  },

  onCardSearchInput(e) {
    const keyword = e.detail.value;
    console.log('考勤卡搜索输入:', keyword);
    this.setData({ cardSearchKeyword: keyword });
    // 实时搜索
    this.updateVisibleCardList();
  },

  onCardSearchSubmit(e) {
    const keyword = e.detail.value;
    console.log('考勤卡搜索提交:', keyword);
    this.setData({ cardSearchKeyword: keyword });
    this.updateVisibleCardList();
  },

  onCardSearchBlur() {
    // 搜索框失焦时不自动收起，保持用户体验
    console.log('考勤卡搜索框失焦');
  },

  onCardSearchClear() {
    console.log('清空考勤卡搜索');
    this.setData({ cardSearchKeyword: '' });
    this.updateVisibleCardList();
  },

  // 模板搜索相关方法
  onExpandTemplateSearch() {
    console.log('展开模板搜索框');
    this.setData({ templateSearchExpanded: true });
  },

  onCollapseTemplateSearch() {
    console.log('收起模板搜索框');
    this.setData({
      templateSearchExpanded: false,
      templateSearchKeyword: '' // 收起时清空搜索
    });
    this.updateVisibleTemplateList();
  },

  onTemplateSearchInput(e) {
    const keyword = e.detail.value;
    console.log('模板搜索输入:', keyword);
    this.setData({ templateSearchKeyword: keyword });
    // 实时搜索
    this.updateVisibleTemplateList();
  },

  onTemplateSearchSubmit(e) {
    const keyword = e.detail.value;
    console.log('模板搜索提交:', keyword);
    this.setData({ templateSearchKeyword: keyword });
    this.updateVisibleTemplateList();
  },

  onTemplateSearchBlur() {
    // 搜索框失焦时不自动收起，保持用户体验
    console.log('模板搜索框失焦');
  },

  onTemplateSearchClear() {
    console.log('清空模板搜索');
    this.setData({ templateSearchKeyword: '' });
    this.updateVisibleTemplateList();
  },

  // 更新显示的考勤卡列表
  updateDisplayCardList() {
    const { cardActiveTab, allCardList, issuedCardList, unissuedCardList } = this.data;
    let cards = allCardList;
    if (cardActiveTab === 'issued') {
      cards = issuedCardList;
    } else if (cardActiveTab === 'unissued') {
      cards = unissuedCardList;
    }
    this.setData({ cards });
  },

  // 更新显示的模板列表
  updateDisplayTemplateList() {
    const { templateActiveTab, allTemplateList, homepageTemplateList } = this.data;
    let templateList = allTemplateList;
    if (templateActiveTab === 'homepage') {
      templateList = homepageTemplateList;
    }
    this.setData({ templateList });
  },

  // 更新可见考勤卡列表（经过搜索筛选）
  updateVisibleCardList() {
    const { cards, cardSearchKeyword } = this.data;
    let filteredList = [...cards];

    // 根据搜索关键词筛选
    if (cardSearchKeyword && cardSearchKeyword.trim()) {
      const keyword = cardSearchKeyword.trim().toLowerCase();
      filteredList = filteredList.filter(card => {
        return (
          (card.cardNumber && card.cardNumber.toLowerCase().includes(keyword)) ||
          (card.userNickName && card.userNickName.toLowerCase().includes(keyword))
        );
      });
    }

    console.log('更新可见考勤卡列表:', {
      searchKeyword: cardSearchKeyword,
      totalCount: cards.length,
      filteredCount: filteredList.length
    });

    this.setData({ visibleCardList: filteredList });
  },

  // 更新可见模板列表（经过搜索筛选）
  updateVisibleTemplateList() {
    const { templateList, templateSearchKeyword } = this.data;
    let filteredList = [...templateList];

    // 根据搜索关键词筛选
    if (templateSearchKeyword && templateSearchKeyword.trim()) {
      const keyword = templateSearchKeyword.trim().toLowerCase();
      filteredList = filteredList.filter(template => {
        return (
          (template.name && template.name.toLowerCase().includes(keyword))
        );
      });
    }

    console.log('更新可见模板列表:', {
      searchKeyword: templateSearchKeyword,
      totalCount: templateList.length,
      filteredCount: filteredList.length
    });

    this.setData({ visibleTemplateList: filteredList });
  },
  // 加载考勤卡列表
  async loadCardList() {
    this.setData({ loading: true });
    try {
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'getCardList',
          data: {
            activeTab: this.data.cardActiveTab
          }
        }
      });
      
      if (result.result.success) {
        const now = new Date();
        
        const formatCardList = (cards) => {
          return cards.map(card => {
            const validFrom = this.formatDate(card.validFrom);
            const validTo = this.formatDate(card.validTo);
            const issueDate = this.formatDate(card.issueDate);
            const toDate = new Date(card.validTo);
            const isExpired = now > toDate;
            const isExpiring = !isExpired && (toDate - now < 7 * 24 * 60 * 60 * 1000);
            
            // 获取用户昵称
            const userNickName = card.userId ? (card.userNickName || '未知用户') : '未绑定';
            
            return {
              ...card,
              validFrom,
              validTo,
              issueDate,
              isExpired,
              isExpiring,
              userNickName
            };
          });
        };

        const allCards = formatCardList(result.result.data);
        
        // 按状态分类
        const issuedCards = allCards.filter(card => card.userId && card.userId !== '');
        const unissuedCards = allCards.filter(card => !card.userId || card.userId === '');
        
        this.setData({
          allCardList: allCards,
          issuedCardList: issuedCards,
          unissuedCardList: unissuedCards
        });
        
        this.updateDisplayCardList();
        this.updateVisibleCardList();
      } else {
        showToast(this, { message: result.result.message || '获取考勤卡失败', theme: 'error' });
        this.setData({ 
          allCardList: [],
          issuedCardList: [],
          unissuedCardList: []
        });
      }
    } catch (e) {
      console.error('获取考勤卡失败:', e);
      showToast(this, { message: '获取考勤卡失败', theme: 'error' });
      this.setData({ 
        allCardList: [],
        issuedCardList: [],
        unissuedCardList: []
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载模板列表
  async loadTemplateList() {
    this.setData({ loading: true });
    try {
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'getTemplateList',
          data: {
            activeTab: this.data.templateActiveTab
          }
        }
      });
      
      if (result.result.success) {
        const templates = result.result.data || [];
        
        // 格式化模板列表
        const formatTemplateList = (templates) => {
          return templates.map(template => ({
            ...template,
            id: template._id,
            showOnHomepageText: template.showOnHomepage ? '是' : '否'
          }));
        };

        this.setData({
          allTemplateList: formatTemplateList(templates),
          homepageTemplateList: formatTemplateList(templates.filter(t => t.showOnHomepage))
        });
        
        this.updateDisplayTemplateList();
        this.updateVisibleTemplateList();
      } else {
        showToast(this, { message: result.result.message || '加载模板列表失败', theme: 'error' });
      }
    } catch (error) {
      console.error('加载模板列表失败:', error);
      showToast(this, { message: '加载模板列表失败', theme: 'error' });
    } finally {
      this.setData({ loading: false });
    }
  },

  onCreateCard() {
    // 设置默认日期为今天
    const today = new Date();
    const todayStr = this.formatDate(today);
    
    this.setData({ 
      showCreateDialog: true,
      createForm: {
        totalTimes: '10',
        validDays: '365',
        validFrom: todayStr,
        validTo: todayStr
      }
    }, () => {
      // 初始化时计算结束日期
      this.calculateEndDate();
    });
  },

  // 添加模板按钮
  onAddTemplate() {
    this.setData({ 
      showCreateTemplateDialog: true,
      createTemplateForm: {
        name: '',
        totalTimes: '10',
        validDays: '365',
        showOnHomepage: false,
        description: ''
      }
    });
  },



  // 编辑模板按钮
  onEditTemplate(e) {
    const templateId = e.currentTarget.dataset.id;
    const template = this.data.templateList.find(t => t.id === templateId);
    
    if (template) {
      this.setData({ 
        showEditTemplateDialog: true,
        currentEditTemplateId: templateId,
        editTemplateForm: {
          name: template.name || '',
          totalTimes: template.totalTimes?.toString() || '10',
          validDays: template.validDays?.toString() || '365',
          showOnHomepage: template.showOnHomepage || false,
          description: template.description || ''
        }
      });
    }
  },



  // 删除模板
  async onDeleteTemplate(e) {
    const templateId = e.currentTarget.dataset.id;
    const template = this.data.templateList.find(t => t.id === templateId);
    
    if (!template) {
      showToast(this, { message: '模板不存在', theme: 'error' });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除模板"${template.name}"吗？此操作不可恢复。`,
      success: async (res) => {
        if (res.confirm) {
          await this._deleteTemplate(templateId);
        }
      }
    });
  },

  // 删除模板的具体实现
  async _deleteTemplate(templateId) {
    showLoading(this, '删除中...');
    try {
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'deleteTemplate',
          data: {
            id: templateId
          }
        }
      });
      
      if (result.result.success) {
        showToast(this, { message: '删除成功', theme: 'success' });
        await this.loadTemplateList();
      } else {
        showToast(this, { message: result.result.message || '删除失败', theme: 'error' });
      }
    } catch (error) {
      console.error('删除模板失败:', error);
      showToast(this, { message: '删除失败', theme: 'error' });
    } finally {
      hideToast(this);
    }
  },



  onCreateDialogClose() {
    this.setData({ 
      showCreateDialog: false,
      creating: false
    });
  },

  // 新建模板对话框相关方法
  onCreateTemplateDialogClose() {
    this.setData({ 
      showCreateTemplateDialog: false,
      creatingTemplate: false
    });
  },

  onCreateTemplateInputChange(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({ [`createTemplateForm.${field}`]: e.detail.value });
  },

  onCreateTemplateSwitchChange(e) {
    this.setData({ 'createTemplateForm.showOnHomepage': e.detail.value });
  },

  async onCreateTemplateDialogConfirm() {
    const form = this.data.createTemplateForm;
    
    // 验证表单
    if (!form.name || !form.name.trim()) {
      showToast(this, { message: '请输入模板名称', theme: 'warning' });
      return;
    }
    
    if (!form.totalTimes || form.totalTimes <= 0) {
      showToast(this, { message: '请输入有效的总次数', theme: 'warning' });
      return;
    }
    
    if (!form.validDays || form.validDays <= 0) {
      showToast(this, { message: '请输入有效的有效期天数', theme: 'warning' });
      return;
    }

    this.setData({ creatingTemplate: true });

    try {
      console.log('准备调用云函数，数据:', {
        action: 'addTemplate',
        data: {
          name: form.name.trim(),
          totalTimes: Number(form.totalTimes),
          validDays: Number(form.validDays),
          showOnHomepage: form.showOnHomepage,
          description: form.description?.trim() || ''
        }
      });

      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'addTemplate',
          data: {
            name: form.name.trim(),
            totalTimes: Number(form.totalTimes),
            validDays: Number(form.validDays),
            showOnHomepage: form.showOnHomepage,
            description: form.description?.trim() || ''
          }
        }
      });
      
      console.log('云函数返回结果:', result);
      
      if (result.result && result.result.success) {
        showToast(this, { message: '模板创建成功', theme: 'success' });
        this.setData({ showCreateTemplateDialog: false });
        await this.loadTemplateList();
      } else {
        const errorMessage = result.result?.message || result.result?.error || '创建失败';
        console.error('云函数返回错误:', result.result);
        showToast(this, { message: errorMessage, theme: 'error' });
      }
    } catch (error) {
      console.error('创建模板失败:', error);
      showToast(this, { message: '创建失败: ' + (error.message || '网络错误'), theme: 'error' });
    } finally {
      this.setData({ creatingTemplate: false });
    }
  },

  // 编辑模板对话框相关方法
  onEditTemplateDialogClose() {
    this.setData({ 
      showEditTemplateDialog: false,
      editingTemplate: false,
      currentEditTemplateId: null
    });
  },

  onEditTemplateInputChange(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({ [`editTemplateForm.${field}`]: e.detail.value });
  },

  onEditTemplateSwitchChange(e) {
    this.setData({ 'editTemplateForm.showOnHomepage': e.detail.value });
  },

  async onEditTemplateDialogConfirm() {
    const { editTemplateForm, currentEditTemplateId } = this.data;
    
    // 验证表单
    if (!editTemplateForm.name || !editTemplateForm.name.trim()) {
      showToast(this, { message: '请输入模板名称', theme: 'warning' });
      return;
    }
    
    if (!editTemplateForm.totalTimes || editTemplateForm.totalTimes <= 0) {
      showToast(this, { message: '请输入有效的总次数', theme: 'warning' });
      return;
    }
    
    if (!editTemplateForm.validDays || editTemplateForm.validDays <= 0) {
      showToast(this, { message: '请输入有效的有效期天数', theme: 'warning' });
      return;
    }

    this.setData({ editingTemplate: true });

    try {
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'updateTemplate',
          data: {
            id: currentEditTemplateId,
            name: editTemplateForm.name.trim(),
            totalTimes: Number(editTemplateForm.totalTimes),
            validDays: Number(editTemplateForm.validDays),
            showOnHomepage: editTemplateForm.showOnHomepage,
            description: editTemplateForm.description?.trim() || ''
          }
        }
      });

      if (result.result.success) {
        showToast(this, { message: '模板编辑成功', theme: 'success' });
        this.setData({ 
          showEditTemplateDialog: false,
          editingTemplate: false,
          currentEditTemplateId: null
        });
        await this.loadTemplateList();
      } else {
        showToast(this, { message: result.result.message || '编辑失败', theme: 'error' });
      }
    } catch (error) {
      console.error('编辑模板失败:', error);
      showToast(this, { message: '编辑失败: ' + (error.message || '网络错误'), theme: 'error' });
    } finally {
      this.setData({ editingTemplate: false });
    }
  },
  stopPropagation() {
    // 阻止事件冒泡
  },
  onInputChange(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({ [`createForm.${field}`]: e.detail.value });
  },
  onDateChange(e) {
    const field = e.currentTarget.dataset.field;
    const newDate = e.detail.value;
    
    this.setData({ [`createForm.${field}`]: newDate }, () => {
      // 如果修改的是开始日期，自动计算结束日期
      if (field === 'validFrom') {
        this.calculateEndDate();
      }
      // 如果修改的是结束日期，自动计算天数
      else if (field === 'validTo') {
        this.calculateValidDays();
      }
    });
  },

  onCreateDatePickerTap(e) {
    const field = e.currentTarget.dataset.field;
    const currentValue = this.data.createForm[field] || '';
    
    this.setData({
      createDatePickerVisible: true,
      createDatePickerValue: currentValue,
      createDatePickerField: field
    });
  },

  onCreateDatePickerConfirm(e) {
    const newDate = e.detail.value;
    const field = this.data.createDatePickerField;
    
    this.setData({ 
      [`createForm.${field}`]: newDate,
      createDatePickerVisible: false
    }, () => {
      // 如果修改的是开始日期，自动计算结束日期
      if (field === 'validFrom') {
        this.calculateEndDate();
      }
      // 如果修改的是结束日期，自动计算天数
      else if (field === 'validTo') {
        this.calculateValidDays();
      }
    });
  },

  onCreateDatePickerCancel() {
    this.setData({ createDatePickerVisible: false });
  },

  // 有效期天数变化
  onValidDaysChange(e) {
    const validDays = e.detail.value;
    this.setData({ 'createForm.validDays': validDays }, () => {
      // 根据开始日期和天数计算结束日期
      this.calculateEndDate();
    });
  },

  // 根据开始日期和天数计算结束日期
  calculateEndDate() {
    const { validFrom, validDays } = this.data.createForm;
    if (validFrom && validDays && validDays > 0) {
      const startDate = new Date(validFrom);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + parseInt(validDays) - 1); // 减1是因为包含开始日期
      
      this.setData({
        'createForm.validTo': this.formatDate(endDate)
      });
    }
  },

  // 根据开始日期和结束日期计算天数
  calculateValidDays() {
    const { validFrom, validTo } = this.data.createForm;
    if (validFrom && validTo) {
      const startDate = new Date(validFrom);
      const endDate = new Date(validTo);
      const diffTime = endDate.getTime() - startDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // 加1是因为包含开始和结束日期
      
      if (diffDays > 0) {
        this.setData({
          'createForm.validDays': diffDays.toString()
        });
      }
    }
  },
  onDelayDaysChange(e) {
    this.setData({ delayDays: Number(e.detail.value) || 1 });
  },

  // 绑定弹窗相关方法
  onIssueDialogClose() {
    this.setData({
      showIssueDialog: false,
      issueForm: {
        userOpenid: '',
        selectedUser: null
      },
      userSearchKeyword: '',
      filteredUserList: [],
      userList: []
    });
    this.currentIssueCardId = null;
  },

  // 加载用户列表
  async loadUserList() {
    this.setData({ loadingUsers: true });

    try {
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'getUserList',
          data: {
            page: 1,
            pageSize: 100  // 获取前100个用户，足够大部分场景使用
          }
        }
      });

      if (result.result && result.result.success) {
        const userList = result.result.data || [];
        this.setData({
          userList: userList,
          filteredUserList: userList  // 初始时显示所有用户
        });
      } else {
        this.showToast(result.result?.message || '获取用户列表失败');
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      this.showToast('获取用户列表失败: ' + (error.message || '网络错误'));
    } finally {
      this.setData({ loadingUsers: false });
    }
  },

  // 用户搜索
  onUserSearch(e) {
    const keyword = (e.detail.value || '').toLowerCase();
    this.setData({ userSearchKeyword: keyword });
    this.filterUsers(keyword);
  },

  // 清除搜索
  onUserSearchClear() {
    this.setData({ userSearchKeyword: '' });
    this.filterUsers('');
  },

  // 过滤用户列表
  filterUsers(keyword) {
    if (!keyword) {
      // 如果搜索关键词为空，显示所有用户
      this.setData({ filteredUserList: this.data.userList });
    } else {
      // 根据昵称和openid进行搜索
      const filtered = this.data.userList.filter(user => {
        const nickName = (user.nickName || '').toLowerCase();
        const openid = (user.openid || '').toLowerCase();
        return nickName.includes(keyword) || openid.includes(keyword);
      });
      this.setData({ filteredUserList: filtered });
    }
  },

  // 选择用户
  onSelectUser(e) {
    const user = e.currentTarget.dataset.user;
    this.setData({
      'issueForm.selectedUser': user,
      'issueForm.userOpenid': user.openid
    });
  },

  onIssueInputChange(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({ [`issueForm.${field}`]: e.detail.value });
  },

  async onIssueDialogConfirm() {
    const { selectedUser } = this.data.issueForm;

    if (!selectedUser) {
      this.showToast('请选择要绑定的用户');
      return;
    }

    if (!this.currentIssueCardId) {
      this.showToast('未找到要绑定的考勤卡');
      return;
    }

    // 直接使用选中的用户信息，显示确认弹窗
    this.setData({
      showIssueDialog: false,
      showIssueConfirmDialog: true,
      targetUserInfo: selectedUser,
      issuing: false
    });
  },

  // 确认绑定弹窗相关方法
  onIssueConfirmDialogClose() {
    this.setData({ 
      showIssueConfirmDialog: false,
      targetUserInfo: null
    });
    this.currentIssueCardId = null;
  },

  async onIssueConfirmDialogConfirm() {
    const { targetUserInfo } = this.data;
    
    if (!targetUserInfo || !this.currentIssueCardId) {
      this.showToast('信息不完整，请重新操作');
      return;
    }

    this.setData({ issuing: true });

    try {
      // 调用云函数绑定考勤卡
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'issueCard',
          data: {
            cardId: this.currentIssueCardId,
            userOpenid: targetUserInfo.openid
          }
        }
      });

      if (result.result && result.result.success) {
        showToast(this, { message: result.result.message || '绑定成功', theme: 'success' });
        this.onIssueConfirmDialogClose();
        await this.loadCardList(); // 刷新列表
      } else {
        this.showToast(result.result?.message || '绑定失败');
      }
    } catch (error) {
              console.error('绑定考勤卡失败:', error);
              this.showToast('绑定失败: ' + (error.message || '网络错误'));
    } finally {
      this.setData({ issuing: false });
    }
  },

  // 解绑确认弹窗相关方法
  onRevokeConfirmDialogClose() {
    this.setData({ 
      showRevokeConfirmDialog: false,
      revokeCardInfo: null
    });
  },

  async onRevokeConfirmDialogConfirm() {
    const { revokeCardInfo } = this.data;
    
    if (!revokeCardInfo) {
      this.showToast('信息不完整，请重新操作');
      return;
    }

    this.setData({ issuing: true });

    try {
      // 调用云函数解绑考勤卡
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'revokeCard',
          data: {
            cardId: revokeCardInfo._id
          }
        }
      });

      if (result.result && result.result.success) {
        showToast(this, { message: result.result.message || '解绑成功', theme: 'success' });
        this.onRevokeConfirmDialogClose();
        await this.loadCardList(); // 刷新列表
      } else {
        this.showToast(result.result?.message || '解绑失败');
      }
    } catch (error) {
              console.error('解绑考勤卡失败:', error);
              this.showToast('解绑失败: ' + (error.message || '网络错误'));
    } finally {
      this.setData({ issuing: false });
    }
  },

  // 显示轻提示
  showToast(message, theme = 'success') {
    showToast(this, { message, theme });
  },
  async onCreateDialogConfirm() {
    const form = this.data.createForm;
    
    // 详细校验
    if (!form.totalTimes || form.totalTimes <= 0) {
      this.showToast('请输入有效的总次数');
      return;
    }
    
    if (!form.validFrom) {
      this.showToast('请选择有效期开始日期');
      return;
    }
    
    if (!form.validDays || form.validDays <= 0) {
      this.showToast('请输入有效的有效期天数');
      return;
    }
    
    if (!form.validTo) {
      this.showToast('请选择有效期结束日期');
      return;
    }
    
    // 绑定日期自动设置为今天，无需验证
    
    // 检查日期逻辑
    if (new Date(form.validFrom) > new Date(form.validTo)) {
      this.showToast('有效期开始日期不能晚于结束日期');
      return;
    }
    
    this.setData({ creating: true });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'createCard',
          data: {
            totalTimes: Number(form.totalTimes),
            validFrom: form.validFrom,
            validTo: form.validTo
          }
        }
      });
      
      if (result.result.success) {
        showToast(this, { message: '新建成功', theme: 'success' });
        this.setData({ showCreateDialog: false });
        this.resetCreateForm();
        await this.loadCardList();
      } else {
        showToast(this, { message: result.result.message || '新建失败', theme: 'error' });
      }
    } catch (error) {
              console.error('新建考勤卡失败:', error);
      this.showToast('新建失败: ' + (error.message || '网络错误'));
    } finally {
      this.setData({ creating: false });
    }
  },
  async generateCardNumber() {
    try {
      const db = wx.cloud.database();
      const today = new Date();
      const dateStr = today.getFullYear().toString() + 
                     (today.getMonth() + 1).toString().padStart(2, '0') + 
                     today.getDate().toString().padStart(2, '0');
      
      // 查询今天的最大编号
      const result = await db.collection('membershipCard')
        .where({
          cardNumber: db.command.regex({
            regexp: `^${dateStr}\\d{4}$`,
            options: 'i'
          })
        })
        .orderBy('cardNumber', 'desc')
        .limit(1)
        .get();
      
      let nextNumber = 1;
      if (result.data.length > 0) {
        const lastCardNumber = result.data[0].cardNumber;
        const lastNumber = parseInt(lastCardNumber.slice(-4));
        nextNumber = lastNumber + 1;
      }
      
      return `${dateStr}${nextNumber.toString().padStart(4, '0')}`;
    } catch (error) {
      console.error('生成卡号失败:', error);
      // 如果查询失败，使用当前时间戳作为备选方案
      const today = new Date();
      const dateStr = today.getFullYear().toString() + 
                     (today.getMonth() + 1).toString().padStart(2, '0') + 
                     today.getDate().toString().padStart(2, '0');
      const timestamp = Date.now().toString().slice(-4);
      return `${dateStr}${timestamp}`;
    }
  },
  resetCreateForm() {
    // 设置默认日期为今天
    const today = new Date();
    const todayStr = this.formatDate(today);
    
    this.setData({
      createForm: {
        totalTimes: '',
        validDays: '365',
        validFrom: todayStr,
        validTo: todayStr
      }
    });
  },
  async onIssueCard(e) {
    const cardId = e.currentTarget.dataset.id;
    this.setData({
      showIssueDialog: true,
      issueForm: {
        userOpenid: '',
        selectedUser: null
      },
      issuing: false,
      userSearchKeyword: '',
      filteredUserList: []
    });
    // 保存当前要绑定的考勤卡ID
    this.currentIssueCardId = cardId;

    // 加载用户列表
    await this.loadUserList();
  },
  onRevokeCard(e) {
    const cardId = e.currentTarget.dataset.id;
    const card = this.data.cards.find(c => c._id === cardId);
    
    if (!card) {
      this.showToast('未找到考勤卡信息');
      return;
    }
    
    // 显示解绑确认弹窗
    this.setData({
      showRevokeConfirmDialog: true,
      revokeCardInfo: card
    });
  },
  onDelayCard(e) {
    showToast(this, { message: '开发中', theme: 'warning' });
  },
  onFreezeCard(e) {
    showToast(this, { message: '开发中', theme: 'warning' });
  },
  onDeleteCard(e) {
    this.setData({
      showActionDialog: true,
      actionType: 'delete',
      actionCardId: e.currentTarget.dataset.id
    });
  },

  // 编辑考勤卡相关方法
  onEditCard(e) {
    const cardId = e.currentTarget.dataset.id;
    const card = this.data.cards.find(c => c._id === cardId);
    
    if (!card) {
      this.showToast('未找到考勤卡信息');
      return;
    }

    // 计算有效期天数
    const startDate = new Date(card.validFrom);
    const endDate = new Date(card.validTo);
    const diffTime = endDate.getTime() - startDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

    // 计算使用情况
    const usedTimes = card.totalTimes - card.remainingTimes;
    const usageInfo = card.userId ?
      `已使用${usedTimes}次，剩余${card.remainingTimes}次` :
      '未绑定用户';

    this.setData({
      showEditDialog: true,
      currentEditCardId: cardId,
      editForm: {
        totalTimes: card.totalTimes.toString(),
        validDays: diffDays.toString(),
        validFrom: card.validFrom,
        validTo: card.validTo
      },
      editCardUsageInfo: usageInfo,
      editCardMinTotalTimes: card.userId ? usedTimes : 1  // 最小总次数限制
    });
  },

  onEditDialogClose() {
    this.setData({ 
      showEditDialog: false,
      editing: false,
      currentEditCardId: null
    });
  },

  onEditInputChange(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({ [`editForm.${field}`]: e.detail.value });
  },

  onEditDateChange(e) {
    const field = e.currentTarget.dataset.field;
    const newDate = e.detail.value;
    
    this.setData({ [`editForm.${field}`]: newDate }, () => {
      // 如果修改的是开始日期，自动计算结束日期
      if (field === 'validFrom') {
        this.calculateEditEndDate();
      }
      // 如果修改的是结束日期，自动计算天数
      else if (field === 'validTo') {
        this.calculateEditValidDays();
      }
    });
  },

  onEditDatePickerTap(e) {
    const field = e.currentTarget.dataset.field;
    const currentValue = this.data.editForm[field] || '';
    
    this.setData({
      editDatePickerVisible: true,
      editDatePickerValue: currentValue,
      editDatePickerField: field
    });
  },

  onEditDatePickerConfirm(e) {
    const newDate = e.detail.value;
    const field = this.data.editDatePickerField;
    
    this.setData({ 
      [`editForm.${field}`]: newDate,
      editDatePickerVisible: false
    }, () => {
      // 如果修改的是开始日期，自动计算结束日期
      if (field === 'validFrom') {
        this.calculateEditEndDate();
      }
      // 如果修改的是结束日期，自动计算天数
      else if (field === 'validTo') {
        this.calculateEditValidDays();
      }
    });
  },

  onEditDatePickerCancel() {
    this.setData({ editDatePickerVisible: false });
  },

  onEditValidDaysChange(e) {
    const validDays = e.detail.value;
    this.setData({ 'editForm.validDays': validDays }, () => {
      // 根据开始日期和天数计算结束日期
      this.calculateEditEndDate();
    });
  },

  calculateEditEndDate() {
    const { validFrom, validDays } = this.data.editForm;
    if (validFrom && validDays && validDays > 0) {
      const startDate = new Date(validFrom);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + parseInt(validDays) - 1);
      
      this.setData({
        'editForm.validTo': this.formatDate(endDate)
      });
    }
  },

  calculateEditValidDays() {
    const { validFrom, validTo } = this.data.editForm;
    if (validFrom && validTo) {
      const startDate = new Date(validFrom);
      const endDate = new Date(validTo);
      const diffTime = endDate.getTime() - startDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      
      if (diffDays > 0) {
        this.setData({
          'editForm.validDays': diffDays.toString()
        });
      }
    }
  },

  async onEditDialogConfirm() {
    const { editForm, currentEditCardId } = this.data;
    
    // 验证表单
    if (!editForm.totalTimes || !editForm.totalTimes.trim()) {
      this.showToast('请输入总次数');
      return;
    }
    
    if (!editForm.validDays || !editForm.validDays.trim()) {
      this.showToast('请输入有效期天数');
      return;
    }
    
    if (!editForm.validFrom) {
      this.showToast('请选择有效期开始日期');
      return;
    }
    
    if (!editForm.validTo) {
      this.showToast('请选择有效期结束日期');
      return;
    }

    const totalTimes = parseInt(editForm.totalTimes);
    const validDays = parseInt(editForm.validDays);
    
    if (totalTimes <= 0) {
      this.showToast('总次数必须大于0');
      return;
    }
    
    if (validDays <= 0) {
      this.showToast('有效期天数必须大于0');
      return;
    }

    this.setData({ editing: true });

    try {
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'updateCard',
          data: {
            cardId: currentEditCardId,
            totalTimes: totalTimes,
            validFrom: editForm.validFrom,
            validTo: editForm.validTo
          }
        }
      });

      if (result.result.success) {
        showToast(this, { message: '编辑成功', theme: 'success' });
        this.setData({ 
          showEditDialog: false,
          editing: false,
          currentEditCardId: null
        });
        await this.loadCardList();
      } else {
        showToast(this, { message: result.result.message || '编辑失败', theme: 'error' });
      }
    } catch (error) {
              console.error('编辑考勤卡失败:', error);
      this.showToast('编辑失败: ' + (error.message || '网络错误'));
    } finally {
      this.setData({ editing: false });
    }
  },
  onActionDialogClose() {
    this.setData({ showActionDialog: false });
  },
  async onActionDialogConfirm() {
    const { actionType, actionCardId, delayDays } = this.data;
    const db = wx.cloud.database();
    try {
      if (actionType === 'issue') {
        // 重新绑定：调用云函数延长有效期
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'reissueCard',
            data: {
              cardId: actionCardId
            }
          }
        });
        
        if (result.result && result.result.success) {
          this.showToast(result.result.message || '重新绑定成功');
        } else {
          this.showToast(result.result?.message || '重新绑定失败');
          return;
        }
      } else if (actionType === 'revoke') {
        // 解绑：调用云函数解除用户关联
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'revokeCard',
            data: {
              cardId: actionCardId
            }
          }
        });
        
        if (result.result && result.result.success) {
          this.showToast(result.result.message || '解绑成功');
        } else {
          this.showToast(result.result?.message || '解绑失败');
          return;
        }
      } else if (actionType === 'delay') {
        // 延期：调用云函数
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'delayCard',
            data: {
              cardId: actionCardId,
              delayDays: delayDays
            }
          }
        });
        
        if (result.result.success) {
          showToast(this, { message: result.result.message || '已延期', theme: 'success' });
        } else {
          showToast(this, { message: result.result.message || '延期失败', theme: 'error' });
          return;
        }
      } else if (actionType === 'freeze') {
        // 冻结：调用云函数
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'freezeCard',
            data: {
              cardId: actionCardId
            }
          }
        });
        
        if (result.result.success) {
          showToast(this, { message: result.result.message || '已冻结', theme: 'success' });
        } else {
          showToast(this, { message: result.result.message || '冻结失败', theme: 'error' });
          return;
        }
      } else if (actionType === 'delete') {
        try {
          const result = await wx.cloud.callFunction({
            name: 'adminManagement',
            data: {
              action: 'deleteCard',
              data: {
                cardId: actionCardId
              }
            }
          });
          
          if (result.result.success) {
            showToast(this, { message: '已删除', theme: 'success' });
          } else {
            showToast(this, { message: result.result.message || '删除失败', theme: 'error' });
            return;
          }
        } catch (deleteError) {
          showToast(this, { message: '删除失败: ' + deleteError.message, theme: 'error' });
          return;
        }
      }
      this.setData({ showActionDialog: false });
      await this.loadCardList();
    } catch (e) {
      showToast(this, { message: '操作失败', theme: 'error' });
    }
  },
  addYear(dateStr) {
    const d = new Date(dateStr);
    d.setFullYear(d.getFullYear() + 1);
    return this.formatDate(d);
  },
  addDays(dateStr, days) {
    const d = new Date(dateStr);
    d.setDate(d.getDate() + days);
    return this.formatDate(d);
  },

  formatDate(dateStr) {
    if (!dateStr) return '';
    const d = new Date(dateStr);
    if (isNaN(d.getTime())) return '';
    return `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}-${d.getDate().toString().padStart(2,'0')}`;
  },
  onPullDownRefresh() {
    this.refreshData().then(() => wx.stopPullDownRefresh());
  }
}); 