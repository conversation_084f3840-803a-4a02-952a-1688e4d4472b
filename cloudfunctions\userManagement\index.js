// userManagement/index.js
// 用户管理云函数，统一处理用户相关操作

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 数据库集合
const USERS_COLLECTION = 'users';

/**
 * 云函数入口函数
 * @param {Object} event 事件对象
 * @param {string} event.action 操作类型
 * @param {Object} event.data 数据
 * @param {Object} context 上下文
 */
exports.main = async (event, context) => {
  const { action, data } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  
  try {
    switch (action) {
      case 'login':
        // 修复：传入正确的用户信息结构
        return await handleLogin(openid, data.userInfo || data);
      case 'checkUser':
        return await checkUser(openid);
      case 'updateAvatar':
        return await updateAvatar(openid, data);
      case 'updateUserInfo':
        return await updateUserInfo(openid, data);
      case 'getUserInfo':
        return await getUserInfo(openid);
      case 'testDatabaseAccess':
        return await testDatabaseAccess(openid);
      case 'getUserStats':
        return await getUserStats(openid);
      default:
        return {
          success: false,
          message: '未知操作类型'
        };
    }
  } catch (error) {
    console.error('用户管理操作失败:', error);
    return {
      success: false,
      message: '操作失败',
      error: error.message
    };
  }
};

/**
 * 处理用户登录/注册
 * @param {string} openid 用户openid
 * @param {Object} userInfo 用户信息
 */
async function handleLogin(openid, userInfo) {
  console.log('=== handleLogin 开始 ===');
  console.log('openid:', openid);
  console.log('userInfo:', JSON.stringify(userInfo, null, 2));

  try {
    // 检查用户是否已存在
    console.log('检查用户是否已存在...');
    const userResult = await db.collection(USERS_COLLECTION).where({
      openid: openid
    }).get();

    console.log('用户查询结果:', {
      total: userResult.data.length,
      hasData: userResult.data.length > 0
    });

    let userData = null;
    
    if (userResult.data.length === 0) {
      // 新用户，创建用户记录
      const newUser = {
        openid: openid,
        nickName: userInfo.nickName || '',
        avatarUrl: userInfo.avatarUrl || '',
        gender: userInfo.gender || 0,
        language: userInfo.language || 'zh_CN',
        roles: userInfo.roles || ['学员'],
        createTime: db.serverDate(),
        updateTime: db.serverDate(),
        lastLoginTime: db.serverDate()
      };
      
      const addResult = await db.collection(USERS_COLLECTION).add({
        data: newUser
      });
      
      userData = {
        ...newUser,
        _id: addResult._id
      };
    } else {
      // 老用户，更新登录时间和用户信息
      const existingUser = userResult.data[0];
      
      // 处理角色更新，确保兼容旧数据
      let updatedRoles = existingUser.roles;
      
      if (!updatedRoles && existingUser.role) {
        updatedRoles = [existingUser.role];
      }
      
      if (userInfo.roles) {
        updatedRoles = userInfo.roles;
      }
      
      if (!Array.isArray(updatedRoles)) {
        updatedRoles = ['学员'];
      }
      
      // 更新用户信息
      await db.collection(USERS_COLLECTION).doc(existingUser._id).update({
        data: {
          nickName: userInfo && userInfo.nickName || existingUser.nickName,
          avatarUrl: userInfo && userInfo.avatarUrl || existingUser.avatarUrl,
          roles: updatedRoles,
          updateTime: db.serverDate(),
          lastLoginTime: db.serverDate()
        }
      });
      
      userData = {
        _id: existingUser._id,
        openid: existingUser.openid,
        nickName: userInfo && userInfo.nickName || existingUser.nickName,
        avatarUrl: userInfo && userInfo.avatarUrl || existingUser.avatarUrl,
        gender: existingUser.gender,
        language: existingUser.language,
        roles: updatedRoles,
        createTime: existingUser.createTime,
        updateTime: db.serverDate(),
        lastLoginTime: db.serverDate()
      };
    }
    
    return {
      success: true,
      openid: openid,
      userInfo: userData,
      roles: userData.roles
    };
    
  } catch (error) {
    console.error('登录失败:', error);
    return {
      success: false,
      message: '登录失败，请重试',
      error: error.message
    };
  }
}

/**
 * 检查用户是否存在
 * @param {string} openid 用户openid
 */
async function checkUser(openid) {
  try {
    const userResult = await db.collection(USERS_COLLECTION).where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: true,
        openid: openid,
        userInfo: null,
        isNewUser: true
      };
    } else {
      const existingUser = userResult.data[0];
      
      // 更新最后登录时间
      await db.collection(USERS_COLLECTION).doc(existingUser._id).update({
        data: {
          lastLoginTime: db.serverDate()
        }
      });
      
      // 返回更新后的用户信息
      const updatedUser = {
        ...existingUser,
        lastLoginTime: new Date()
      };
      
      return {
        success: true,
        openid: openid,
        userInfo: updatedUser,
        isNewUser: false
      };
    }
  } catch (error) {
    console.error('检查用户失败:', error);
    return {
      success: false,
      message: '检查用户失败',
      error: error.message
    };
  }
}

/**
 * 更新用户头像
 * @param {string} openid 用户openid
 * @param {Object} data 数据
 */
async function updateAvatar(openid, data) {
  console.log('=== updateAvatar 开始 ===');
  console.log('openid:', openid);
  console.log('data:', JSON.stringify(data, null, 2));

  try {
    const { avatarUrl } = data;

    // 详细验证头像URL
    if (!avatarUrl) {
      console.error('头像URL为空');
      return {
        success: false,
        message: '缺少头像URL'
      };
    }

    if (typeof avatarUrl !== 'string') {
      console.error('头像URL类型错误:', typeof avatarUrl);
      return {
        success: false,
        message: '头像URL格式错误'
      };
    }

    if (!avatarUrl.startsWith('cloud://')) {
      console.warn('头像URL不是云存储格式:', avatarUrl);
    }

    console.log('验证通过，开始查找用户...');

    // 查找用户
    const userResult = await db.collection(USERS_COLLECTION).where({ openid }).get();
    console.log('用户查询结果:', {
      total: userResult.data.length,
      hasData: userResult.data.length > 0
    });

    if (userResult.data.length === 0) {
      console.error('用户不存在，openid:', openid);
      return {
        success: false,
        message: '用户不存在'
      };
    }

    const user = userResult.data[0];
    const userId = user._id;
    console.log('找到用户:', {
      userId: userId,
      nickName: user.nickName,
      currentAvatarUrl: user.avatarUrl
    });

    console.log('开始更新头像...');
    const updateResult = await db.collection(USERS_COLLECTION).doc(userId).update({
      data: {
        avatarUrl,
        updateTime: db.serverDate()
      }
    });

    console.log('数据库更新结果:', updateResult);
    console.log('=== updateAvatar 成功 ===');

    return {
      success: true,
      avatarUrl,
      userId: userId,
      updateResult: updateResult
    };
  } catch (error) {
    console.error('=== updateAvatar 失败 ===');
    console.error('错误详情:', error);
    console.error('错误堆栈:', error.stack);
    console.error('错误消息:', error.message);
    console.error('错误代码:', error.code);

    return {
      success: false,
      message: '更新头像失败',
      error: error.message,
      errorCode: error.code,
      errorStack: error.stack
    };
  }
}

/**
 * 更新用户信息
 * @param {string} openid 用户openid
 * @param {Object} data 用户数据
 */
async function updateUserInfo(openid, data) {
  console.log('=== updateUserInfo 开始 ===');
  console.log('openid:', openid);
  console.log('data:', JSON.stringify(data, null, 2));

  try {
    // 验证输入数据
    if (!data || typeof data !== 'object') {
      console.error('更新数据无效:', data);
      return {
        success: false,
        message: '更新数据无效'
      };
    }

    // 特别检查头像URL
    if (data.avatarUrl) {
      console.log('包含头像更新，头像URL:', data.avatarUrl);
      console.log('头像URL类型:', typeof data.avatarUrl);
      console.log('头像URL长度:', data.avatarUrl.length);

      if (!data.avatarUrl.startsWith('cloud://')) {
        console.warn('头像URL不是云存储格式:', data.avatarUrl);
      }
    }

    console.log('开始查找用户...');

    // 查找用户
    const userResult = await db.collection(USERS_COLLECTION).where({ openid }).get();
    console.log('用户查询结果:', {
      total: userResult.data.length,
      hasData: userResult.data.length > 0
    });

    if (userResult.data.length === 0) {
      console.error('用户不存在，openid:', openid);
      return {
        success: false,
        message: '用户不存在'
      };
    }

    const user = userResult.data[0];
    const userId = user._id;
    console.log('找到用户:', {
      userId: userId,
      nickName: user.nickName,
      currentAvatarUrl: user.avatarUrl
    });

    const updateData = {
      ...data,
      updateTime: db.serverDate()
    };

    console.log('准备更新的数据:', JSON.stringify(updateData, null, 2));
    console.log('开始更新用户信息...');

    const updateResult = await db.collection(USERS_COLLECTION).doc(userId).update({
      data: updateData
    });

    console.log('数据库更新结果:', updateResult);
    console.log('=== updateUserInfo 成功 ===');

    return {
      success: true,
      message: '用户信息更新成功',
      userId: userId,
      updateResult: updateResult,
      updatedData: updateData
    };
  } catch (error) {
    console.error('=== updateUserInfo 失败 ===');
    console.error('错误详情:', error);
    console.error('错误堆栈:', error.stack);
    console.error('错误消息:', error.message);
    console.error('错误代码:', error.code);

    return {
      success: false,
      message: '更新用户信息失败',
      error: error.message,
      errorCode: error.code,
      errorStack: error.stack
    };
  }
}

/**
 * 获取用户信息
 * @param {string} openid 用户openid
 */
async function getUserInfo(openid) {
  try {
    const userResult = await db.collection(USERS_COLLECTION).where({ openid }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    return {
      success: true,
      userInfo: userResult.data[0]
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      success: false,
      message: '获取用户信息失败',
      error: error.message
    };
  }
}

/**
 * 测试数据库访问权限
 * @param {string} openid 用户openid
 */
async function testDatabaseAccess(openid) {
  console.log('=== testDatabaseAccess 开始 ===');
  console.log('openid:', openid);

  try {
    // 测试读取权限
    console.log('测试读取权限...');
    const readResult = await db.collection(USERS_COLLECTION).where({ openid }).get();
    console.log('读取结果:', {
      success: true,
      count: readResult.data.length,
      hasData: readResult.data.length > 0
    });

    if (readResult.data.length > 0) {
      const user = readResult.data[0];
      console.log('用户数据:', {
        _id: user._id,
        nickName: user.nickName,
        avatarUrl: user.avatarUrl,
        roles: user.roles
      });

      // 测试写入权限
      console.log('测试写入权限...');
      const testUpdateResult = await db.collection(USERS_COLLECTION).doc(user._id).update({
        data: {
          lastTestTime: db.serverDate()
        }
      });

      console.log('写入测试结果:', testUpdateResult);

      return {
        success: true,
        message: '数据库访问权限正常',
        readTest: { success: true, count: readResult.data.length },
        writeTest: { success: true, result: testUpdateResult }
      };
    } else {
      return {
        success: true,
        message: '用户不存在，但读取权限正常',
        readTest: { success: true, count: 0 },
        writeTest: { skipped: true, reason: '用户不存在' }
      };
    }
  } catch (error) {
    console.error('=== testDatabaseAccess 失败 ===');
    console.error('错误详情:', error);
    console.error('错误堆栈:', error.stack);
    console.error('错误消息:', error.message);
    console.error('错误代码:', error.code);

    return {
      success: false,
      message: '数据库访问权限测试失败',
      error: error.message,
      errorCode: error.code,
      errorStack: error.stack
    };
  }
}

/**
 * 获取用户统计数据
 * @param {string} openid 用户openid
 */
async function getUserStats(openid) {
  try {
    const bookingsCollection = db.collection('bookings');

    // 获取参与活动总数
    const activityCountResult = await bookingsCollection.where({
      userId: openid
    }).count();

    // 暂时返回模拟的打卡次数，因为逻辑未定
    const checkInCount = Math.floor(Math.random() * 50) + 10; // 10-60次打卡

    return {
      success: true,
      data: {
        activityCount: activityCountResult.total,
        checkInCount: checkInCount
      }
    };
  } catch (error) {
    console.error('获取用户统计数据失败:', error);
    return {
      success: false,
      message: '获取用户统计数据失败',
      error: error.message
    };
  }
}