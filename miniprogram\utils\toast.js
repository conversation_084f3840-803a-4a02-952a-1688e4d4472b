 /**
 * TDesign Toast 工具函数
 * 统一管理所有提示信息，替换原生的 wx.showLoading 和 wx.showToast
 */

/**
 * 显示Toast提示
 * @param {Object} page - 页面实例
 * @param {Object} options - 配置选项
 * @param {string} options.message - 提示信息
 * @param {string} options.theme - 主题类型：success/error/warning/loading
 * @param {number} options.duration - 显示时长（毫秒），loading类型时为0表示不自动隐藏
 * @param {boolean} options.loading - 是否为加载状态
 */
export function showToast(page, { message, theme = 'success', duration = 2000, loading = false }) {
    const toast = page.selectComponent('#t-toast');
    if (toast) {
      if (loading) {
        // loading状态不自动隐藏
        toast.show({ message, theme: 'loading', duration: 0 });
      } else {
        toast.show({ message, theme, duration });
      }
    } else {
      // 降级到原生API
      if (loading) {
        wx.showLoading({ title: message });
      } else {
        wx.showToast({ 
          title: message, 
          icon: theme === 'success' ? 'success' : (theme === 'error' ? 'error' : 'none'),
          duration: duration
        });
      }
    }
  }
  
  /**
   * 隐藏Toast提示
   * @param {Object} page - 页面实例
   */
  export function hideToast(page) {
    const toast = page.selectComponent('#t-toast');
    if (toast) {
      toast.hide();
    } else {
      wx.hideLoading();
    }
  }
  
  /**
   * 显示加载中提示
   * @param {Object} page - 页面实例
   * @param {string} message - 提示信息，默认为"加载中..."
   */
  export function showLoading(page, message = '加载中...') {
    showToast(page, { message, theme: 'loading', loading: true });
  }
  
  /**
   * 显示成功提示
   * @param {Object} page - 页面实例
   * @param {string} message - 提示信息
   * @param {number} duration - 显示时长
   */
  export function showSuccess(page, message, duration = 2000) {
    showToast(page, { message, theme: 'success', duration });
  }
  
  /**
   * 显示错误提示
   * @param {Object} page - 页面实例
   * @param {string} message - 提示信息
   * @param {number} duration - 显示时长
   */
  export function showError(page, message, duration = 3000) {
    showToast(page, { message, theme: 'error', duration });
  }
  
  /**
   * 显示警告提示
   * @param {Object} page - 页面实例
   * @param {string} message - 提示信息
   * @param {number} duration - 显示时长
   */
  export function showWarning(page, message, duration = 3000) {
    showToast(page, { message, theme: 'warning', duration });
  }
  
  /**
   * 显示普通提示
   * @param {Object} page - 页面实例
   * @param {string} message - 提示信息
   * @param {number} duration - 显示时长
   */
  export function showInfo(page, message, duration = 2000) {
    showToast(page, { message, theme: 'success', duration });
  }