/* profile-edit.wxss */
/* 个人资料编辑页面样式文件 */

/**
 * 样式设计理念：
 *
 * 1. 现代化设计：
 *    - 圆角卡片布局
 *    - 柔和的阴影效果
 *    - 清晰的视觉层次
 *    - 舒适的色彩搭配
 *
 * 2. 移动端优化：
 *    - 响应式设计
 *    - 安全区域适配
 *    - 触摸友好的交互区域
 *    - 流畅的动画过渡
 *
 * 3. 用户体验：
 *    - 清晰的信息层次
 *    - 直观的操作反馈
 *    - 一致的设计语言
 *    - 无障碍访问支持
 */

/**
 * 根容器样式
 *
 * 设计考虑：
 * - 全屏布局，确保内容充满整个屏幕
 * - 底部预留安全区域，避免被底部指示器遮挡
 * - 浅灰色背景，与白色卡片形成对比
 * - 使用系统字体栈，确保最佳的文字显示效果
 */
.container {
  /* 基础内边距：16px提供舒适的边距 */
  padding: 16px;

  /**
   * 底部安全区域适配
   *
   * calc()计算说明：
   * - 16px: 基础底部边距
   * - 120rpx: 提交按钮区域的高度
   * - env(safe-area-inset-bottom): iOS设备底部安全区域
   *
   * 目的：
   * - 确保内容不被底部指示器（Home Indicator）遮挡
   * - 在不同设备上保持一致的视觉效果
   * - 提供足够的空间放置提交按钮
   */
  padding-bottom: calc(16px + 120rpx + env(safe-area-inset-bottom));

  /* 背景色：浅灰色，营造层次感 */
  background-color: #f5f5f5;

  /* 最小高度：确保内容充满整个视口 */
  min-height: 100vh;

  /**
   * 字体栈设置
   *
   * 字体优先级：
   * 1. "PingFang SC": 苹果系统中文字体，iOS/macOS优先
   * 2. "PingFang": PingFang字体的通用名称
   * 3. "Helvetica Neue": 现代Helvetica字体，清晰易读
   * 4. "Helvetica": 经典Helvetica字体，广泛支持
   * 5. "Arial": Windows系统默认字体，兼容性好
   * 6. sans-serif: 无衬线字体通用名称，最后的备选
   *
   * 设计原则：
   * - 优先使用系统原生字体，性能最佳
   * - 确保在不同平台上的一致性
   * - 无衬线字体在小屏幕上更清晰
   */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /**
   * 盒模型设置
   *
   * border-box说明：
   * - padding和border包含在元素的总宽度内
   * - 避免因内边距导致的布局溢出
   * - 更直观的尺寸计算方式
   * - 现代CSS布局的标准做法
   */
  box-sizing: border-box;
}

/**
 * 页面头部区域样式
 *
 * 功能：显示页面标题和副标题，提供页面上下文信息
 * 设计：居中对齐，层次分明，引导用户注意力
 */

/**
 * 头部容器样式
 *
 * 布局特点：
 * - 居中对齐：符合移动端的视觉习惯
 * - 充足的边距：与其他内容区分开来
 * - 垂直内边距：提供呼吸空间
 */
.header {
  /* 文本居中对齐：营造正式、专业的感觉 */
  text-align: center;

  /* 底部外边距：与表单区域保持适当距离 */
  margin-bottom: 32px;

  /* 垂直内边距：为标题和副标题提供舒适的空间 */
  padding: 24px 0;
}

/**
 * 主标题样式
 *
 * 视觉层次：
 * - 最大的字体尺寸，突出重要性
 * - 较重的字体粗细，增强视觉冲击力
 * - 深色文字，确保良好的对比度和可读性
 */
.title {
  /* 字体大小：24px，移动端的大标题标准尺寸 */
  font-size: 24px;

  /**
   * 字体粗细：600（半粗体）
   *
   * 字体粗细对比：
   * - 400: normal（正常）
   * - 500: medium（中等）
   * - 600: semi-bold（半粗体）
   * - 700: bold（粗体）
   *
   * 选择600的原因：
   * - 比正常文字更突出，但不会过于厚重
   * - 在移动设备上有良好的显示效果
   * - 符合现代UI设计的趋势
   */
  font-weight: 600;

  /* 文字颜色：深灰色，提供良好的对比度 */
  color: #333;

  /**
   * 显示模式：块级元素
   *
   * 作用：
   * - 确保标题独占一行
   * - 可以设置上下边距
   * - 与副标题形成垂直布局
   */
  display: block;

  /* 底部边距：与副标题保持适当间距 */
  margin-bottom: 8px;
}

/**
 * 副标题样式
 *
 * 设计目的：
 * - 提供补充说明信息
 * - 引导用户理解页面功能
 * - 与主标题形成视觉层次
 */
.subtitle {
  /* 字体大小：14px，适合辅助信息的尺寸 */
  font-size: 14px;

  /**
   * 文字颜色：中灰色
   *
   * 颜色选择原理：
   * - #666：中等灰度，既不会太突出也不会太淡
   * - 与主标题的#333形成层次对比
   * - 符合WCAG可访问性标准的对比度要求
   * - 在各种屏幕上都有良好的可读性
   */
  color: #666;

  /* 显示模式：块级元素，确保独占一行 */
  display: block;
}

/**
 * 表单区域样式
 *
 * 设计理念：
 * - 卡片式设计：现代化的UI风格
 * - 白色背景：与页面背景形成对比
 * - 圆角边框：柔和、友好的视觉效果
 * - 阴影效果：增加层次感和立体感
 */

/**
 * 表单容器样式
 *
 * 视觉特点：
 * - 卡片式布局，符合Material Design设计规范
 * - 适度的圆角，既现代又不过于圆润
 * - 微妙的阴影，增加浮起的视觉效果
 */
.form-section {
  /* 背景色：纯白色，提供清洁的内容区域 */
  background-color: #ffffff;

  /**
   * 圆角设置：12px
   *
   * 圆角选择原理：
   * - 12px：适中的圆角，既现代又不过于圆润
   * - 符合iOS和Android的设计规范
   * - 在不同尺寸的屏幕上都有良好的视觉效果
   * - 与其他UI组件保持一致的设计语言
   */
  border-radius: 12px;

  /* 内边距：24px，为内容提供充足的呼吸空间 */
  padding: 24px;

  /* 底部外边距：与其他元素保持适当距离 */
  margin-bottom: 24px;

  /**
   * 阴影效果设置
   *
   * box-shadow参数解析：
   * - 0: 水平偏移量（无偏移）
   * - 2px: 垂直偏移量（向下2px）
   * - 8px: 模糊半径（8px的模糊效果）
   * - rgba(0, 0, 0, 0.08): 阴影颜色（8%透明度的黑色）
   *
   * 设计目的：
   * - 创造浮起的视觉效果
   * - 增加页面的层次感
   * - 微妙而不突兀的阴影
   * - 符合现代扁平化设计的趋势
   */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/**
 * 区块标题样式
 *
 * 功能：标识每个表单区块的内容类型
 * 设计：清晰、简洁，与内容形成层次关系
 */
.section-title {
  /* 字体大小：16px，适合区块标题的尺寸 */
  font-size: 16px;

  /* 字体粗细：600，突出标题的重要性 */
  font-weight: 600;

  /* 文字颜色：深灰色，与主标题保持一致 */
  color: #333;

  /* 底部边距：与下方内容保持适当间距 */
  margin-bottom: 16px;

  /* 显示模式：块级元素，确保独占一行 */
  display: block;
}

/**
 * 头像区域样式
 *
 * 功能：用户头像的选择和显示区域
 * 设计：居中布局，突出头像的重要性
 */

/**
 * 头像区块容器
 *
 * 布局：为头像区域提供独立的空间
 */
.avatar-section {
  /* 底部外边距：与昵称区域保持适当距离 */
  margin-bottom: 32px;
}

/**
 * 头像包装器样式
 *
 * 设计特点：
 * - 固定尺寸：120px × 120px，适合移动端的头像显示
 * - 居中对齐：突出头像的重要性
 * - 相对定位：为内部的绝对定位元素提供参考
 * - Flexbox布局：确保内容完美居中
 */
.avatar-wrapper {
  /**
   * 定位模式：相对定位
   *
   * 作用：
   * - 为内部的avatar-button提供定位参考
   * - 不脱离文档流，保持正常的布局
   * - 允许子元素使用绝对定位覆盖在头像上
   */
  position: relative;

  /* 尺寸设置：120px × 120px，移动端头像的标准尺寸 */
  width: 120px;
  height: 120px;

  /**
   * 居中对齐设置
   *
   * margin: 0 auto：
   * - 水平居中对齐
   * - 上下边距为0，左右边距自动
   * - 16px底部边距，与下方元素保持距离
   */
  margin: 0 auto 16px auto;

  /**
   * Flexbox布局设置
   *
   * 目的：
   * - 确保头像或占位符在容器中完美居中
   * - 处理不同尺寸图片的居中显示
   * - 提供一致的视觉效果
   */
  display: flex;
  align-items: center;      /* 垂直居中 */
  justify-content: center;  /* 水平居中 */
}

/**
 * 头像图片样式
 *
 * 功能：显示用户选择的头像图片
 * 设计：圆角矩形，带有边框，现代化的视觉效果
 */
.avatar {
  /* 尺寸：填满整个包装器容器 */
  width: 100%;
  height: 100%;

  /**
   * 圆角设置：8px
   *
   * 设计考虑：
   * - 8px圆角：适度的圆润效果，不会过于圆润
   * - 与表单卡片的12px圆角形成层次关系
   * - 符合现代移动应用的设计趋势
   * - 在不同尺寸下都有良好的视觉效果
   */
  border-radius: 8px;

  /**
   * 边框设置
   *
   * 边框参数：
   * - 2px: 边框宽度，提供清晰的边界
   * - solid: 实线边框，简洁明了
   * - #e0e0e0: 浅灰色，柔和而不突兀
   *
   * 设计目的：
   * - 定义头像的边界
   * - 与背景形成对比
   * - 即使是白色背景的头像也能清晰显示
   */
  border: 2px solid #e0e0e0;
}

/**
 * 头像占位符样式
 *
 * 功能：当用户未选择头像时显示的占位区域
 * 设计：与头像图片保持一致的视觉风格
 */
.avatar-placeholder {
  /* 尺寸：与头像图片保持一致 */
  width: 100%;
  height: 100%;

  /* 圆角：与头像图片保持一致 */
  border-radius: 8px;

  /* 边框：与头像图片保持一致 */
  border: 2px solid #e0e0e0;

  /**
   * 背景色：浅灰色
   *
   * 颜色选择：
   * - #f5f5f5：与页面背景色相同
   * - 营造统一的视觉效果
   * - 提供柔和的占位背景
   */
  background-color: #f5f5f5;

  /**
   * Flexbox布局：确保图标居中显示
   *
   * 布局目的：
   * - 将TDesign的用户图标完美居中
   * - 提供一致的视觉效果
   * - 适应不同尺寸的图标
   */
  display: flex;
  align-items: center;      /* 垂直居中 */
  justify-content: center;  /* 水平居中 */
}

/**
 * 头像选择按钮样式
 *
 * 功能：透明的点击区域，覆盖在头像上方
 * 设计：完全透明，不影响头像显示，但提供点击功能
 */
.avatar-button {
  /**
   * 绝对定位：覆盖在头像上方
   *
   * 定位策略：
   * - 相对于avatar-wrapper进行定位
   * - 完全覆盖头像区域
   * - 不影响其他元素的布局
   */
  position: absolute;
  top: 0;
  left: 0;

  /* 尺寸：与头像区域完全一致 */
  width: 120px;
  height: 120px;

  /**
   * 按钮样式重置
   *
   * 重置目的：
   * - 移除浏览器默认的按钮样式
   * - 创建完全自定义的按钮外观
   * - 确保在不同平台上的一致性
   */
  background: transparent;  /* 透明背景 */
  border: none;            /* 无边框 */
  padding: 0;              /* 无内边距 */
  margin: 0;               /* 无外边距 */

  /* 圆角：与头像保持一致 */
  border-radius: 8px;
}

/**
 * 头像选择提示覆盖层样式
 *
 * 功能：用户点击时显示的半透明提示层
 * 设计：黑色半透明背景，白色提示文字
 */
.avatar-overlay {
  /**
   * 绝对定位：覆盖整个按钮区域
   *
   * 定位目的：
   * - 完全覆盖头像区域
   * - 提供视觉反馈
   * - 不影响正常的头像显示
   */
  position: absolute;
  top: 0;
  left: 0;

  /* 尺寸：与按钮区域完全一致 */
  width: 120px;
  height: 120px;

  /**
   * 背景色：半透明黑色
   *
   * rgba(0, 0, 0, 0.5)解析：
   * - 0, 0, 0: RGB值，纯黑色
   * - 0.5: 透明度50%
   *
   * 设计效果：
   * - 保持头像可见性
   * - 提供明显的视觉反馈
   * - 突出白色提示文字
   */
  background-color: rgba(0, 0, 0, 0.5);

  /**
   * Flexbox布局：提示文字居中显示
   */
  display: flex;
  align-items: center;      /* 垂直居中 */
  justify-content: center;  /* 水平居中 */

  /**
   * 透明度控制：默认隐藏
   *
   * 交互设计：
   * - 默认opacity: 0，完全透明（隐藏）
   * - 用户点击时显示（通过:active伪类）
   * - 提供即时的视觉反馈
   */
  opacity: 0;

  /**
   * 过渡动画：平滑的透明度变化
   *
   * transition参数：
   * - opacity: 动画属性（透明度）
   * - 0.3s: 动画时长（300毫秒）
   * - ease: 缓动函数（先快后慢）
   *
   * 用户体验：
   * - 平滑的显示/隐藏效果
   * - 避免突兀的变化
   * - 提供流畅的交互感受
   */
  transition: opacity 0.3s ease;

  /* 圆角：与其他元素保持一致 */
  border-radius: 8px;
}

/**
 * 按钮激活状态样式
 *
 * 功能：用户点击按钮时显示覆盖层
 * 交互：提供即时的视觉反馈
 */
.avatar-button:active .avatar-overlay {
  /**
   * 激活时显示覆盖层
   *
   * :active伪类：
   * - 用户按下按钮时触发
   * - 提供即时的视觉反馈
   * - 增强交互的响应性
   */
  opacity: 1;
}

/**
 * 头像选择提示文字样式
 *
 * 功能：引导用户进行头像选择操作
 * 设计：白色文字，小字号，居中显示
 */
.avatar-text {
  /* 文字颜色：白色，与半透明黑色背景形成对比 */
  color: #ffffff;

  /**
   * 字体大小：12px
   *
   * 尺寸选择：
   * - 12px：适合提示文字的小字号
   * - 不会过于突出，但仍然清晰可读
   * - 符合移动端的文字尺寸规范
   */
  font-size: 12px;

  /* 文本对齐：居中，与Flexbox布局配合 */
  text-align: center;
}

/**
 * 昵称输入区域样式
 *
 * 功能：用户昵称的输入和编辑
 * 设计：简洁的输入框，符合移动端的交互习惯
 */

/**
 * 昵称区块容器
 *
 * 布局：为昵称输入区域提供独立的空间
 */
.nickname-section {
  /* 底部外边距：与其他区域保持适当距离 */
  margin-bottom: 32px;
}

/**
 * 昵称输入框样式
 *
 * 设计特点：
 * - 全宽布局：充分利用可用空间
 * - 适中的高度：便于移动端触摸操作
 * - 清晰的边框：定义输入区域
 * - 舒适的内边距：提供良好的输入体验
 */
.nickname-input {
  /* 宽度：100%，充分利用容器宽度 */
  width: 100%;

  /**
   * 高度：48px
   *
   * 尺寸选择：
   * - 48px：移动端输入框的标准高度
   * - 符合iOS和Android的设计规范
   * - 提供足够的触摸目标区域
   * - 便于用户点击和输入
   */
  height: 48px;

  /**
   * 边框设置
   *
   * 边框参数：
   * - 1px: 细边框，不会过于突出
   * - solid: 实线边框，简洁明了
   * - #e0e0e0: 浅灰色，柔和的视觉效果
   */
  border: 1px solid #e0e0e0;

  /* 圆角：8px，与其他元素保持一致 */
  border-radius: 8px;

  /**
   * 内边距：左右16px
   *
   * 设计考虑：
   * - 为文字提供舒适的输入空间
   * - 避免文字紧贴边框
   * - 提供良好的视觉平衡
   */
  padding: 0 16px;

  /**
   * 字体大小：16px
   *
   * 尺寸选择：
   * - 16px：移动端输入文字的标准尺寸
   * - 在iOS上避免自动缩放的最小字号
   * - 确保良好的可读性
   * - 与其他文字元素保持协调
   */
  font-size: 16px;

  /* 文字颜色：深灰色，确保良好的对比度 */
  color: #333;

  /* 背景色：白色，与表单卡片保持一致 */
  background-color: #ffffff;

  /**
   * 盒模型：border-box
   *
   * 作用：
   * - padding和border包含在总宽度内
   * - 避免因内边距导致的宽度溢出
   * - 确保输入框的实际宽度为100%
   */
  box-sizing: border-box;
}

/**
 * 输入框聚焦状态样式
 *
 * 功能：用户点击输入框时的视觉反馈
 * 设计：蓝色边框，突出当前输入区域
 */
.nickname-input:focus {
  /**
   * 聚焦时的边框颜色
   *
   * 颜色选择：
   * - #0052d9：品牌蓝色，与TDesign主题色一致
   * - 提供明显的视觉反馈
   * - 突出当前活动的输入区域
   * - 符合用户的交互预期
   */
  border-color: #0052d9;
}

/**
 * 提交按钮区域样式
 *
 * 功能：为提交按钮提供独立的布局空间
 * 设计：简洁的容器，突出按钮的重要性
 */
.submit-section {
  /**
   * 底部外边距：24px
   *
   * 设计目的：
   * - 与其他内容保持适当距离
   * - 为按钮提供独立的视觉空间
   * - 避免与底部安全区域冲突
   */
  margin-bottom: 24px;
}

/**
 * 响应式设计
 *
 * 目标设备：小屏幕设备（宽度 ≤ 375px）
 * 设计目的：在小屏幕上优化布局和尺寸
 *
 * 适配策略：
 * - 减少内边距，节省空间
 * - 缩小头像尺寸，适应小屏幕
 * - 保持设计的一致性和可用性
 */
@media (max-width: 375px) {
  /**
   * 小屏幕下的容器样式调整
   *
   * 优化：减少内边距，为内容提供更多空间
   */
  .container {
    /* 内边距：从16px减少到12px */
    padding: 12px;
  }

  /**
   * 小屏幕下的表单区域调整
   *
   * 优化：减少内边距，保持内容的可读性
   */
  .form-section {
    /* 内边距：从24px减少到20px */
    padding: 20px;
  }

  /**
   * 小屏幕下的头像尺寸调整
   *
   * 优化：从120px缩小到100px
   * 目的：
   * - 节省屏幕空间
   * - 保持头像的清晰度
   * - 维持整体布局的平衡
   */
  .avatar-wrapper {
    width: 100px;
    height: 100px;
  }

  /**
   * 头像按钮尺寸同步调整
   *
   * 保持与头像包装器的尺寸一致
   */
  .avatar-button {
    width: 100px;
    height: 100px;
  }

  /**
   * 头像覆盖层尺寸同步调整
   *
   * 保持与头像区域的尺寸一致
   */
  .avatar-overlay {
    width: 100px;
    height: 100px;
  }
}

/**
 * 样式文件总结和学习价值
 *
 * 这个样式文件展示了现代移动端UI设计的核心原则：
 *
 * 1. 设计系统：
 *    - 一致的颜色方案（#333, #666, #e0e0e0等）
 *    - 统一的圆角规范（8px, 12px）
 *    - 协调的间距系统（16px, 24px, 32px）
 *    - 标准的字体尺寸（12px, 14px, 16px, 24px）
 *
 * 2. 移动端优化：
 *    - 触摸友好的尺寸（48px高度的输入框）
 *    - 安全区域适配（env(safe-area-inset-bottom)）
 *    - 响应式设计（@media查询）
 *    - 合适的字体大小（避免iOS自动缩放）
 *
 * 3. 用户体验：
 *    - 清晰的视觉层次（不同的字体大小和颜色）
 *    - 即时的交互反馈（:focus, :active状态）
 *    - 平滑的动画过渡（transition效果）
 *    - 无障碍访问支持（良好的对比度）
 *
 * 4. 现代CSS技术：
 *    - Flexbox布局（居中对齐）
 *    - CSS变量概念（一致的颜色值）
 *    - 盒模型控制（box-sizing: border-box）
 *    - 高级选择器（伪类、后代选择器）
 *
 * 5. 设计趋势：
 *    - 卡片式布局（Material Design影响）
 *    - 微妙的阴影效果（增加层次感）
 *    - 圆角设计（现代化、友好的视觉效果）
 *    - 扁平化设计（简洁、清晰的界面）
 *
 * 对于有建筑师背景的开发者：
 * - CSS布局类似于建筑平面图的空间规划
 * - 颜色和字体系统类似于建筑的材料和色彩方案
 * - 响应式设计类似于建筑的功能分区和适应性设计
 * - 用户体验设计类似于建筑的人性化和舒适性考虑
 */