// constants.js
// 系统常量定义 - 这个文件定义了整个应用中使用的常量值，便于统一管理和修改

/**
 * 预约状态枚举 - 定义了系统中所有可能的预约状态
 * 使用对象形式定义枚举，每个属性代表一种状态
 */
// export关键字使这个常量可以被其他文件导入使用
export const BOOKING_STATUS = {
  UPCOMING: 'upcoming',    // 即将开始 - 预约已确认但还未开始
  ONGOING: 'ongoing',      // 进行中 - 预约正在进行中
  COMPLETED: 'completed',   // 已完成 - 预约已结束并完成
  CANCELLED: 'cancelled'    // 已取消 - 预约被取消
};

/**
 * 预约状态文本映射 - 将状态代码转换为用户友好的显示文本
 * 使用计算属性名语法[BOOKING_STATUS.XXX]引用上面定义的常量值
 */
export const BOOKING_STATUS_TEXT = {
  // 方括号语法[BOOKING_STATUS.UPCOMING]表示使用BOOKING_STATUS.UPCOMING的值作为对象的键
  [BOOKING_STATUS.UPCOMING]: '即将开始', // 当状态为'upcoming'时显示'即将开始'
  [BOOKING_STATUS.ONGOING]: '进行中',
  [BOOKING_STATUS.COMPLETED]: '已完成',
  [BOOKING_STATUS.CANCELLED]: '已取消'
}; 