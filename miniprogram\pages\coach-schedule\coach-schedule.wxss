/* coach-schedule.wxss */

/*
 * 页面根元素样式 - 从schedule页面移植的渐变背景特效
 * 设置页面的基础高度和美观的渐变背景
 */
page {
  height: 100%;
  /* 精美的对角线渐变背景 - 从浅灰到淡蓝的优雅过渡 */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  /* 固定背景，滚动时背景不移动，增强视觉稳定性 */
  background-attachment: fixed;
}

/*
 * 页面容器样式
 * 确保页面容器占满整个可用高度，背景透明以显示页面渐变
 */
.page {
  height: 100%;
  background: transparent;
}
.container {
  /* 激进的空间优化：减少所有内边距，最大化课程列表显示区域 */
  padding: 8px;
  /* 完全移除底部内边距，让课程列表容器延伸到容器底部 */
  padding-bottom: 0;
  /* 使用透明背景，让页面的渐变背景特效显示出来 */
  background-color: transparent;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
  /* 使用flexbox布局，让内容充分利用空间 */
  display: flex;
  flex-direction: column;
  /* 关键优化：设置容器高度为calc(100vh - 120rpx)，减去底部导航栏高度
     这样容器本身就不会与底部导航栏重叠，课程列表可以充分利用剩余空间 */
  height: calc(100vh - 120rpx);
  /* 确保容器内容不会超出计算后的高度范围 */
  overflow: hidden;
}

/* 视图切换栏样式 - 激进的空间优化，最大化课程列表显示区域 */
.view-section {
  width: 100%;
  /* 完全移除底部间距，采用紧凑布局 */
  padding-bottom: 0px;
  /* 减少底部外边距，进一步节省垂直空间 */
  margin-bottom: 6px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0; /* 防止视图切换区域被压缩 */
}

/* 筛选栏样式 */
.filter-section {
  width: 100%;
  padding-bottom:0px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/*
 * 顶部标签栏容器样式 - 从schedule页面完整移植
 *
 * 功能：包含主选项卡组件的容器
 * 布局：优雅的线条式设计，增强视觉层次感
 * 设计理念：从简单的booking-tabs升级为专业的TDesign选项卡
 */
.top-tabs-section {
  /*
   * 布局和定位 - 与schedule页面保持完全一致
   */
  position: relative;

  /*
   * 背景设计 - 精致的渐变背景
   */
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);

  /*
   * 边框设计 - 精致的边框效果
   */
  border: 1px solid #f0f0f0;
  border-radius: 8px;

  /*
   * 阴影效果 - 轻微的阴影增加层次感
   */
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.02),
    0 0 0 1px rgba(0, 0, 0, 0.01);

  /*
   * 内边距 - 适当的内边距
   */
  padding: 4px 8px;

  /*
   * 外边距 - 与其他元素保持间距
   */
  margin: 0 0 8px 0;

  /*
   * 过渡动画 - 平滑的过渡效果
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 字体设置 - 统一的字体族
   */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/*
 * 自定义线条选项卡样式 - 从schedule页面完整移植
 *
 * 功能：覆盖TDesign组件的默认样式
 * 目的：实现优雅的线条选项卡设计
 * 与schedule页面保持完全一致的视觉效果
 */
.custom-top-tabs {
  /*
   * 背景 - 完全透明，让容器背景显示
   */
  background-color: transparent;
  border: none;

  /*
   * 移除圆角 - 线条选项卡不需要圆角
   */
  border-radius: 0;

  /*
   * 溢出控制
   */
  overflow: visible;

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 16px;

  /*
   * 布局控制
   */
  margin: 0;
  width: 100%;
  height: auto;

  /*
   * 最小高度设置为最小值以确保紧凑布局
   */
  min-height: 1px;
}

/**
 * TDesign线条选项卡导航区域样式 - 从schedule页面移植
 */
.custom-top-tabs .t-tabs__nav {
  /*
   * 内边距 - 优化的内边距
   */
  padding: 0;
  height: auto;
  min-height: 1px;

  /*
   * 背景和边框 - 透明设计
   */
  background: transparent;
  border: none;

  /*
   * 字体设置
   */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * 线条选项卡项目样式 - 从schedule页面移植
 */
.custom-top-tabs .t-tabs__item {
  /*
   * 字体设置 - 优化可读性
   */
  font-size: 16px !important;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * 颜色设置 - 默认状态
   */
  color: #666666 !important;

  /*
   * 内边距 - 舒适的点击区域
   */
  padding: 12px 16px !important;

  /*
   * 布局设置
   */
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 44px;

  /*
   * 背景和边框 - 透明设计
   */
  background: transparent;
  border: none;
  border-radius: 0;

  /*
   * 过渡动画 - 平滑的状态切换
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 底部边框 - 默认状态下的细线
   */
  border-bottom: 3px solid transparent;
}

/*
 * 选项卡项目的渐变装饰效果 - 从schedule页面完整移植
 * 这是关键的渐变特效实现
 */
.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  height: 2px;
  /* 关键渐变特效：从透明到蓝色再到透明的水平渐变 */
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/**
 * 激活状态的线条选项卡 - 从schedule页面完整移植包含渐变特效
 */
.custom-top-tabs .t-tabs__item--active {
  /*
   * 文字颜色和字重
   */
  color: #0052d9 !important;
  font-weight: 600 !important;

  /*
   * 底部边框 - 激活状态的蓝色指示线，更粗更明显
   */
  border-bottom: 3px solid #0052d9 !important;

  /*
   * 背景效果 - 保持透明背景，让页面渐变背景显示
   */
  background: transparent !important;

  /*
   * 文字阴影特效 - 增加层次感和渐变效果
   * 这是从schedule页面移植的关键渐变特效
   */
  text-shadow: 0 0 1px rgba(0, 82, 217, 0.1);

  /*
   * 过渡动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/*
 * 激活状态的顶部装饰线 - 从schedule页面移植
 */
.custom-top-tabs .t-tabs__item--active::before {
  width: 60%; /* 激活时显示顶部装饰线 */
}

/**
 * 非激活状态的选项卡悬停效果 - 从schedule页面移植
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover {
  /*
   * 悬停时的文字颜色
   */
  color: #333333 !important;

  /*
   * 悬停时的背景效果
   */
  background: rgba(0, 0, 0, 0.02);

  /*
   * 过渡动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/*
 * 悬停时的顶部装饰线 - 从schedule页面移植
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover::before {
  width: 30%; /* 悬停时显示较短的顶部装饰线 */
}

/**
 * 底部指示线容器 - 从schedule页面移植
 */
.custom-top-tabs .t-tabs__track {
  display: none; /* 隐藏默认的滑动指示器，使用border-bottom代替 */
}

.course-list {
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  /* 使用flex: 1让课程列表容器占据剩余的所有空间 */
  flex: 1;
  display: flex;
  flex-direction: column;
  /* 关键优化：由于容器高度已经减去了导航栏高度，这里只需要考虑安全区域
     大幅减少底部内边距，让课程列表更接近屏幕底部边缘 */
  padding-bottom: calc(env(safe-area-inset-bottom) + 4px);
  /* 确保容器可以延伸到计算后的底部边缘，最大化垂直空间利用 */
  min-height: 0;
  /* 允许内容滚动，当课程列表超出容器高度时可以滚动查看 */
  overflow-y: auto;
  /* 优化滚动性能，提供更流畅的滚动体验 */
  -webkit-overflow-scrolling: touch;
}
.course-card {
  background-color: #ffffff;
  border-radius: 12px;
  /* 优化内边距：在保持可读性的前提下，减少内边距以节省空间 */
  padding: 12px;
  /* 进一步减少卡片间距：让更多课程卡片在屏幕上可见，提升信息密度 */
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}
.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.course-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}
.course-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;
  white-space: nowrap;
}
.course-status.available {
  background-color: #e8f5e8;
  color: #52c41a;
}
.course-status.booked {
  background-color: #e6f3ff;
  color: #0052d9;
}
.course-status.full {
  background-color: #fff2e8;
  color: #fa8c16;
}
.course-status.ended {
  background-color: #f0f0f0;
  color: #888;
}
.course-info-list {
  margin-bottom: 12px;
}
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}
.info-item:last-child {
  margin-bottom: 0;
}
.info-item t-icon {
  margin-right: 8px;
  color: #0052d9;
}

/* 活动详情单行显示并截断 */
.activity-detail {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
.course-footer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.tag-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 已预约学员区域样式 */
.booked-students-section {
  margin: 12px 0;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.collapse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f8f8;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.collapse-content {
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.student-list {
  padding: 8px 0;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  font-size: 14px;
  color: #666;
}

.student-item t-icon {
  margin-right: 8px;
  color: #0052d9;
}

.student-name {
  flex: 1;
}

.no-students {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 20px 0;
}
/* 底部导航栏占位符 - 确保与课程列表的底部内边距计算保持一致 */
#tab-bar-placeholder {
  /* 保持120rpx高度，与课程列表底部内边距计算中的导航栏高度一致 */
  height: 120rpx;
  /* 添加背景色以便调试时可见（生产环境可移除） */
  /* background-color: rgba(255, 0, 0, 0.1); */
}
.timeline-date {
  color: #888;
  font-size: 26rpx;
  text-align: left;
  margin: 32rpx 0 12rpx 16rpx;
  font-weight: bold;
}
.end-indicator {
  text-align: center;
  color: #bbb;
  font-size: 24rpx;
  margin: 24rpx 0 12rpx 0;
}

.loading-indicator {
  text-align: center;
  color: #0052d9;
  font-size: 24rpx;
  margin: 24rpx 0 12rpx 0;
  padding: 20rpx 0;
}

/*
 * 响应式布局调整 - 从schedule页面移植
 * 针对小屏幕进行优化，确保标签栏在不同尺寸下都能正确显示
 * 特别是激活指示器不会被裁切的问题
 */

/* 针对中等屏幕设备的优化（如iPhone 12 mini） */
@media (max-width: 390px) {
  .custom-top-tabs .t-tabs__item {
    padding: 12px 14px !important; /* 减小水平内边距 */
    font-size: 15px !important; /* 缩小字体 */
    min-height: 42px; /* 减小最小高度 */
  }

  .top-tabs-section {
    padding: 3px 6px; /* 减小容器内边距 */
  }
}

/* 针对小屏幕设备的进一步优化（如iPhone SE） */
@media (max-width: 375px) {
  .custom-top-tabs .t-tabs__item {
    padding: 12px 12px !important; /* 进一步减小内边距 */
    font-size: 15px !important;
    min-height: 40px; /* 进一步减小最小高度 */
  }

  .top-tabs-section {
    padding: 2px 4px; /* 进一步减小容器内边距 */
    margin: 0 0 6px 0; /* 减小外边距 */
  }

  /* 确保激活指示器在小屏幕上可见 */
  .view-section {
    margin-bottom: 8px; /* 增加底部外边距，为指示器预留空间 */
  }
}