// app.js
// 这是微信小程序的全局应用文件，相当于传统Web项目的main.js或index.js
// App()函数是小程序的核心函数，用于注册小程序应用实例
// 类似于Vue的new Vue()或React的ReactDOM.render()，但它管理的是整个小程序应用

/**
 * App()函数注册小程序应用
 * 参数是一个对象，包含应用的生命周期函数、全局数据等
 * 这个对象的结构类似于Vue的选项对象或React的组件类
 */
App({
  /**
   * globalData: 全局数据对象，相当于Vuex的state或Redux的store
   * 在小程序的任何页面都可以通过getApp().globalData访问这些数据
   * 类似于Web开发中的全局变量，但更规范和安全
   */
  globalData: {
    userInfo: null,        // 用户信息对象，初始值为null（空值）
    isLoggedIn: false,     // 登录状态，布尔值：true表示已登录，false表示未登录
    env: "cloud1-1gm190n779af8083"  // 云开发环境ID，类似于数据库连接字符串
  },

  /**
   * onLaunch: 应用启动生命周期函数，小程序启动时自动调用
   * 类似于Vue的created()或React的componentDidMount()
   * 只会在小程序启动时调用一次，用于初始化操作
   * 参数options包含启动小程序的场景值、路径等信息
   */
  onLaunch: function () {
    // 初始化云开发环境
    // wx.cloud是微信小程序的云开发API，类似于Web的axios或fetch用于后端交互
    if (!wx.cloud) {
      // console.error()用于在控制台输出错误信息，类似于Web的console.log()
      console.error("请使用 2.2.3 或以上的基础库以使用云能力");
    } else {
      // wx.cloud.init()初始化云开发，类似于Web项目中配置API基础URL
      wx.cloud.init({
        env: this.globalData.env,  // 云环境ID，this指向当前App实例
        traceUser: true,           // 是否在将用户访问记录到用户管理中，布尔值
      });
    }

    // 调用检查登录状态的方法
    // this.checkLoginStatus()调用下面定义的方法，this指向当前App实例
    this.checkLoginStatus();
  },

  /**
   * checkLoginStatus: 自定义方法，检查用户登录状态
   * 类似于Web开发中检查localStorage中的token或用户信息
   * 在小程序中使用wx.getStorageSync()读取本地存储数据
   */
  checkLoginStatus() {
    // wx.getStorageSync()同步获取本地存储数据，类似于Web的localStorage.getItem()
    // 'userInfo'是存储的键名，类似于localStorage.getItem('userInfo')
    const userInfo = wx.getStorageSync('userInfo');

    // if语句进行条件判断，如果userInfo存在（不为null、undefined、空字符串等）
    if (userInfo) {
      // 将获取到的用户信息赋值给全局数据
      this.globalData.userInfo = userInfo;
      // 设置登录状态为true
      this.globalData.isLoggedIn = true;
    } else {
      // 如果没有用户信息，设置为初始状态
      this.globalData.userInfo = null;
      this.globalData.isLoggedIn = false;
    }
  },

  /**
   * wxLogin: 微信一键登录方法 - 支持获取真实用户信息
   * async/await: ES2017异步编程语法，类似于Promise的then/catch，但写法更简洁
   * async函数返回Promise对象，await等待异步操作完成
   * 这种写法比传统的回调函数或Promise链式调用更易读
   */
  async wxLogin() {
    // try-catch: 异常处理语句，类似于Java的try-catch
    // try块中放可能出错的代码，catch块处理错误
    try {
      // wx.showLoading(): 显示加载提示框，类似于Web的loading动画
      wx.showLoading({
        title: '登录中...',    // 提示文字
        mask: true            // 是否显示透明蒙层，防止用户点击其他区域
      });

      // await wx.login(): 获取微信登录凭证，类似于Web的OAuth授权
      // await关键字等待异步操作完成，然后将结果赋值给loginResult
      const loginResult = await wx.login();

      // 检查是否成功获取到登录凭证code
      // !loginResult.code 表示如果code不存在（null、undefined、空字符串等）
      if (!loginResult.code) {
        // throw new Error(): 抛出自定义错误，会被catch块捕获
        throw new Error('获取登录凭证失败');
      }

      // 创建用户信息对象，这是一个JavaScript对象字面量
      // 注意：由于微信政策变化，现在需要用户主动授权才能获取真实信息
      const userInfo = {
        nickName: '微信用户',    // 用户昵称，字符串类型
        avatarUrl: '',          // 头像URL，空字符串表示使用默认头像
        gender: 0,              // 性别：0未知，1男，2女，数字类型
        country: '',            // 国家，字符串类型
        province: '',           // 省份，字符串类型
        city: '',               // 城市，字符串类型
        language: 'zh_CN'       // 语言，zh_CN表示简体中文
      };

      // 调用云函数进行登录验证，类似于Web的API调用
      // wx.cloud.callFunction(): 调用云函数，类似于axios.post()或fetch()
      const cloudResult = await wx.cloud.callFunction({
        name: 'userManagement',   // 云函数名称，类似于API接口路径
        data: {                   // 传递给云函数的数据，类似于POST请求的body
          action: 'login',        // 操作类型
          data: {
            userInfo: userInfo    // 用户信息对象
          }
        }
      });

      // 检查云函数调用结果
      if (cloudResult.result.success) {
        // 使用展开运算符(...)合并对象，ES6语法
        // ...userInfo 将userInfo对象的所有属性展开
        // 后面的属性会覆盖前面的同名属性
        const finalUserInfo = {
          ...userInfo,                                    // 展开基础用户信息
          openid: cloudResult.result.openid,             // 微信用户唯一标识
          role: cloudResult.result.role || '学员',        // 用户角色，||表示如果前者为空则使用后者
          ...cloudResult.result.userInfo                 // 展开云函数返回的用户信息
        };

        // wx.setStorageSync(): 同步设置本地存储，类似于localStorage.setItem()
        wx.setStorageSync('userInfo', finalUserInfo);
        // 更新全局数据
        this.globalData.userInfo = finalUserInfo;
        this.globalData.isLoggedIn = true;

        // wx.hideLoading(): 隐藏加载提示框
        wx.hideLoading();
        // wx.showToast(): 显示消息提示框，类似于Web的alert()但更美观
        wx.showToast({
          title: '登录成功',      // 提示文字
          icon: 'success'        // 图标类型：success成功，error错误，loading加载中
        });

        // return: 返回用户信息，调用此方法的地方可以获取到这个返回值
        return finalUserInfo;
      } else {
        // 如果云函数返回失败，抛出错误
        throw new Error(cloudResult.result.message || '登录失败');
      }
    } catch (error) {
      // catch块：捕获并处理try块中抛出的错误
      wx.hideLoading();                    // 隐藏加载框
      console.error('登录失败:', error);    // 在控制台输出错误信息，便于调试
      wx.showToast({
        title: error.message || '登录失败',  // 显示错误信息
        icon: 'error'                      // 错误图标
      });
      throw error;                         // 重新抛出错误，让调用方知道登录失败
    }
  },

  /**
   * logout: 退出登录方法
   * 清除本地存储的用户信息和全局状态
   * 类似于Web开发中清除localStorage和重置用户状态
   */
  logout() {
    // wx.removeStorageSync(): 同步删除本地存储数据
    // 类似于localStorage.removeItem('userInfo')
    wx.removeStorageSync('userInfo');

    // 重置全局数据为初始状态
    this.globalData.userInfo = null;      // 清空用户信息
    this.globalData.isLoggedIn = false;   // 设置为未登录状态
  },

  /**
   * getUserInfo: 获取用户信息的方法
   * 这是一个getter方法，类似于Java的getUserInfo()或C#的GetUserInfo()
   * 返回当前登录用户的信息对象
   * @returns {Object|null} 用户信息对象，如果未登录则返回null
   */
  getUserInfo() {
    // return语句返回全局数据中的用户信息
    // 调用方可以通过 getApp().getUserInfo() 获取用户信息
    return this.globalData.userInfo;
  },

  /**
   * isLoggedIn: 检查是否已登录的方法
   * 这是一个布尔值判断方法，类似于Java的isLoggedIn()或C#的IsLoggedIn()
   * @returns {boolean} true表示已登录，false表示未登录
   */
  isLoggedIn() {
    // 返回登录状态的布尔值
    return this.globalData.isLoggedIn;
  }
});

/**
 * 总结：app.js文件的作用和重要性
 *
 * 1. 应用入口：类似于Web项目的main.js，是整个小程序的入口文件
 * 2. 全局管理：管理全局数据、全局方法，类似于Vuex或Redux的作用
 * 3. 生命周期：处理应用级别的生命周期事件（启动、显示、隐藏等）
 * 4. 初始化：进行云开发初始化、用户状态检查等初始化操作
 * 5. 工具方法：提供全局可用的工具方法，如登录、退出等
 *
 * 与您熟悉的C#和Java对比：
 * - App()类似于C#的Application类或Java的Application类
 * - globalData类似于C#的静态属性或Java的静态字段
 * - 生命周期方法类似于C#的Application_Start或Java的@PostConstruct
 * - 全局方法类似于C#的静态方法或Java的静态方法
 *
 * 与Web开发对比：
 * - 类似于Vue的main.js + Vuex store
 * - 类似于React的index.js + Redux store
 * - 全局数据管理类似于localStorage + 状态管理
 */
