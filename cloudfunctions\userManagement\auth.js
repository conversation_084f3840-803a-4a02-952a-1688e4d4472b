// auth.js
// 权限验证工具函数

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 验证用户是否为管理员
 * @param {string} openid 用户openid
 * @returns {Promise<Object>} 验证结果
 */
async function verifyAdmin(openid) {
  try {
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    const isAdmin = user.roles && user.roles.includes('管理员');
    
    if (!isAdmin) {
      return {
        success: false,
        message: '权限不足，只有管理员可以执行此操作'
      };
    }
    
    return {
      success: true,
      user: user
    };
  } catch (error) {
    console.error('权限验证失败:', error);
    return {
      success: false,
      message: '权限验证失败',
      error: error.message
    };
  }
}

/**
 * 验证用户是否为讲师
 * @param {string} openid 用户openid
 * @returns {Promise<Object>} 验证结果
 */
async function verifyCoach(openid) {
  try {
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    const isCoach = user.roles && user.roles.includes('讲师');
    
    if (!isCoach) {
      return {
        success: false,
        message: '权限不足，只有讲师可以执行此操作'
      };
    }
    
    return {
      success: true,
      user: user
    };
  } catch (error) {
    console.error('权限验证失败:', error);
    return {
      success: false,
      message: '权限验证失败',
      error: error.message
    };
  }
}

/**
 * 验证用户是否具有指定角色
 * @param {string} openid 用户openid
 * @param {string|Array} roles 需要的角色
 * @returns {Promise<Object>} 验证结果
 */
async function verifyRoles(openid, roles) {
  try {
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    const userRoles = user.roles || [];
    
    // 如果roles是字符串，转换为数组
    const requiredRoles = Array.isArray(roles) ? roles : [roles];
    
    // 检查用户是否具有任一所需角色
    const hasRole = requiredRoles.some(role => userRoles.includes(role));
    
    if (!hasRole) {
      return {
        success: false,
        message: `权限不足，需要以下角色之一: ${requiredRoles.join(', ')}`
      };
    }
    
    return {
      success: true,
      user: user
    };
  } catch (error) {
    console.error('权限验证失败:', error);
    return {
      success: false,
      message: '权限验证失败',
      error: error.message
    };
  }
}

module.exports = {
  verifyAdmin,
  verifyCoach,
  verifyRoles
}; 