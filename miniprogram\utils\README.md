# 统一预约逻辑使用说明

## 概述

`bookingUtils.js` 提供了统一的预约和取消预约逻辑，用于替代各个页面中重复的预约相关代码。

## 主要功能

### 1. 预约课程 (`bookCourse`)

```javascript
import { bookCourse } from '../../utils/bookingUtils.js';

// 使用示例
await bookCourse(course, this,
  // 成功回调
  (course) => {
    // 更新页面状态
    this.setData({
      isBooked: true,
      courseStatus: '已预约'
    });
  },
  // 错误回调
  (error) => {
    console.error('预约失败:', error);
  }
);
```

#### 预约限制规则

1. **用户必须已登录**：未登录用户无法预约
2. **课程状态检查**：
   - 课程未结束
   - 课程未开始
   - 课程有剩余名额
3. **讲师限制**：讲师不能预约自己的课程
4. **会员卡检查**：用户必须有有效的会员卡且剩余次数大于0
5. **重复预约检查**：同一用户不能重复预约同一课程

### 2. 取消预约 (`cancelBooking`)

```javascript
import { cancelBooking } from '../../utils/bookingUtils.js';

// 使用示例
await cancelBooking(course, this,
  // 成功回调
  (course) => {
    // 更新页面状态
    this.setData({
      isBooked: false,
      courseStatus: '可预约'
    });
  },
  // 错误回调
  (error) => {
    console.error('取消失败:', error);
  }
);
```

### 3. 加载系统设置 (`loadSystemSettings`)

```javascript
import { loadSystemSettings } from '../../utils/bookingUtils.js';

// 使用示例
async loadSystemSettings() {
  try {
    const cancelTimeLimit = await loadSystemSettings(this);
    console.log('取消时间限制:', cancelTimeLimit);
  } catch (error) {
    console.error('加载系统设置失败:', error);
  }
}
```

## 统一逻辑包含的功能

### 预约流程
1. **登录检查** - 自动检查用户登录状态
2. **课程可用性检查** - 检查课程是否可预约（未结束、未开始、未满员）
3. **时间限制检查** - 检查是否在取消时间限制内
4. **考勤卡检查** - 检查用户是否有有效的考勤卡
5. **预约确认** - 显示预约确认对话框
6. **执行预约** - 调用云函数进行预约
7. **结果处理** - 根据结果执行成功或失败回调

### 取消预约流程
1. **登录检查** - 自动检查用户登录状态
2. **课程状态检查** - 检查课程是否已结束或已开始
3. **时间限制检查** - 检查是否在取消时间限制内
4. **取消确认** - 显示取消确认对话框
5. **执行取消** - 调用云函数进行取消
6. **结果处理** - 根据结果执行成功或失败回调

## 已集成的页面

1. **课程详情页面** (`course-detail`)
   - 使用统一的预约和取消预约逻辑
   - 支持成功回调更新页面状态

2. **课程表页面** (`schedule`)
   - 使用统一的预约和取消预约逻辑
   - 支持本地数据更新和后台刷新

3. **我的预约页面** (`my-bookings`)
   - 使用统一的取消预约逻辑
   - 支持预约数据重新加载

## 注意事项

1. **课程对象格式** - 确保传入的课程对象包含必要的字段：
   - `id` 或 `_id`: 课程ID
   - `name`: 课程名称
   - `startTime`: 开始时间
   - `available`: 是否可预约
   - `ended`: 是否已结束
   - `started`: 是否已开始

2. **页面对话框支持** - 如果页面有自定义对话框（`showDialog`），会优先使用自定义对话框，否则使用系统默认对话框

3. **错误处理** - 所有错误都会通过回调函数返回，页面可以根据需要处理

4. **系统设置** - 自动加载和使用系统设置中的取消时间限制

## 优势

1. **代码复用** - 避免重复的预约逻辑代码
2. **统一体验** - 所有页面的预约流程保持一致
3. **易于维护** - 修改预约逻辑只需要在一个地方进行
4. **错误处理** - 统一的错误处理和用户提示
5. **扩展性** - 可以轻松添加新的检查逻辑或功能 