// config.js
// 配置管理工具类 - 支持数据库驱动的配置系统

import { getSystemSettings, updateSystemSettings } from './database.js';
import { SYSTEM_SETTINGS_DEFAULTS } from './constants.js';

/**
 * 配置管理器类
 * 负责从数据库加载配置，并提供缓存和默认值支持
 */
class ConfigManager {
  constructor() {
    this.config = null;
    this.loading = false;
    this.listeners = [];
  }

  /**
   * 初始化配置管理器
   */
  async init() {
    if (this.config === null && !this.loading) {
      await this.loadConfig();
    }
  }

  /**
   * 从数据库加载配置
   */
  async loadConfig() {
    if (this.loading) return;
    
    this.loading = true;
    try {
      const dbConfig = await getSystemSettings();
      this.config = { ...SYSTEM_SETTINGS_DEFAULTS, ...dbConfig };
      this.notifyListeners();
    } catch (error) {
      console.error('加载配置失败:', error);
      // 使用默认配置作为后备
      this.config = { ...SYSTEM_SETTINGS_DEFAULTS };
    } finally {
      this.loading = false;
    }
  }

  /**
   * 获取配置值
   * @param {string} key 配置键
   * @param {any} defaultValue 默认值
   * @returns {any} 配置值
   */
  get(key, defaultValue = null) {
    if (this.config === null) {
      // 如果配置未加载，返回默认值
      return defaultValue !== null ? defaultValue : SYSTEM_SETTINGS_DEFAULTS[key];
    }
    return this.config[key] !== undefined ? this.config[key] : defaultValue;
  }

  /**
   * 设置配置值（仅内存，不持久化）
   * @param {string} key 配置键
   * @param {any} value 配置值
   */
  set(key, value) {
    if (this.config === null) {
      this.config = { ...SYSTEM_SETTINGS_DEFAULTS };
    }
    this.config[key] = value;
    this.notifyListeners();
  }

  /**
   * 更新配置并持久化到数据库
   * @param {Object} updates 要更新的配置项
   * @returns {Promise<boolean>} 更新是否成功
   */
  async update(updates) {
    try {
      const success = await updateSystemSettings(updates);
      if (success) {
        // 更新本地配置
        this.config = { ...this.config, ...updates };
        this.notifyListeners();
      }
      return success;
    } catch (error) {
      console.error('更新配置失败:', error);
      return false;
    }
  }

  /**
   * 刷新配置（重新从数据库加载）
   */
  async refresh() {
    this.config = null;
    await this.loadConfig();
  }

  /**
   * 获取所有配置
   * @returns {Object} 所有配置项
   */
  getAll() {
    return this.config ? { ...this.config } : { ...SYSTEM_SETTINGS_DEFAULTS };
  }

  /**
   * 添加配置变更监听器
   * @param {Function} listener 监听器函数
   */
  addListener(listener) {
    this.listeners.push(listener);
  }

  /**
   * 移除配置变更监听器
   * @param {Function} listener 监听器函数
   */
  removeListener(listener) {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器配置已变更
   */
  notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.config);
      } catch (error) {
        console.error('配置监听器执行失败:', error);
      }
    });
  }

  /**
   * 检查配置是否已加载
   * @returns {boolean} 是否已加载
   */
  isLoaded() {
    return this.config !== null;
  }

  /**
   * 获取配置加载状态
   * @returns {boolean} 是否正在加载
   */
  isLoading() {
    return this.loading;
  }
}

// 创建全局配置管理器实例
const configManager = new ConfigManager();

// 导出配置管理器实例和便捷方法
export default configManager;

/**
 * 便捷方法：获取配置值
 * @param {string} key 配置键
 * @param {any} defaultValue 默认值
 * @returns {any} 配置值
 */
export const getConfig = (key, defaultValue = null) => {
  return configManager.get(key, defaultValue);
};

/**
 * 便捷方法：设置配置值
 * @param {string} key 配置键
 * @param {any} value 配置值
 */
export const setConfig = (key, value) => {
  configManager.set(key, value);
};

/**
 * 便捷方法：更新配置
 * @param {Object} updates 要更新的配置项
 * @returns {Promise<boolean>} 更新是否成功
 */
export const updateConfig = async (updates) => {
  return await configManager.update(updates);
};

/**
 * 便捷方法：初始化配置
 */
export const initConfig = async () => {
  await configManager.init();
};

/**
 * 便捷方法：刷新配置
 */
export const refreshConfig = async () => {
  await configManager.refresh();
};