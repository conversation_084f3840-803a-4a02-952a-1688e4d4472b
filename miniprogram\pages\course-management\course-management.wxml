<!--
  course-management.wxml - 课程管理页面结构文件

  页面功能概述：
  这是健身房管理系统的课程管理页面，提供完整的课程生命周期管理功能

  主要功能模块：
  1. 双tab设计：课程管理 + 模板管理
  2. 多维度筛选：按状态、时间、关键词筛选课程
  3. 时间轴展示：类似微信朋友圈的时间线设计
  4. 实时搜索：支持按名称、教练、场地搜索
  5. 批量操作：上线/下线、删除、编辑等操作

  技术特点：
  - 使用TDesign组件库，保证UI一致性和美观度
  - 采用条件渲染，根据不同状态显示不同内容
  - 实现复杂的滚动加载和下拉刷新机制
  - 支持动画效果，提升用户体验

  与您熟悉的技术对比：
  - 页面结构类似于ASP.NET的Master Page + Content Page
  - 条件渲染类似于Razor的@if语法
  - 数据绑定类似于WPF的Binding机制
  - 组件化设计类似于WinForms的UserControl
-->

<!--
  根容器：页面的最外层容器
  view: WXML的基础容器组件，相当于HTML的div元素

  CSS类名说明：
  container: 定义页面的基础布局、内边距、背景色等样式
-->
<view class="container">
  <!--
    顶部区域：包含主选项卡和操作按钮

    布局设计：
    采用水平布局，左侧为选项卡，右侧为操作按钮
    类似于传统桌面应用的工具栏设计
  -->
  <view class="top-section">
    <!--
      顶部选项卡区域

      功能：在"活动维护"和"模板维护"之间切换
      设计理念：将相关但不同的功能模块分开管理
    -->
    <view class="top-tabs-section">
      <!--
        TDesign选项卡组件
        t-tabs: TDesign的选项卡容器组件

        属性详解：
        1. value="{{topActiveTab}}": 当前选中的tab值
           - 数据绑定语法：{{变量名}}
           - 双向绑定：页面数据变化会更新UI，用户操作会更新数据

        2. bindchange="onTopTabsChange": 选项卡切换事件
           - bind前缀：事件绑定语法
           - change事件：当用户点击不同tab时触发
           - 事件处理函数：onTopTabsChange

        3. theme="card": 选项卡主题样式
           - card: 卡片式样式，有背景和边框
           - line: 线条式样式，只有底部线条
           - tag: 标签式样式，类似于标签页

        4. show-bottom-line="{{false}}": 是否显示底部线条
           - false: 不显示底部线条，因为使用card主题已有边框

        5. t-class="custom-top-tabs": 自定义CSS类名
           - TDesign组件的自定义样式机制
           - 可以覆盖组件默认样式

        与其他技术对比：
        - HTML: <div class="tabs"> + JavaScript控制
        - Vue: <el-tabs v-model="activeTab" @tab-click="handleClick">
        - React: <Tabs activeKey={activeTab} onChange={handleChange}>
        - WPF: <TabControl SelectedIndex="{Binding ActiveTabIndex}">
      -->
      <t-tabs value="{{topActiveTab}}" bindchange="onTopTabsChange" theme="line" show-bottom-line="{{true}}" t-class="custom-top-tabs">
        <!--
          选项卡面板：定义具体的tab项
          t-tab-panel: TDesign的选项卡面板组件

          属性说明：
          - value: tab的唯一标识符，与topActiveTab的值对应
          - label: tab显示的文本标签

          业务含义：
          - course: 活动维护，管理具体的健身课程
          - template: 模板维护，管理课程模板，便于快速创建课程
        -->
        <t-tab-panel value="course" label="活动维护" />
        <t-tab-panel value="template" label="模板维护" />
      </t-tabs>
    </view>

    <!-- 移除顶部操作按钮区域，将按钮移到搜索行 -->
  </view>

  <!--
    活动筛选区域 - 固定在顶部，不参与滚动

    功能模块：
    1. 状态筛选：按课程状态筛选（全部、已上线、未上线、历史）
    2. 关键词搜索：按名称、教练、场地搜索

    用户体验：
    提供多维度的数据筛选方式，帮助管理员快速找到目标课程

    布局位置：与top-section同级，固定在页面顶部
  -->
  <view wx:if="{{topActiveTab === 'course'}}" class="filter-section">
      <!--
        搜索和筛选区域

        功能：搜索功能 + 筛选下拉选择器 + 操作按钮
        设计：搜索功能放在最左边，筛选下拉在搜索展开时隐藏
        布局：
        - 收起状态：[🔍] [筛选下拉] [添加活动] [批量操作]
        - 展开状态：[🔍 ____________搜索框____________] [×]
      -->
      <view class="search-actions-section {{searchExpanded ? 'expanded' : 'collapsed'}}">
        <!--
          收起状态：搜索图标 + 筛选下拉 + 操作按钮
          布局：[🔍] [筛选下拉] [添加活动] [批量操作]

          临时修改：移除搜索展开条件，便于测试下拉菜单
        -->
        <view class="collapsed-layout">
          <!--
            搜索图标 - 放在最左边
            功能：点击展开搜索输入框
          -->
          <view class="search-icon-only" bindtap="onExpandSearch">
            <t-icon name="search" size="20" class="search-toggle-icon" />
          </view>

          <!--
            筛选下拉选择器 - 参考album-management的菜单弹出方式

            使用固定定位的遮罩层 + 下拉菜单，避免被容器截断：
            - 点击按钮显示/隐藏下拉菜单
            - 使用position: fixed避免被父容器overflow限制
            - 点击外部区域自动关闭
            - 平滑的动画效果
          -->
          <view class="filter-dropdown-trigger" bindtap="onShowFilterMenu">
            <view class="filter-button">
              <!--
                当前选中项的文本
                显示当前选中的筛选项标签
              -->
              <text class="filter-text">{{currentFilterLabel}}</text>
              <!--
                下拉箭头图标
                使用TDesign图标库的chevron-down图标
              -->
              <t-icon name="chevron-down" size="16" class="filter-arrow {{filterMenuVisible ? 'rotated' : ''}}" />
            </view>
          </view>

          <!--
            操作按钮区域
            包含：添加活动按钮、批量操作按钮
          -->
          <view class="actions-container">
            <!--
              添加活动按钮
              条件显示：只在课程管理tab显示
            -->
            <t-button wx:if="{{topActiveTab === 'course'}}" theme="primary" size="small" bindtap="onAddCourse">
              添加活动
            </t-button>

            <!--
              批量操作按钮
              动态主题：批量模式时显示warning主题，否则显示default主题
              动态文本：根据当前是否在批量模式显示不同文本
            -->
            <t-button wx:if="{{topActiveTab === 'course'}}" theme="{{batchMode ? 'warning' : 'default'}}" size="small" bindtap="onToggleBatchMode">
              {{batchMode ? '退出批量' : '批量操作'}}
            </t-button>
          </view>
        </view>

        <!--
          展开状态：搜索框占据全部宽度，筛选下拉隐藏
          布局：[🔍 ____________搜索框____________] [×]

          临时修改：改为条件渲染，配合上面的修改
        -->
        <view wx:if="{{searchExpanded}}" class="expanded-layout">
          <view class="search-input-container">
            <!--
              搜索图标
              位置：搜索输入框左侧
            -->
            <t-icon name="search" size="16" class="search-icon" />

            <!--
              搜索输入框
              功能：输入搜索关键词，支持实时搜索
              事件：
              - bindinput: 输入变化事件，实时过滤数据
              - bindconfirm: 确认搜索事件（点击键盘搜索按钮）
              - bindblur: 失焦事件，无内容时自动收起
              - focus: 自动聚焦属性，展开时自动获得焦点
            -->
            <input
              class="search-input"
              placeholder="搜索活动名称/讲师/场地"
              value="{{searchValue}}"
              bindinput="onSearchInput"
              bindconfirm="onSearchSubmit"
              bindblur="onSearchBlur"
              focus="{{searchExpanded}}"
            />

            <!--
              清空搜索图标
              条件显示：只有当有搜索内容时才显示
              功能：清空搜索关键词
            -->
            <t-icon
              wx:if="{{searchValue}}"
              name="close-circle-filled"
              size="16"
              class="clear-icon"
              bindtap="onSearchClear"
            />

            <!--
              收起搜索图标
              功能：收起搜索框，返回收起状态
              位置：搜索输入框右侧
            -->
            <t-icon
              name="chevron-left"
              size="16"
              class="collapse-icon"
              bindtap="onCollapseSearch"
            />
          </view>
        </view>


      </view>
    </view>

  <!--
    活动维护内容区域 - 可滚动区域

    条件渲染：只有当topActiveTab为'course'时才显示
    这是页面的主要内容区域，包含批量操作栏和课程列表
    布局：在筛选区域下方，可以滚动
  -->
  <view wx:if="{{topActiveTab === 'course'}}" class="course-content">

    <!--
      批量操作栏 - 两行布局

      功能：在批量模式下显示批量操作按钮
      条件显示：只在批量模式且有选中项时显示

      布局结构：
      第一行：显示选择数量信息
      第二行：显示批量操作按钮
    -->
    <view wx:if="{{batchMode && selectedCourseIds.length > 0}}" class="batch-actions-bar">
      <!--
        单行布局：左侧显示选择信息，右侧显示操作按钮

        功能：在一行内显示批量操作的所有元素
        布局：[已选择 X 个活动] [批量上线] [批量下线] [批量删除]
      -->

      <!-- 左侧：批量信息文本 -->
      <view class="batch-info">
        <text>已选择 {{selectedCourseIds.length}} 个活动</text>
      </view>

      <!-- 右侧：批量操作按钮组 -->
      <view class="batch-buttons">
        <!-- 非历史活动状态下显示上线和下线按钮 -->
        <t-button wx:if="{{courseActiveTab !== 'history'}}" theme="success" size="small" bindtap="onBatchOnline">
          批量上线
        </t-button>
        <t-button wx:if="{{courseActiveTab !== 'history'}}" theme="warning" size="small" bindtap="onBatchOffline">
          批量下线
        </t-button>
        <!-- 删除按钮在所有状态下都显示 -->
        <t-button theme="danger" size="small" bindtap="onBatchDelete">
          批量删除
        </t-button>
      </view>
    </view>

    <!--
      活动列表区域

      功能：显示课程数据的主要区域
      支持多种状态：加载中、空状态、正常列表
    -->
    <view class="course-list">
      <!--
        初始加载状态

        条件渲染：wx:if="{{courseInitialLoading}}"
        - 只在页面首次加载时显示
        - 提供友好的加载提示
        - 避免空白页面造成的用户困惑

        设计元素：
        - 三个跳动的圆点：视觉上表示正在加载
        - 文字提示：明确告知用户当前状态
      -->
      <view wx:if="{{courseInitialLoading}}" class="loading-indicator">
        <!--
          加载动画：三个跳动的圆点

          CSS动画实现：
          每个圆点有不同的动画延迟
          形成波浪式的跳动效果

          用户体验：
          比静态文字更有趣，减少等待的焦虑感
        -->
        <view class="loading-dots">
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
        </view>
        <text>正在加载活动列表...</text>
      </view>

      <!--
        空状态显示

        条件渲染：wx:elif="{{!courseLoading && visibleCourseList.length === 0}}"
        触发条件：
        1. 不在加载状态：!courseLoading
        2. 没有可显示的数据：visibleCourseList.length === 0

        t-empty组件：TDesign的空状态组件
        - 提供统一的空状态视觉设计
        - 支持自定义描述文字
        - 比自定义空状态更专业和一致

        动态描述文字：
        - 有搜索内容时：'未找到匹配的活动'
        - 无搜索内容时：'暂无活动'
        - 提供更精确的用户反馈
      -->
      <t-empty wx:elif="{{!courseLoading && visibleCourseList.length === 0}}" description="{{searchValue ? '未找到匹配的活动' : '暂无活动'}}" />

      <!--
        活动列表 - 时间轴分页模式

        条件渲染：wx:elif="{{visibleCourseList.length > 0}}"
        只有当有数据时才显示滚动列表

        scroll-view组件：小程序的可滚动视图容器
        功能特点：
        1. 虚拟滚动：只渲染可见区域的内容
        2. 下拉刷新：支持自定义刷新逻辑
        3. 滚动事件：监听滚动到底部等事件
        4. 性能优化：大数据量下的流畅滚动

        与您熟悉的技术对比：
        - Web: overflow-y: scroll + 滚动事件监听
        - Android: RecyclerView + SwipeRefreshLayout
        - iOS: UIScrollView + UIRefreshControl
        - WPF: ScrollViewer + VirtualizingStackPanel
      -->
      <!--
        基础滚动配置

        scroll-y="true": 启用垂直滚动
        - 允许用户上下滚动查看内容
        - 水平滚动保持禁用状态

        style="height: 70vh; min-height: 0;": 高度设置
        - 70vh: 占用视口高度的70%
        - min-height: 0: 允许高度收缩到0
        - 确保滚动区域有固定的可视高度
      -->
            <scroll-view
        wx:elif="{{visibleCourseList.length > 0}}"
        scroll-y="true"
        style="height: {{scrollViewHeight}}px;"
        scroll-into-view="{{scrollIntoView}}"
        bindscrolltolower="onCourseScrollToLower"
        refresher-enabled="{{courseActiveTab !== 'history' && !noMoreHistory}}"
        refresher-triggered="{{isRefresherTriggered}}"
        refresher-threshold="{{45}}"
        refresher-default-style="none"
        bindrefresherrefresh="onRefresherRefresh"
        bindrefresherpulling="onRefresherPulling"
        bindrefresherrestore="onRefresherRestore"
      >
        <!-- 顶部锚点，用于回到今天功能 -->
        <view id="top" style="height: 1px;"></view>

        <!--
          自定义下拉刷新提示

          slot="refresher": 插槽机制
          - 替换scroll-view的默认刷新UI
          - 提供自定义的刷新视觉效果
          - 类似于Vue的slot或React的children

          设计理念：
          根据不同的刷新状态显示不同的提示内容
          提供清晰的操作指引和状态反馈
        -->
        <view slot="refresher" class="custom-refresher">
          <!--
            下拉状态：正在下拉但未达到刷新阈值

            条件：refresherStatus === 'pulling'
            视觉元素：
            - 向下箭头：提示用户继续下拉
            - 文字说明：明确告知操作目的
          -->
          <view wx:if="{{refresherStatus === 'pulling'}}" class="refresher-content">
            <view class="refresher-icon">↓</view>
            <text class="refresher-text">下拉查看历史活动</text>
          </view>

          <!--
            松手状态：已达到刷新阈值，可以松手触发刷新

            条件：refresherStatus === 'loosing'
            视觉元素：
            - 向上箭头：提示用户可以松手
            - 文字变化：从"下拉"变为"松手"

            用户体验：
            明确的状态变化让用户知道何时可以触发刷新
          -->
          <view wx:elif="{{refresherStatus === 'loosing'}}" class="refresher-content">
            <view class="refresher-icon">↑</view>
            <text class="refresher-text">松手加载历史活动</text>
          </view>

          <!--
            加载状态：正在执行刷新操作

            条件：refresherStatus === 'loading'
            视觉元素：
            - 加载动画：三个跳动的圆点
            - 加载文字：告知用户正在处理

            设计一致性：
            与页面其他加载状态使用相同的动画效果
          -->
          <view wx:elif="{{refresherStatus === 'loading'}}" class="refresher-content">
            <view class="loading-dots">
              <view class="loading-dot"></view>
              <view class="loading-dot"></view>
              <view class="loading-dot"></view>
            </view>
            <text class="refresher-text">加载历史活动中...</text>
          </view>
        </view>

        <!--
          设计说明：触顶提示的演进

          旧方案：固定显示触顶提示
          - 问题：占用屏幕空间，用户体验不佳
          - 问题：与下拉刷新功能重复

          新方案：使用refresher机制
          - 优势：只在需要时显示提示
          - 优势：与用户操作紧密结合
          - 优势：更符合现代应用的交互模式

          技术改进：
          从被动提示改为主动交互
          提升了用户的操作体验和界面利用率
        -->

        <!--
          课程列表渲染

          block标签：小程序的逻辑容器
          - 不会在页面中渲染为实际元素
          - 用于包装列表渲染逻辑
          - 类似于React的Fragment或Vue的template

          wx:for="{{visibleCourseList}}": 列表渲染
          - 遍历当前可见的课程列表
          - 支持虚拟滚动，只渲染可见项目

          wx:key="_id": 列表项唯一标识
          - 用于优化列表渲染性能
          - 当数据变化时准确识别变更项目
          - 避免不必要的DOM重建
        -->
        <block wx:for="{{visibleCourseList}}" wx:key="_id">
          <!--
            时间轴分组标题

            功能：为相同日期的课程显示日期分隔线
            类似于微信聊天记录的日期分组

            条件逻辑：
            index === 0: 第一个项目总是显示日期
            visibleCourseList[index-1].timelineDate !== item.timelineDate:
            当前项目的日期与前一个项目不同时显示

            数据字段：
            timelineDate: 格式化的时间轴日期，如"2024年1月15日（周一）"

            用户体验：
            清晰的时间分组帮助用户快速定位特定日期的课程
          -->
          <view wx:if="{{index === 0 || visibleCourseList[index-1].timelineDate !== item.timelineDate}}" class="timeline-date" id="timeline-{{item.encodedTimelineDate}}">{{item.timelineDate}}</view>

          <!--
            课程卡片容器

            动态CSS类名：
            基础类名：course-card
            条件类名：slide-in（当项目在flashIndexes数组中时）

            flashIndexes机制：
            - 新加载的课程会被添加到flashIndexes数组
            - 触发slide-in动画效果
            - 1秒后自动清除，恢复正常状态

            事件绑定：
            bindtap="onCourseCardTap": 点击事件
            data-course="{{item}}": 传递完整的课程对象

            设计考虑：
            传递完整对象而不是ID，减少详情页的数据加载时间
          -->
          <view class="course-card{{flashIndexes.indexOf(index) !== -1 ? ' slide-in' : ''}}{{batchMode ? ' batch-mode' : ''}}" bindtap="{{batchMode ? 'onCourseSelect' : 'onCourseCardTap'}}" data-course="{{item}}" data-course-id="{{item._id}}">
            <view class="course-header">
              <view class="course-title-row">
                <!--
                  批量选择复选框

                  功能：在批量模式下显示复选框
                  位置：活动名称左侧，水平居中对齐
                  样式：正方形复选框
                -->
                <view wx:if="{{batchMode}}" class="course-checkbox" catchtap="onCourseSelect" data-course-id="{{item._id}}">
                  <view class="custom-checkbox {{item.isSelected ? 'checked' : ''}}">
                    <t-icon wx:if="{{item.isSelected}}" name="check" size="12" class="check-icon" />
                  </view>
                </view>
                <view class="course-title">{{item.name}}</view>
              </view>
              <view class="course-status {{item.ended ? 'ended' : (item.status === 'online' ? 'online' : (item.status === 'offline' ? 'offline' : 'no-status'))}}">
                {{item.ended ? '已结束' : (item.status === 'online' ? '已上线' : (item.status === 'offline' ? '未上线' : '无状态'))}}
              </view>
            </view>
            <!--
              课程信息列表

              功能：显示课程的详细信息
              布局：每个信息项包含图标和文字
              设计：统一的信息展示格式，便于快速浏览
            -->
            <view class="course-info-list">
              <!--
                时间信息

                图标：time - 时钟图标，直观表示时间信息
                内容：日期 + 开始时间-结束时间
                格式：1月15日(星期一) 14:30-16:00

                数据来源：
                - formattedDate: 格式化的日期字符串
                - formattedTime: 格式化的开始时间
                - endTimeStr: 格式化的结束时间
              -->
              <view class="info-item">
                <t-icon name="time" size="16" />
                <text>{{item.formattedDate}} {{item.formattedTime}}-{{item.endTimeStr}}</text>
              </view>

              <!--
                教练信息

                条件显示：wx:if="{{item.coachDisplay}}"
                只有当有教练信息时才显示此项
                避免显示空的教练信息

                图标：user - 用户图标，表示人员信息
                内容：教练的显示名称或数量
              -->
              <view class="info-item" wx:if="{{item.coachDisplay}}">
                <t-icon name="user" size="16" />
                <text>{{item.coachDisplay}}</text>
              </view>

              <!--
                场地信息

                图标：location - 位置图标，表示地点信息
                内容：课程举办的场地名称
                重要性：帮助用户了解课程地点
              -->
              <view class="info-item">
                <t-icon name="location" size="16" />
                <text>{{item.venue}}</text>
              </view>

              <!--
                名额信息

                图标：chart - 图表图标，表示数量统计
                内容：剩余名额/总名额
                计算：item.capacity - item.bookedCount

                业务价值：
                - 帮助管理员了解课程预约情况
                - 判断是否需要调整课程容量
                - 评估课程受欢迎程度
              -->
              <view class="info-item">
                <t-icon name="chart" size="16" />
                <text>剩余名额：{{item.capacity - item.bookedCount}}/{{item.capacity}}</text>
              </view>

              <!--
                活动详情

                图标：help-circle - 帮助圆圈图标，表示详细信息
                内容：课程的详细描述
                数据路径：item.activityDetail.description

                注意：
                使用嵌套对象访问，需要确保activityDetail对象存在
                避免因数据结构问题导致显示错误
              -->
              <view class="info-item">
                <t-icon name="help-circle" size="16" />
                <text>活动详情：{{item.activityDetail.description}}</text>
              </view>
            </view>
            <!--
              课程操作区域

              功能：提供课程管理的操作按钮
              布局：水平排列的操作按钮
              设计：不同操作使用不同的颜色主题
              显示条件：只在非批量模式下显示
            -->
            <view wx:if="{{!batchMode}}" class="course-footer">
              <!--
                操作按钮容器

                布局：使用flexbox水平排列按钮
                间距：按钮之间有适当的间距
                响应式：在小屏幕上自动换行
              -->
              <view class="action-buttons">
                <!--
                  编辑按钮

                  功能：跳转到课程编辑页面
                  主题：primary - 蓝色，表示主要操作
                  尺寸：small - 小尺寸，适合卡片内使用

                  事件：catchtap="onEditCourse"
                  - catchtap: 阻止事件冒泡，避免触发卡片点击
                  - 与bindtap的区别：catchtap会阻止事件向上传递

                  数据：data-id="{{item._id}}"
                  - 传递课程ID给事件处理函数
                  - 用于确定要编辑的具体课程
                -->
                <t-button size="small" theme="primary" catchtap="onEditCourse" data-id="{{item._id}}">编辑</t-button>

                <!--
                  状态切换按钮

                  条件显示：wx:if="{{!item.ended && courseActiveTab !== 'history'}}"
                  显示条件：
                  1. !item.ended: 课程未结束
                  2. courseActiveTab !== 'history': 不在历史tab中

                  原因：
                  - 已结束的课程不能改变状态
                  - 历史tab中的课程通常不需要状态操作

                  动态主题：theme="{{item.status === 'online' ? 'warning' : 'success'}}"
                  - online状态：warning主题（橙色），表示"下线"操作
                  - offline状态：success主题（绿色），表示"上线"操作
                  - 颜色与操作意图匹配，提供直观的视觉反馈

                  动态文字：
                  - online → "下线"
                  - offline → "上线"
                  - 其他状态 → "上线"（兜底处理）

                  事件数据：
                  - data-id: 课程ID
                  - data-status: 当前状态，用于判断切换方向
                -->
                <t-button wx:if="{{!item.ended && courseActiveTab !== 'history'}}" size="small" theme="{{item.status === 'online' ? 'warning' : 'success'}}"
                  catchtap="onToggleCourseStatus" data-id="{{item._id}}" data-status="{{item.status}}">
                  {{item.status === 'online' ? '下线' : (item.status === 'offline' ? '上线' : '上线')}}
                </t-button>

                <!--
                  删除按钮

                  功能：删除当前课程
                  主题：danger - 红色，表示危险操作
                  位置：通常放在最后，避免误操作

                  安全考虑：
                  - 点击后会弹出确认对话框
                  - 防止用户误删重要数据
                  - 提供撤销或恢复机制

                  事件：catchtap="onDeleteCourse"
                  数据：data-id="{{item._id}}" - 要删除的课程ID
                -->
                <t-button size="small" theme="danger" catchtap="onDeleteCourse" data-id="{{item._id}}">删除</t-button>
              </view>
            </view>
            <!--
              已预约学员折叠区域

              功能：显示和管理课程的预约学员信息
              交互：点击展开/收起学员列表
              设计：折叠式设计节省空间，按需显示详细信息

              业务价值：
              - 帮助管理员了解预约情况
              - 便于联系和管理学员
              - 支持考勤和课程管理
            -->
            <view class="booked-students-section" catchtap="onStudentSectionTap">
              <!--
                折叠区域头部

                功能：显示学员数量和展开/收起控制
                布局：左侧文字，右侧箭头图标
                交互：点击切换展开/收起状态

                事件：catchtap="toggleCollapse"
                - 阻止事件冒泡，避免触发外层容器的事件
                - 专门处理折叠状态的切换

                数据：data-course-id="{{item._id}}"
                - 传递课程ID，用于更新对应课程的折叠状态
                - 支持多个课程独立的折叠状态管理
              -->
              <view class="collapse-header" catchtap="toggleCollapse" data-course-id="{{item._id}}">
                <!--
                  学员数量显示

                  动态计算：{{item.bookedStudents ? item.bookedStudents.length : 0}}
                  - 如果有学员数据：显示实际数量
                  - 如果没有学员数据：显示0
                  - 防御性编程：避免因数据异常导致显示错误

                  格式：已预约学员（3人）
                  - 清晰表达信息类型和数量
                  - 括号内的数字提供快速的数量概览
                -->
                <text>已预约学员（{{item.bookedStudents ? item.bookedStudents.length : 0}}人）</text>

                <!--
                  展开/收起指示图标

                  动态图标：根据折叠状态显示不同箭头
                  判断逻辑：item.collapseValue && item.collapseValue.length > 0 && item.collapseValue.indexOf('students') !== -1

                  条件分解：
                  1. item.collapseValue: 折叠状态数组存在
                  2. item.collapseValue.length > 0: 数组不为空
                  3. item.collapseValue.indexOf('students') !== -1: 包含'students'标识

                  图标选择：
                  - 展开状态：chevron-up（向上箭头）
                  - 收起状态：chevron-down（向下箭头）

                  用户体验：
                  直观的视觉反馈，用户清楚当前状态和可执行的操作
                -->
                <t-icon name="{{item.collapseValue && item.collapseValue.length > 0 && item.collapseValue.indexOf('students') !== -1 ? 'chevron-up' : 'chevron-down'}}" size="16" />
              </view>

              <!--
                折叠内容区域

                条件显示：只有在展开状态时才显示
                判断条件：与上面的图标判断逻辑相同
                确保UI状态的一致性

                内容：学员列表的详细信息
                布局：垂直排列的学员项目
              -->
              <view class="collapse-content" wx:if="{{item.collapseValue && item.collapseValue.length > 0 && item.collapseValue.indexOf('students') !== -1}}">
                <!--
                  学员列表容器

                  功能：包装学员列表，提供统一的样式和布局
                  支持两种状态：有学员和无学员
                -->
                <view class="student-list">
                  <!--
                    无学员状态

                    条件：wx:if="{{!item.bookedStudents || item.bookedStudents.length === 0}}"
                    触发情况：
                    1. 学员数据不存在：!item.bookedStudents
                    2. 学员数组为空：item.bookedStudents.length === 0

                    显示：友好的空状态提示
                    避免显示空白区域造成用户困惑
                  -->
                  <view wx:if="{{!item.bookedStudents || item.bookedStudents.length === 0}}" class="no-students">
                    暂无预约学员
                  </view>

                  <!--
                    有学员状态

                    条件：wx:else - 与上面的条件相反
                    即：有学员数据且数组不为空

                    内容：学员列表的详细展示
                  -->
                  <view wx:else>
                    <!--
                      学员项目列表

                      列表渲染：wx:for="{{item.bookedStudents}}"
                      - 遍历预约学员数组
                      - 为每个学员创建一个显示项目

                      wx:key="openid": 列表项唯一标识
                      - 使用openid作为key，确保列表渲染的性能
                      - openid是微信用户的唯一标识符

                      wx:for-item="student": 自定义循环变量名
                      - 将默认的item重命名为student
                      - 提高代码可读性，明确表示当前项是学员对象
                    -->
                    <view class="student-item" wx:for="{{item.bookedStudents}}" wx:key="openid" wx:for-item="student">
                      <!--
                        学员图标

                        图标：user - 用户图标，表示学员身份
                        尺寸：14px - 比其他图标稍小，适合列表项使用
                      -->
                      <t-icon name="user" size="14" />

                      <!--
                        学员姓名显示

                        多字段兼容：{{student.nickName || student.name || '未知用户'}}
                        优先级：
                        1. nickName: 微信昵称（优先显示）
                        2. name: 用户设置的姓名
                        3. '未知用户': 兜底显示，避免空白

                        数据兼容性：
                        处理不同版本或来源的用户数据结构差异
                        确保在各种情况下都有合适的显示内容
                      -->
                      <text class="student-name">{{student.nickName || student.name || '未知用户'}}</text>

                      <!--
                        管理员移除学员图标

                        功能：管理员可以为学员取消预约
                        权限：仅管理员可见
                        限制：不受时间限制，即使活动已结束也可以操作

                        图标设计：
                        - 小尺寸图标，不占用过多空间
                        - 删除图标，表明这是移除操作
                        - 靠右对齐，保持视觉整齐
                        - 红色主题，表示危险操作
                      -->
                      <t-icon
                        wx:if="{{isAdmin}}"
                        name="delete"
                        size="16"
                        color="#e34d59"
                        class="remove-student-icon"
                        catchtap="onRemoveStudent"
                        data-course-id="{{item._id}}"
                        data-student-openid="{{student.openid}}"
                        data-student-name="{{student.nickName || student.name || '未知用户'}}"
                        data-booking-id="{{student.bookingId}}"
                      />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </block>
        <view wx:if="{{isLoadingBottom}}" class="loading-indicator">
          <view class="loading-dots">
            <view class="loading-dot"></view>
            <view class="loading-dot"></view>
            <view class="loading-dot"></view>
          </view>
          <text>{{courseActiveTab === 'history' ? '加载更多历史活动中...' : '加载更多活动中...'}}</text>
        </view>
        <view wx:elif="{{courseActiveTab === 'history' ? noMoreHistory : noMoreFuture}}" class="end-indicator">—— 到底啦！ ——</view>
      </scroll-view>
    </view>

    <!--
      回到顶部按钮 - 返回今天

      功能：快速跳转到今天的活动
      组件：使用TDesign的backtop组件
      条件显示：只在课程管理页面显示

      用户体验：
      - 当用户浏览历史活动或未来活动时，可以快速回到今天
      - 只显示图标，简洁设计
      - 灰色半透明设计，不干扰主要内容
      - 明确的日历图标表达功能
    -->
    <t-fab
      icon="backtop"
      draggable="{{true}}"
      bindclick="onBackToToday"
      class="back-to-today-fab"
    />
  </view>



  <!--
    模板维护内容区域 - 可滚动区域

    条件渲染：只有当topActiveTab为'template'时才显示
    这是页面的主要内容区域，包含批量操作栏和模板列表
    布局：在筛选区域下方，可以滚动
  -->
  <view wx:if="{{topActiveTab === 'template'}}" class="template-content">

    <!--
      批量操作栏 - 两行布局

      功能：在批量模式下显示批量操作按钮
      条件显示：只在批量模式且有选中项时显示

      布局结构：
      第一行：显示选择数量信息
      第二行：显示批量操作按钮
    -->
    <view wx:if="{{templateBatchMode && selectedTemplateIds.length > 0}}" class="batch-actions-bar">
      <!--
        单行布局：左侧显示选择信息，右侧显示操作按钮

        功能：在一行内显示批量操作的所有元素
        布局：[已选择 X 个模板] [批量删除]
      -->

      <!-- 左侧：批量信息文本 -->
      <view class="batch-info">
        <text>已选择 {{selectedTemplateIds.length}} 个模板</text>
      </view>

      <!-- 右侧：批量操作按钮组 -->
      <view class="batch-buttons">
        <!--
          批量删除按钮

          功能：删除选中的模板
          主题：danger - 红色，危险操作
          确认：需要5秒确认机制
        -->
        <t-button size="small" theme="danger" bindtap="onBatchDeleteTemplates">
          批量删除
        </t-button>
      </view>
    </view>

    <!--
      模板列表区域

      功能：显示所有可用的课程模板
      布局：卡片式设计，每个模板一张卡片
      交互：支持编辑、应用、删除操作
    -->
    <!--
      模板列表滚动区域

      style="height: 100%;": 高度设置
      - 和上面的活动列表一样，这里的高度也从 "70vh" 修改为 "100%"。
      - 这样可以确保模板列表也能占满所有可用垂直空间，解决底部空白问题。
      - 保持了页面布局逻辑的一致性。
    -->
    <scroll-view
      wx:if="{{visibleTemplateList.length > 0}}"
      scroll-y="true"
      style="height: 100%;"
      scroll-into-view="{{templateScrollIntoView}}"
      bindscrolltolower="onTemplateScrollToLower"
    >
      <!--
        模板列表内容

        条件：有模板数据时显示
        结构：模板卡片的容器
      -->
      <view class="template-list">
        <!--
          模板卡片

          列表渲染：wx:for="{{visibleTemplateList}}"
          - 遍历模板列表数组
          - 为每个模板创建一张卡片

          wx:key="_id": 模板唯一标识
          - 优化列表渲染性能
          - 确保数据变化时的正确更新

          样式：course-card（复用活动卡片样式）
          - 与活动卡片保持一致的设计
          - 支持批量模式的选择状态
          - 照搬活动维护的卡片布局
        -->
        <view
          class="course-card {{templateBatchMode ? 'batch-mode' : ''}}"
          wx:for="{{visibleTemplateList}}"
          wx:key="_id"
          bindtap="{{templateBatchMode ? 'onTemplateSelect' : 'onTemplateCardTap'}}"
          data-template="{{item}}"
          data-template-id="{{item._id}}"
        >
          <!--
            模板卡片头部

            布局：照搬活动卡片头部设计
            内容：批量模式时左侧复选框，模板名称，右侧模板标签
          -->
          <view class="course-header">
            <view class="course-title-row">
              <!--
                批量选择复选框

                功能：在批量模式下显示复选框
                位置：模板名称左侧，水平居中对齐
                样式：复用活动卡片的复选框样式
              -->
              <view wx:if="{{templateBatchMode}}" class="course-checkbox" catchtap="onTemplateSelect" data-template-id="{{item._id}}">
                <view class="custom-checkbox {{item.isSelected ? 'checked' : ''}}">
                  <t-icon wx:if="{{item.isSelected}}" name="check" size="12" class="check-icon" />
                </view>
              </view>
              <view class="course-title">{{item.name}}</view>
            </view>
            <!--
              模板状态标签

              显示：模板标签，区分于活动状态
              样式：使用与活动状态相似的样式，但内容固定为"模板"
            -->
            <view class="course-status template-status">
              模板
            </view>
          </view>

          <!--
            模板信息列表

            功能：显示模板的详细配置信息
            布局：照搬活动卡片的信息列表设计
            样式：复用course-info-list类名，保持一致性
          -->
          <view class="course-info-list">
            <!--
              讲师信息

              图标：user - 用户图标，表示人员信息
              内容：讲师显示信息，与活动卡片格式保持一致
              条件显示：只有当有教练信息时才显示此项
            -->
            <view class="info-item" wx:if="{{item.coachDisplay}}">
              <t-icon name="user" size="16" />
              <text>{{item.coachDisplay}}</text>
            </view>

            <!--
              场地信息

              图标：location - 位置图标
              内容：模板配置的默认场地
              格式：与活动卡片保持一致
            -->
            <view class="info-item">
              <t-icon name="location" size="16" />
              <text>{{item.venue || '未设置场地'}}</text>
            </view>

            <!--
              容量信息

              图标：chart - 图表图标，表示数量配置
              内容：模板设置的人数上限
              格式：与活动卡片的名额信息保持一致
            -->
            <view class="info-item">
              <t-icon name="chart" size="16" />
              <text>人数上限：{{item.capacity || 0}}人</text>
            </view>

            <!--
              活动详情

              图标：help-circle - 帮助图标，表示详细信息
              内容：模板的活动描述
              格式：与活动卡片保持一致
            -->
            <view class="info-item">
              <t-icon name="help-circle" size="16" />
              <text>活动详情：{{item.activityDetail.description || '无描述'}}</text>
            </view>
          </view>

          <!--
            模板操作区域

            功能：提供模板管理的操作按钮
            布局：照搬活动卡片的操作按钮布局
            样式：复用course-footer类名，保持一致性
            条件显示：只在非批量模式下显示
          -->
          <view wx:if="{{!templateBatchMode}}" class="course-footer">
            <!--
              操作按钮容器

              布局：照搬活动卡片的按钮布局
              样式：复用action-buttons类名
            -->
            <view class="action-buttons">
              <!--
                编辑模板按钮

                功能：跳转到模板编辑页面
                主题：primary - 蓝色，主要操作
                事件：catchtap="onEditTemplate" - 阻止事件冒泡
                数据：data-id="{{item._id}}" - 模板ID
              -->
              <t-button size="small" theme="primary" catchtap="onEditTemplate" data-id="{{item._id}}">编辑</t-button>

              <!--
                应用模板按钮

                功能：基于模板创建新课程
                主题：success - 绿色，表示积极操作
                事件：catchtap="onApplyTemplate" - 阻止事件冒泡
                数据：data-id="{{item._id}}" - 模板ID

                业务流程：
                1. 复制模板配置
                2. 跳转到课程编辑页面
                3. 预填充模板数据
                4. 用户补充时间等信息
                5. 保存为新课程
              -->
              <t-button size="small" theme="success" catchtap="onApplyTemplate" data-id="{{item._id}}">应用</t-button>

              <!--
                删除模板按钮

                功能：删除当前模板
                主题：danger - 红色，危险操作
                事件：catchtap="onDeleteTemplate" - 阻止事件冒泡
                数据：data-id="{{item._id}}" - 模板ID

                安全措施：
                - 删除前会弹出确认对话框
                - 检查模板是否被使用
                - 提供撤销或恢复机制
              -->
              <t-button size="small" theme="danger" catchtap="onDeleteTemplate" data-id="{{item._id}}">删除</t-button>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!--
      空状态显示

      条件：visibleTemplateList.length === 0
      组件：t-empty - TDesign的空状态组件
      描述：简洁明了的空状态提示

      用户体验：
      避免显示空白页面，提供明确的状态反馈
    -->
    <t-empty wx:if="{{visibleTemplateList.length === 0}}" description="暂无模板" />
  </view>

  <!--
    全局UI组件区域

    功能：提供页面级的交互组件
    位置：放在页面底部，确保在所有内容之上显示
    组件：对话框、消息提示、Toast等
  -->

  <!--
    通用确认对话框

    组件：t-dialog - TDesign的对话框组件
    ID：confirmDialog - 用于JavaScript中的组件引用

    用途：
    - 删除确认
    - 操作确认
    - 重要提示

    调用方式：
    在JavaScript中通过this.selectComponent('#confirmDialog')获取组件实例
  -->
  <t-dialog id="confirmDialog" />

  <!--
    消息提示组件

    组件：t-message - TDesign的消息组件
    ID：message - 用于JavaScript中的组件引用

    用途：
    - 操作成功提示
    - 错误信息显示
    - 警告信息提示

    特点：
    - 自动消失
    - 不阻塞用户操作
    - 轻量级反馈
  -->
  <t-message id="message" />

  <!--
    移除学员确认对话框

    功能：管理员为学员取消预约的确认对话框
    特点：带有倒计时的确认按钮，防止误操作
    权限：仅管理员可用，不受时间限制

    组件：t-dialog - TDesign的对话框组件
    属性说明：
    - visible: 控制对话框显示/隐藏
    - title: 对话框标题，明确操作类型
    - content: 动态内容，包含学员信息和倒计时
    - confirm-btn: 确认按钮文字，带倒计时
    - cancel-btn: 取消按钮文字

    事件绑定：
    - bind:confirm: 执行移除操作
    - bind:cancel: 取消操作
    - bind:close: 关闭对话框

    业务特点：
    - 不受时间限制：即使活动已结束也可以操作
    - 可选择退还考勤卡次数
    - 5秒倒计时确认机制
  -->
  <t-dialog
    visible="{{removeStudentDialogVisible}}"
    title="移除学员预约"
    content="{{removeStudentDialogContent}}"
    confirm-btn="{{removeStudentDialogConfirmBtn}}"
    cancel-btn="取消"
    bind:confirm="onRemoveStudentConfirm"
    bind:cancel="onRemoveStudentCancel"
    bind:close="onRemoveStudentCancel"
  />

  <!--
    倒计时确认对话框

    功能：用于重要操作的倒计时确认
    场景：课程下线等可能影响用户的操作

    属性配置：
    - visible: 控制对话框显示/隐藏
    - title: 对话框标题，使用警告emoji增强视觉效果
    - content: 动态内容，包含倒计时信息
    - confirm-btn: 确认按钮文字，动态变化
    - cancel-btn: 取消按钮文字，固定为"取消"

    事件绑定：
    - bind:confirm: 确认操作
    - bind:cancel: 取消操作
    - bind:close: 关闭对话框（与取消相同处理）

    设计理念：
    通过倒计时机制强制用户思考操作后果
    减少误操作，提升系统安全性
  -->
  <t-dialog
    visible="{{countdownDialogVisible}}"
    title="⚠️ 重要警告"
    content="{{countdownDialogContent}}"
    confirm-btn="{{countdownDialogConfirmBtn}}"
    cancel-btn="取消"
    bind:confirm="onCountdownConfirm"
    bind:cancel="onCountdownCancel"
    bind:close="onCountdownCancel"
  />

  <!--
    批量操作确认对话框

    功能：批量操作的确认对话框，带5秒倒计时
    特点：防止误操作，确保管理员确认
  -->
  <t-dialog
    visible="{{batchConfirmDialogVisible}}"
    title="⚠️ 批量操作确认"
    content="{{batchConfirmContent}}"
    confirm-btn="{{batchConfirmBtn}}"
    cancel-btn="取消"
    bind:confirm="onBatchConfirm"
    bind:cancel="onBatchCancel"
    bind:close="onBatchCancel"
  />

  <!--
    模板批量操作确认对话框

    功能：模板批量操作的确认对话框，带5秒倒计时
    特点：照搬活动维护的确认机制，防止误操作
  -->
  <t-dialog
    visible="{{templateBatchConfirmDialogVisible}}"
    title="⚠️ 模板批量操作确认"
    content="{{templateBatchConfirmContent}}"
    confirm-btn="{{templateBatchConfirmBtn}}"
    cancel-btn="取消"
    bind:confirm="onTemplateBatchConfirm"
    bind:cancel="onTemplateBatchCancel"
    bind:close="onTemplateBatchCancel"
  />

  <!--
    Toast提示组件

    组件：t-toast - TDesign的轻提示组件
    ID：t-toast - 用于JavaScript中的组件引用

    用途：
    - 快速操作反馈
    - 加载状态提示
    - 简短信息显示

    特点：
    - 短暂显示
    - 不影响用户操作
    - 位置固定（通常在页面中央）
  -->
  <t-toast id="t-toast" />
</view>

<!-- 筛选菜单遮罩层 - 参考album-management的实现方式 -->
<view wx:if="{{filterMenuVisible}}" class="filter-menu-overlay" bindtap="onFilterMenuClose">
  <view class="filter-menu" catchtap="">
    <!--
      筛选选项列表
      遍历courseTabList显示所有可选项
    -->
    <view
      wx:for="{{courseTabList}}"
      wx:key="value"
      class="filter-menu-item {{courseActiveTab === item.value ? 'active' : ''}}"
      bindtap="onFilterMenuItemTap"
      data-value="{{item.value}}"
      data-label="{{item.label}}"
    >
      <!--
        选项文本
        显示筛选项的标签
      -->
      <text class="filter-menu-item-text">{{item.label}}</text>
      <!--
        选中状态图标
        只在当前选中项显示
      -->
      <t-icon
        wx:if="{{courseActiveTab === item.value}}"
        name="check"
        size="16"
        class="filter-menu-item-check"
      />
    </view>
  </view>
</view>